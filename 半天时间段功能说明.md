# 用户数据半天时间段功能说明

## 功能概述

已成功实现用户数据按半天时间段统计的功能，将每天分为两个半天时间段：
- **上午 (AM)**: 00:00 - 11:59
- **下午 (PM)**: 12:00 - 23:59

## 修改内容

### 1. UserDataLineVo 类修改
- 新增 `period` 字段，用于标识时间段（AM/PM）

### 2. SQL 查询逻辑修改
- 生成半天时间段数据点
- 按半天统计新增用户数
- 按半天统计活跃用户数
- 按半天计算累计用户数

### 3. 控制器接口
- 新增 `/board/user/getUserDataLine` 接口
- 支持按日期范围查询半天数据

## API 接口

### 获取用户数据折线图（半天统计）
```
GET /board/user/getUserDataLine
```

**参数：**
- `startDate`: 开始日期，格式：yyyy-MM-dd
- `endDate`: 结束日期，格式：yyyy-MM-dd

**返回数据示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "date": "2025-06-26",
      "period": "AM",
      "todayNewUserCount": 5,
      "todayActiveUserCount": 12,
      "totalUserCount": 1000
    },
    {
      "date": "2025-06-26", 
      "period": "PM",
      "todayNewUserCount": 8,
      "todayActiveUserCount": 25,
      "totalUserCount": 1008
    },
    {
      "date": "2025-06-27",
      "period": "AM", 
      "todayNewUserCount": 3,
      "todayActiveUserCount": 15,
      "totalUserCount": 1011
    }
  ]
}
```

## 数据说明

- **date**: 日期，格式 yyyy-MM-dd
- **period**: 时间段标识
  - `AM`: 上午 (00:00-11:59)
  - `PM`: 下午 (12:00-23:59)
- **todayNewUserCount**: 该半天时间段内的新增用户数
- **todayActiveUserCount**: 该半天时间段内的活跃用户数（基于签到记录）
- **totalUserCount**: 截止到该半天时间段结束时的累计用户总数

## 使用示例

### 查询最近7天的半天数据
```
GET /board/user/getUserDataLine?startDate=2025-06-20&endDate=2025-06-26
```

### 查询单天的半天数据
```
GET /board/user/getUserDataLine?startDate=2025-06-26&endDate=2025-06-26
```

## 注意事项

1. 每天会返回两个数据点（AM 和 PM）
2. 活跃用户基于 `front_sign` 表的签到记录统计
3. 累计用户数会随着时间递增
4. 如果某个半天时间段没有数据，对应的计数字段会返回 0
