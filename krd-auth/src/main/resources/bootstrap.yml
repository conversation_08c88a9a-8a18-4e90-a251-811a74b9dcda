# Tomcat
server:
  port: 9200

# Spring
spring:
  application:
    # 应用名称
    name: krd-auth
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      username: ${crossname}
      password: ${crossword}
      discovery:
        # 服务注册地址
        server-addr: ${server-addr}
        namespace: ${namespace}
      config:
        namespace: ${namespace}
        # 配置中心地址
        server-addr: ${server-addr}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
