# 用户年龄分布功能说明

## 功能概述

已成功实现用户年龄分布统计功能，只基于 `front_user` 表进行统计，支持三种类型的年龄分布分析：
- **新增用户年龄分布** (type=0)：统计今日新增用户的年龄分布
- **活跃用户年龄分布** (type=1)：统计今日活跃用户的年龄分布（基于签到记录）
- **累计用户年龄分布** (type=2)：统计所有用户的年龄分布

## 年龄段划分

系统将用户年龄划分为以下6个年龄段：
1. **20岁以下**
2. **20-29岁**
3. **30-39岁**
4. **40-49岁**
5. **50岁以上**
6. **未知**（年龄为空或0的用户）

## API 接口

### 获取用户年龄分布
```
GET /board/user/getUserAgeDistribution
```

**参数：**
- `type`: 统计类型
  - `0`: 新增用户年龄分布（今日新增）
  - `1`: 活跃用户年龄分布（今日活跃）
  - `2`: 累计用户年龄分布（所有用户）

**返回数据示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "ageRange": "20岁以下",
      "userCount": 25,
      "percentage": "8.33%"
    },
    {
      "ageRange": "20-29岁",
      "userCount": 120,
      "percentage": "40.00%"
    },
    {
      "ageRange": "30-39岁",
      "userCount": 95,
      "percentage": "31.67%"
    },
    {
      "ageRange": "40-49岁",
      "userCount": 45,
      "percentage": "15.00%"
    },
    {
      "ageRange": "50岁以上",
      "userCount": 12,
      "percentage": "4.00%"
    },
    {
      "ageRange": "未知",
      "userCount": 3,
      "percentage": "1.00%"
    }
  ]
}
```

## 数据说明

- **ageRange**: 年龄段名称
- **userCount**: 该年龄段的用户数量
- **percentage**: 该年龄段用户占总数的百分比（保留两位小数）

## 统计逻辑

### 新增用户年龄分布 (type=0)
- 统计今日（当天）注册的用户：`DATE(create_time) = CURDATE()`
- 只查询 `front_user` 表
- 按 `user_age` 字段进行年龄段分组

### 活跃用户年龄分布 (type=1)
- 统计今日有签到记录的用户：`DATE(check_in_date) = CURDATE()`
- 关联 `front_user` 和 `front_sign` 表
- 使用 `DISTINCT` 避免重复计算同一用户

### 累计用户年龄分布 (type=2)
- 统计所有注册用户
- 只查询 `front_user` 表
- 不进行时间过滤

## 使用示例

### 查询新增用户年龄分布
```
GET /board/user/getUserAgeDistribution?type=0
```

### 查询活跃用户年龄分布
```
GET /board/user/getUserAgeDistribution?type=1
```

### 查询累计用户年龄分布
```
GET /board/user/getUserAgeDistribution?type=2
```

## 注意事项

1. 年龄段按照从小到大的顺序排序返回，"未知"排在最后
2. 百分比自动计算并保留两位小数
3. 如果某个年龄段没有用户，该年龄段不会出现在结果中
4. "未知"年龄段包含年龄为 NULL 或 0 的用户
5. 活跃用户基于签到记录判断，需要 `front_sign` 表有数据
6. 所有百分比之和等于 100%
7. 只依赖 `front_user` 表的 `user_age` 字段进行年龄分组
