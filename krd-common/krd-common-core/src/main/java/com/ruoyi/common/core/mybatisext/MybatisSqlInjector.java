package com.ruoyi.common.core.mybatisext;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 自定义sql注入器
 * <AUTHOR>
 * @date 2025/6/23 11:22
 */
@Configuration
public class MybatisSqlInjector extends DefaultSqlInjector {

    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass,tableInfo);
        methodList.add(new BatchMethod(MySqlMethod.INSERT_BATCH, i -> i.getFieldFill() != FieldFill.UPDATE));//批量插入（过滤掉update）
        methodList.add(new BatchMethod(MySqlMethod.REPLACE_BATCH, i -> i.getFieldFill() != FieldFill.UPDATE));//批量插入（过滤掉update）
        return methodList;
    }
}
