package com.ruoyi.common.core.constant;

/**
 * @ClassName WeChatConstants
 * @Description 微信通用常量
 * <AUTHOR>
 * @Date 2025/5/15 下午5:55
 */
public class WeChatConstants {

    /** 微信appid */
    public static final String WECHAT_APPID = "miniProgramAppId";

    /** 微信appsecret */
    public static final String WECHAT_APPSECRET = "miniProgramSecret";

    /** 微信配置key */
    public static final String WECHAT_CONFIG_KEY = "wechat_config";

    /** 小程序登录凭证校验的url */
    public static final String WECHAT_MINI_SNS_AUTH_CODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session?appid={}&secret={}&js_code={}&grant_type=authorization_code";

    /** 开放平台获取手机号 */
    public static final String WECHAT_GET_USER_PHONE_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={}";

    //用户登token redis存储前缀
    public static final String USER_TOKEN_REDIS_KEY_PREFIX = "TOKEN_USER:";

    /** 小程序accessToken redis key */
    public static final String REDIS_WECAHT_MINI_ACCESS_TOKEN_KEY = "wechat_mini_accessToken";

    /** 获取accessToken的url */
    public static final String WECHAT_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={}&secret={}";

    /*头部 token令牌key*/
    public static final String HEADER_AUTHORIZATION_KEY = "Authori-zation";

}
