package com.ruoyi.common.core.utils.erp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.constant.JiCeConstants;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @ClassName ApiExample
 * @Description 万里牛erp
 * <AUTHOR>
 * @Date 2025/5/21 下午3:23
 */
public class ApiExample {

    /**
     * 推送示例代码
     * @param data 参数
     * @param path 请求路径
     * @param apiUrl 接口地址
     */
    public static void erpPost(Map<String, Object> data,String path,String apiUrl) {
        OkHttpClient client = new OkHttpClient();

        try {
//            String endPoint = apiUrl;

            // 创建FormData
            FormBody.Builder formBuilder = new FormBody.Builder();
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                formBuilder.add(entry.getKey(), String.valueOf(entry.getValue()));
            }
            RequestBody formBody = formBuilder.build();

            // 3. 构建请求
            Request request = new Request.Builder()
                    .url(apiUrl + path)
                    .post(formBody)
                    .addHeader("Content-Type", "application/x-www-form-urlencoded") // 必须设置
                    .build();

            // 4. 发送请求
            Response response = client.newCall(request).execute();
            System.out.println("---------------"+response.body().string());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 组装请求参数 —— 推送参数
     * @param list
     * @param name 业务参数 如items
     *             该方法只用于推送 不可用于查询
     * @return
     * @throws Exception
     */
    public static Map<String, Object> bodyPush(List<Map<String, Object>> list,String name,String appKey,String secret)
            throws Exception {

        Map<String, Object> body = new LinkedHashMap<>();

        body.put(name, list);

        Map<String, Object> signedParams = signParameters(
                body,
                appKey,
                secret,
                name
        );

        // 合并系统参数
        body.putAll(signedParams);
        return body;
    }

    /**
     * 组装请求参数 —— 查询参数
     * @param map
     * @return
     * @throws Exception
     */
    public static Map<String, Object> bodyQuery(Map<String, Object> map,String appKey,String secret)
            throws Exception {

//        Map<String, Object> body = new LinkedHashMap<>();

        Map<String, Object> signedParams = signParameters(
                map,
                appKey,
                secret,
                null
        );

        // 合并系统参数
        map.putAll(signedParams);
        return map;
    }

    /**
     * 签名参数
     * @param parameters
     * @param appKey
     * @param secret
     * @param name
     * @return
     * @throws Exception
     */
    public static Map<String, Object> signParameters(
            Map<String, Object> parameters,
            String appKey,
            String secret,
            String name
    ) throws Exception {

        // 关键修正3：使用新TreeMap保持排序
        Map<String, Object> tree = new TreeMap<>();
        tree.put("_app", appKey);
        tree.put("_t", String.valueOf(System.currentTimeMillis()));
        tree.put("_s", "");

        // 关键修正4：处理嵌套参数
        if (name != null){
            String s = new ObjectMapper().writeValueAsString(parameters.get(name));
            tree.put(name, s); // 以JSON字符串参与签名
        }

        if (name == null){
            tree.putAll(parameters);
        }

        // 构建签名字符串
        StringBuilder content = new StringBuilder();
        for (Map.Entry<String, Object> entry : tree.entrySet()) {
            if (content.length() > 0) content.append("&");
            content.append(entry.getKey())
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue().toString(), "UTF-8"));
        }

        // 生成签名
        String sign = md5(secret + content + secret);
        tree.put("_sign", sign);

        return tree;
    }

    /**
     * 加密签名
     * @param input
     * @return
     * @throws NoSuchAlgorithmException
     */
    private static String md5(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] array = md.digest(input.getBytes());
        StringBuilder sb = new StringBuilder();
        for (byte b : array) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        List<Map<String, Object>> list1 = new ArrayList<>();
        List<Map<String, Object>> list2 = new ArrayList<>();
        Map<String, Object> category = new LinkedHashMap<>();
        category.put("category_id", "类目测试3");
        category.put("name", "汽车产品");
        category.put("parent_id", "0");
        category.put("shop_nick", JiCeConstants.ERP_SHOP_NICK);
        category.put("sort_order", 0);
        category.put("status", 1);
        list1.add(category);

        // 格式化时间为 yyyy-MM-dd HH:mm:ss
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = dateFormat.format(date);

        Map<String, Object> shop = new LinkedHashMap<>();
        shop.put("create_time", formattedDate);
        shop.put("item_id", "ceshi111");
        shop.put("modify_time", formattedDate);
        shop.put("category_id", "12");
        shop.put("shop_nick", JiCeConstants.ERP_SHOP_NICK);
        shop.put("status", 1);
        shop.put("title", "测试商品");
        shop.put("price", 1000);
        shop.put("quantity", 100);
        list2.add(shop);

        Map<String, Object> map = new LinkedHashMap<>();
        map.put("page", "1");
        map.put("limit", "10");
        map.put("start", formattedDate);
        map.put("end", formattedDate);
        map.put("storage_code", "");

        try {
//            Map<String, Object> requestBody = bodyPush(list1,"categories");
//            Map<String, Object> requestBody2 = bodyPush(list2,"items");
//            Map<String, Object> bodyQuery = bodyQuery(map);
//            System.out.println("类目结构" + requestBody);
//            System.out.println("商品结构" + requestBody2);
//            System.out.println("获取" + bodyQuery);
//            erpPost(requestBody, JiCeConstants.ERP_CATEGORY_PUSH);
//            erpPost(requestBody2, JiCeConstants.ERP_SHOP_PUSH);
//            erpPost(bodyQuery, JiCeConstants.ERP_STOCK_BATCH_GET);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}