package com.ruoyi.common.core.constant;

/**
 * @ClassName JiCeConstants
 * @Description 项目通用常量
 * <AUTHOR>
 * @Date 2025/5/21 下午2:48
 */
public class JiCeConstants {

    /**
     * 万里牛 系统配置
     */
    public static final String WLN_ERP_CONFIG = "wln_erp_config";

    /**
     * 万里牛 erp appkey
     */
    public static final String ERP_APP_KEY = "appKey";

    /**
     * 万里牛 erp secret
     */
    public static final String ERP_SECRET = "appSecret";

    /**
     * 万里牛 erp 接口url
     */
    public static final String ERP_URL = "apiUrl";

    /**
     * 万里牛 erp 类目推送
     */
    public static final String ERP_CATEGORY_PUSH = "/erp/b2c/categories/open";

    /**
     * 万里牛 erp 商品推送
     */
    public static final String ERP_SHOP_PUSH = "/erp/b2c/items/open";

    /**
     * 万里牛 erp 订单推送
     */
    public static final String ERP_ORDER_PUSH = "/erp/b2c/trades/open";

    /**
     * 万里牛 erp 批量获取erp 库存
     */
    public static final String ERP_STOCK_BATCH_GET = "/erp/b2c/inventories/erp";

    /**
     * 万里牛 erp 查询erp单笔库存
     */
    public static final String ERP_STOCK_SINGLE_GET = "/erp/b2c/inventories/erp/single";

    /**
     * 万里牛 erp 订单发货状态查询
     */
    public static final String ERP_ORDER_DELIVERY_STATUS = "/erp/b2c/trades/erp/status";

    /**
     * 万里牛 erp 订单发货通知 回调
     */
    public static final String ERP_ORDER_DELIVERY_NOTICE = "/erp/callback/tradeSendMsg";

    /**
     * 万里牛 erp 库存变更通知 回调
     */
    public static final String ERP_STOCK_CHANGE_NOTICE = "/erp/callback/itemQuantityUploadMsg";

    /**
     * 万里牛 售后订单推送
     */
    public static final String ERP_AFTER_SALE_ORDER_PUSH = "/erp/b2c/refunds/open";

    /**
     * 万里牛 erp 店铺名称
     */
    public static final String ERP_SHOP_NICK = "shopId";

    /**
     * oss 配置 key
     */
    public static final String OSS_KEY = "oss_config";

    /**
     * oss Endpoint
     */
    public static final String OSS_ENDPOINT = "endpoint";

    /**
     * oss Bucket名称
     */
    public static final String OSS_BUCKET_NAME = "bucketName";

    /**
     * oss AccessKeyId
     */
    public static final String OSS_ACCESS_KEY_ID = "accessKeyId";

    /**
     * oss AccessKeySecret
     */
    public static final String OSS_ACCESS_KEY_SECRET = "accessKeySecret";

    /**
     * oss region
     */
    public static final String OSS_REGION = "region";

    /**
     * oss maxFileSize
     */
    public static final String OSS_MAX_FILE_SIZE = "maxFileSize";

}
