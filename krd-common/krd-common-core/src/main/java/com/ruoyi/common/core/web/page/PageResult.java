package com.ruoyi.common.core.web.page;

/**
 * @Author: suhai
 * @Date: 2025/6/3
 * @Description:
 */
import java.util.List;

public class PageResult<T> {
    private List<T> list;
    private int pages;
    private long total;

    public PageResult(List<T> list, int pages,long total) {
        this.list = list;
        this.pages = pages;
        this.total = total;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }
}
