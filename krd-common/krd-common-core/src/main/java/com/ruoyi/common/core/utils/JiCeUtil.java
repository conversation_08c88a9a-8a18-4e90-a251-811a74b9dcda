package com.ruoyi.common.core.utils;

/**
 * @ClassName JiCeUtil
 * @Description 项目相关工具类
 * <AUTHOR>
 * @Date 2025/5/20 上午10:36
 */
public class JiCeUtil {

    /**
     * 根据长度生成随机数字
     * @param start 起始数字
     * @param end 结束数字
     * @return 生成的随机码
     */
    public static Integer randomCount(Integer start, Integer end){
        return (int)(Math.random()*(end - start +1) + start);
    }

    /**
     * 编号号生成
     * @param type String 类型
     * @return 生成的随机码
     */
    public static String getNumber(String type){
        return type + randomCount(11111, 99999) + System.currentTimeMillis() + randomCount(11111, 99999);
    }
}
