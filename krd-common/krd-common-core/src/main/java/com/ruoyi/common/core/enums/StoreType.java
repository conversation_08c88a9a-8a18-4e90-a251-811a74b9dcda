package com.ruoyi.common.core.enums;

/**
 * @ClassName StoreType
 * @Description 商城相关类型枚举
 * <AUTHOR>
 * @Date 2025/5/20 上午10:34
 */
public enum StoreType {
    Gift("gift", "礼品卡"), STORE("store", "商品");

    private final String type;
    private final String info;

    StoreType(String type, String info)
    {
        this.type = type;
        this.info = info;
    }

    public String getType()
    {
        return type;
    }

    public String getInfo()
    {
        return info;
    }
}
