package com.ruoyi.common.core.mybatisext;

/**
 * 参考：com.baomidou.mybatisplus.core.enums.SqlMethod自带枚举
 * <AUTHOR>
 * @date 2025/6/23 11:22
 */
public enum MySqlMethod {
    INSERT_BATCH("insertBatch", "正真批量插入数据（选择字段插入）", "<script>\nINSERT INTO %s %s VALUES %s\n</script>"),
    REPLACE_BATCH("replaceBatch", "正真批量插入数据（选择字段插入）", "<script>\nREPLACE INTO %s %s VALUES %s\n</script>"),
    ;

    private final String method;
    private final String desc;
    private final String sql;

    MySqlMethod(String method, String desc, String sql) {
        this.method = method;
        this.desc = desc;
        this.sql = sql;
    }

    public String getMethod() {
        return this.method;
    }

    public String getDesc() {
        return this.desc;
    }

    public String getSql() {
        return this.sql;
    }
}
