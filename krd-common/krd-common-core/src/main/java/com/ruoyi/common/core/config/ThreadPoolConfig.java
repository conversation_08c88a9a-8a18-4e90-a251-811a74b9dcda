package com.ruoyi.common.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/5/26 10:05
 */
@Configuration
public class ThreadPoolConfig {

    private static final int CORE_POOL_SIZE = 5;
    private static final int MAXIMUM_POOL_SIZE_SIZE = 10;

    //  线程池维护线程所允许的空闲时间(分钟)
    private static final int KEEP_ALIVE_TIME = 10;
    private static final int QUEUE_CAPACITY = 100;

    /**
     * 创建一个线程池，用于处理编码打印任务
     * @return 线程池
     */
    @Bean("ruoyi-thread-pool-number-create")
    public ExecutorService getAsyncServiceCreate() {
        return new ThreadPoolExecutor(CORE_POOL_SIZE,
                MAXIMUM_POOL_SIZE_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(QUEUE_CAPACITY),
                //线程池不可用调用者自己执行
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean("ruoyi-thread-pool-number-save")
    public ExecutorService getAsyncServiceSave() {
        return new ThreadPoolExecutor(CORE_POOL_SIZE,
                MAXIMUM_POOL_SIZE_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(QUEUE_CAPACITY),
                //线程池不可用调用者自己执行
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 用于查询uni用户信息
     * @return
     */
    @Bean("ruoyi-thread-pool-uniuser-query")
    public ExecutorService getUniuserAsyncService() {
        return new ThreadPoolExecutor(20,
                60,
                KEEP_ALIVE_TIME,
                TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(100),
                //线程池不可用抛出 RejectedExecutionException异常
                new ThreadPoolExecutor.AbortPolicy());
    }
}
