package com.ruoyi.common.core.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * @ClassName PinyinUtils
 * @Description 获取汉字首拼
 * <AUTHOR>
 * @Date 2025/5/23 下午3:15
 */
public class PinyinUtils {

    public static String getFirstLetters(String chinese) {
        if (chinese == null || chinese.isEmpty()) return "";

        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        StringBuilder result = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            try {
                String[] pinyins = PinyinHelper.toHanyuPinyinStringArray(c, format);
                if (pinyins != null && pinyins.length > 0) {
                    result.append(pinyins[0].charAt(0));
                } else if (Character.isLetter(c)) {
                    result.append(Character.toUpperCase(c));
                }
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                e.printStackTrace();
            }
        }
        return result.toString();
    }

    public static void main(String[] args) {
        System.out.println(getFirstLetters("测试"));
        System.out.println(getFirstLetters("Hello 世界!"));
    }
}
