package com.ruoyi.common.core.web.page;

import java.io.Serializable;
import java.util.List;

/**
 * 表格分页数据对象
 *
 * <AUTHOR>
 */
public class CblTableDataInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 总记录数 */
    private long total;

    /** 列表数据（已弃用，请使用 data.list） */
    private List<?> rows;

    /** 分页数据对象（新结构） */
    private PageResult<?> data;

    /** 消息状态码 */
    private int code;

    /** 消息内容 */
    private String msg;

    /**
     * 构造函数（默认）
     */
    public CblTableDataInfo() {
    }

    /**
     * 分页构造器
     *
     * @param list  列表数据
     * @param total 总记录数
     * @param pages 总页数
     */
    public CblTableDataInfo(List<?> list, long total, int pages) {
        this.total = total;
        this.data = new PageResult<>(list, pages,total);
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public List<?> getRows() {
        return rows;
    }

    public void setRows(List<?> rows) {
        this.rows = rows;
        if (this.data == null && rows != null) {
            // 可选逻辑：同步到 data.list
            this.data = new PageResult<>(rows, 0,  0); // pages 可稍后设置
        }
    }

    public PageResult<?> getData() {
        return data;
    }

    public void setData(PageResult<?> data) {
        this.data = data;
        if (this.rows == null && data != null) {
            this.rows = data.getList();
        }
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
