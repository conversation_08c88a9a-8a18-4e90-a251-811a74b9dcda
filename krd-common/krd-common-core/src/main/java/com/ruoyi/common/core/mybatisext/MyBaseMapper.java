package com.ruoyi.common.core.mybatisext;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * 自定义扩展mapper
 * <AUTHOR>
 * @date 2025/6/23 11:22
 */
public interface MyBaseMapper<T> extends BaseMapper<T> {

    /**
     * 真正的批量插入方法
     * @param entityList    插入集合
     * @return
     */
    int insertBatch(@Param("list") Collection<T> entityList);
    /**
     * 真正的批量插入方法
     * @param entityList    插入集合
     * @return
     */
    int replaceBatch(@Param("list") Collection<T> entityList);
}
