<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.krd</groupId>
        <artifactId>krd</artifactId>
        <version>0.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>krd-common-log</module>
        <module>krd-common-core</module>
        <module>krd-common-redis</module>
        <module>krd-common-seata</module>
        <module>krd-common-swagger</module>
        <module>krd-common-security</module>
        <module>krd-common-sensitive</module>
        <module>krd-common-datascope</module>
        <module>krd-common-datasource</module>
    </modules>

    <artifactId>krd-common</artifactId>
    <packaging>pom</packaging>

    <description>
        ruoyi-common通用模块
    </description>

</project>
