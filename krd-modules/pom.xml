<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.krd</groupId>
        <artifactId>krd</artifactId>
        <version>0.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>krd-system</module>
        <module>krd-gen</module>
        <module>krd-job</module>
        <module>krd-file</module>
        <module>krd-uni</module>
    </modules>

    <artifactId>krd-modules</artifactId>
    <packaging>pom</packaging>

    <description>
        ruoyi-modules业务模块
    </description>
    <dependencies>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>4.5.7</version>
        </dependency>



        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.18</version>
        </dependency>

        <!--京东物流基础sdk-->
        <dependency>
            <groupId>com.jdl.open</groupId>
            <artifactId>base-sdk</artifactId>
            <version>1.0.28-SNAPSHOT</version>
            <exclusions>
                <!-- 排除 log4j-slf4j-impl -->
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--京东物流业务sdk-->
        <dependency>
            <groupId>com.jdl.open</groupId>
            <artifactId>service-sdk</artifactId>
            <version>6.8_20250513152845</version>
        </dependency>
    </dependencies>
</project>
