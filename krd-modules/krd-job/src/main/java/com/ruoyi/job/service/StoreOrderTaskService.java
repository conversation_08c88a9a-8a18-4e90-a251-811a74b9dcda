package com.ruoyi.job.service;

import com.ruoyi.system.api.domain.CancelOrder;
import com.ruoyi.system.api.domain.FrontOrders;

/**
 * @Author: suhai
 * @Date: 2025/6/19
 * @Description: 订单任务服务
 */

public interface StoreOrderTaskService {


    /**
     * 订单自动取消处理
     * @param cancelOrder 订单
     */
    Boolean autoCancel(CancelOrder cancelOrder);


    /**
     * 支付成功处理
     * @param frontOrders 订单
     */
    Boolean paySuccess(FrontOrders frontOrders);

    /**
     * 处理退款申请
     * @param frontOrders 订单
     */
    Boolean processRefund(FrontOrders frontOrders);
}

