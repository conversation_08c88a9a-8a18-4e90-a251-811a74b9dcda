package com.ruoyi.job.strategy.impl;

import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.user.FrontSource;
import com.ruoyi.system.api.mapper.FrontSourceMapper;
import com.ruoyi.job.strategy.RefundStrategy;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 积分支付退款策略实现
 */
@Service
public class PointsRefundStrategy implements RefundStrategy {

    private static final Logger log = LoggerFactory.getLogger(PointsRefundStrategy.class);

    // 积分兑换比例：1元 = ? 积分
    private static final int POINTS_RATE = 100;

    @Autowired
    private FrontUserMapper userMapper;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private FrontSourceMapper frontSourceMapper;

    @Override
    public boolean refund(FrontOrders order) {
        BigDecimal amount = order.getUsePoint();
        // 检验是否有使用积分
        if (order.getUsePoint() == null || order.getUsePoint().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("订单未使用积分支付，无需退款，订单号：{}", order.getOrderNumber());
            return false;
        }

        // 计算需要退还的积分
        BigDecimal requiredPoints = BigDecimal.valueOf(amount.multiply(new BigDecimal(POINTS_RATE)).intValue());

        // 如果退款积分大于原使用积分，则按原使用积分进行退款
        if (requiredPoints.compareTo(order.getUsePoint()) > 0) {
            requiredPoints = order.getUsePoint();
            log.info("退款积分大于原使用积分，按原使用积分退款，退款积分调整为：{}", requiredPoints);
        }

        // 创建一个final变量来在Lambda中使用
        final BigDecimal finalRequiredPoints = requiredPoints;

        // 获取用户信息
        Long userId = order.getUserId();
        FrontUser user = userMapper.selectFrontUserById(userId);
        if (user == null) {
            log.error("用户不存在，userId：{}", userId);
            return false;
        }

        // 执行退款事务
        Boolean execute = transactionTemplate.execute(status -> {
            try {
                // 更新用户积分
                user.setIntegral(user.getIntegral().add(finalRequiredPoints));
                userMapper.updateFrontUser(user);

                // 记录积分变动明细
                FrontSource source = new FrontSource();
                source.setUserId(userId);
                source.setSource(7); // 7:退款返还
                source.setType(0); // 0:增加
                source.setPoint(finalRequiredPoints.intValue());
                source.setBalance(user.getIntegral());
                source.setOrderNumber(order.getOrderNumber());
                source.setCreateTime(LocalDateTime.now());
                frontSourceMapper.insert(source);

                log.info("积分退款成功，订单号：{}，退款积分：{}", order.getOrderNumber(), finalRequiredPoints);
                return true;
            } catch (Exception e) {
                log.error("积分退款失败，订单号：{}，错误信息：{}", order.getOrderNumber(), e.getMessage(), e);
                status.setRollbackOnly();
                return false;
            }
        });

        return Boolean.TRUE.equals(execute);
    }

    @Override
    public String getPayType() {
        return PayConstants.PAY_TYPE_YUE;
    }
}
