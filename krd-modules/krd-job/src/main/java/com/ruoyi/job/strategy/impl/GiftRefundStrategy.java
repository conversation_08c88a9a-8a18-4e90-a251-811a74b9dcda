package com.ruoyi.job.strategy.impl;

import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.system.api.domain.FrontGiftInfo;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.vo.GiftDeduVo;
import com.ruoyi.system.api.mapper.FrontGiftInfoMapper;
import com.ruoyi.job.strategy.RefundStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.List;

/**
 * 礼品卡支付退款策略实现
 */
@Service
public class GiftRefundStrategy implements RefundStrategy {

    private static final Logger log = LoggerFactory.getLogger(GiftRefundStrategy.class);

    @Autowired
    private FrontGiftInfoMapper frontGiftInfoMapper;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    public boolean refund(FrontOrders order) {

        // 检验是否有使用礼品卡
        if (order.getGiftDeduction() == null || order.getGiftDeduction().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("订单未使用礼品卡支付，无需退款，订单号：{}", order.getOrderNumber());
            return false;
        }

        // 处理退款金额，如果退款金额大于礼品卡抵扣总额，则按礼品卡抵扣总额进行退款
        BigDecimal initialRefundAmount = order.getGiftDeduction();
        if (initialRefundAmount.compareTo(order.getGiftDeduction()) > 0) {
            log.info("退款金额大于礼品卡抵扣总额，按礼品卡抵扣总额退款，退款金额调整为：{}", initialRefundAmount);
        }

        // 获取订单使用的礼品卡列表
        List<GiftDeduVo> giftList = order.getGiftList();
        if (giftList == null || giftList.isEmpty()) {
            log.error("订单礼品卡信息为空，无法退款，订单号：{}", order.getOrderNumber());
            return false;
        }

        // 创建final变量用于Lambda表达式
        final BigDecimal refundAmount = initialRefundAmount;

        // 执行退款事务
        Boolean execute = transactionTemplate.execute(status -> {
            try {
                BigDecimal remainRefundAmount = refundAmount;

                // 按照使用顺序逐个退还礼品卡金额
                for (GiftDeduVo gift : giftList) {
                    if (remainRefundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        break; // 退款金额已用完
                    }

                    FrontGiftInfo giftInfo = frontGiftInfoMapper.selectById(gift.getId());
                    if (giftInfo == null) {
                        log.warn("礼品卡不存在，ID：{}", gift.getId());
                        continue;
                    }

                    // 计算此礼品卡需要退还的金额
                    BigDecimal giftRefundAmount = gift.getAmount().min(remainRefundAmount);
                    remainRefundAmount = remainRefundAmount.subtract(giftRefundAmount);

                    // 更新礼品卡余额
                    BigDecimal newBalance = giftInfo.getBalance().add(giftRefundAmount);
                    giftInfo.setBalance(newBalance);

                    // 如果退款后余额大于0且状态为已使用(1)，则更新为未使用(0)
                    if (newBalance.compareTo(BigDecimal.ZERO) > 0 && "1".equals(giftInfo.getStatus())) {
                        giftInfo.setStatus("0");
                    }

                    // 更新礼品卡信息
                    frontGiftInfoMapper.updateById(giftInfo);

                    log.info("礼品卡退款成功，卡ID：{}，退款金额：{}，当前余额：{}",
                            giftInfo.getId(), giftRefundAmount, newBalance);
                }

                log.info("礼品卡退款处理完成，订单号：{}", order.getOrderNumber());
                return true;
            } catch (Exception e) {
                log.error("礼品卡退款失败，订单号：{}，错误信息：{}", order.getOrderNumber(), e.getMessage(), e);
                status.setRollbackOnly();
                return false;
            }
        });

        return Boolean.TRUE.equals(execute);
    }

    @Override
    public String getPayType() {
        return PayConstants.PAY_TYPE_GIFT;
    }
}
