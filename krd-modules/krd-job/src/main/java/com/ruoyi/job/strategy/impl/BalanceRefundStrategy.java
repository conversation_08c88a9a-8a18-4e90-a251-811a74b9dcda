package com.ruoyi.job.strategy.impl;

import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.system.api.domain.FrontBalanceInfo;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.mapper.FrontBalanceInfoMapper;
import com.ruoyi.job.strategy.RefundStrategy;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 余额退款策略实现
 */
@Service
public class BalanceRefundStrategy implements RefundStrategy {

    private static final Logger log = LoggerFactory.getLogger(BalanceRefundStrategy.class);

    @Autowired
    private FrontUserMapper userMapper;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private FrontBalanceInfoMapper frontBalanceInfoMapper;

    @Override
    public boolean refund(FrontOrders order) {
        BigDecimal amount = order.getUseBalance();
        // 检验退款金额是否大于原始使用余额
        if (order.getUseBalance() == null || order.getUseBalance().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("订单未使用余额支付，无需退款，订单号：{}", order.getOrderNumber());
            return false;
        }

        // 如果退款金额大于原使用余额，则按原使用余额进行退款
        BigDecimal refundAmount = BigDecimal.ZERO;
        if (refundAmount.compareTo(order.getUseBalance()) > 0) {
            refundAmount = order.getUseBalance();
            log.info("退款金额大于原使用余额，按原使用余额退款，退款金额调整为：{}", refundAmount);
        } else {
            refundAmount = amount;
        }

        // 获取用户信息
        Long userId = order.getUserId();
        FrontUser user = userMapper.selectFrontUserById(userId);
        if (user == null) {
            log.error("用户不存在，userId：{}", userId);
            return false;
        }

        // 执行退款事务
        BigDecimal finalRefundAmount = refundAmount;
        Boolean execute = transactionTemplate.execute(status -> {
            try {
                // 更新用户余额
                user.setNowMoney(user.getNowMoney().add(finalRefundAmount));
                userMapper.updateFrontUser(user);

                // 记录余额变动明细
                FrontBalanceInfo frontBalanceInfo = new FrontBalanceInfo();
                frontBalanceInfo.setUserId(userId);
                frontBalanceInfo.setType(1L); // 1:增加
                frontBalanceInfo.setMoveaccount(String.valueOf(finalRefundAmount));
                frontBalanceInfo.setBalance(user.getNowMoney());
                frontBalanceInfo.setOrderNumber(order.getOrderNumber());
                frontBalanceInfo.setTitle("订单退款");
                frontBalanceInfo.setCreateTime(LocalDateTime.now());
                frontBalanceInfoMapper.insert(frontBalanceInfo);

                log.info("余额退款成功，订单号：{}，退款金额：{}", order.getOrderNumber(), finalRefundAmount);
                return true;
            } catch (Exception e) {
                log.error("余额退款失败，订单号：{}，错误信息：{}", order.getOrderNumber(), e.getMessage(), e);
                status.setRollbackOnly();
                return false;
            }
        });

        return Boolean.TRUE.equals(execute);
    }

    @Override
    public String getPayType() {
        return PayConstants.PAY_TYPE_POINTS;
    }
}
