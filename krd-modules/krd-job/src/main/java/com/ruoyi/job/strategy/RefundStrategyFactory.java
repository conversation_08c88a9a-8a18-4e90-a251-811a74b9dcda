package com.ruoyi.job.strategy;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 退款策略工厂类
 */
@Service
@AllArgsConstructor
public class RefundStrategyFactory {

    private final Map<String, RefundStrategy> strategyMap = new HashMap<>();

    private final List<RefundStrategy> strategies;

    @PostConstruct
    public void init() {
        for (RefundStrategy strategy : strategies) {
            strategyMap.put(strategy.getPayType(), strategy);
        }
    }

    /**
     * 根据支付类型获取对应的退款策略
     * @param payType 支付类型
     * @return 退款策略实现
     */
    public RefundStrategy getStrategy(String payType) {
        return strategyMap.get(payType);
    }
}
