package com.ruoyi.uni.check.mapper;

import com.ruoyi.common.core.mybatisext.MyBaseMapper;
import com.ruoyi.uni.check.domain.dto.PactarResultDTO;
import com.ruoyi.uni.check.domain.entity.FrontTmpPactarEntity;
import com.ruoyi.uni.check.domain.vo.ReportPactarVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报告模版-套餐-指标解释
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 18:23:57
 */
@Mapper
public interface FrontTmpPactarMapper extends MyBaseMapper<FrontTmpPactarEntity> {

    List<ReportPactarVO> selectPactarList(@Param("itemCode") String itemCode, @Param("tmpId") String tmpId);

    List<PactarResultDTO> selectTmpModel(@Param("tmpId") String tmpId);
}
