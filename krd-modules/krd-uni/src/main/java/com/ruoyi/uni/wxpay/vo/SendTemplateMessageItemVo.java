package com.ruoyi.uni.wxpay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description: 微信模板发送数据类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SendTemplateMessageItemVo {
    public SendTemplateMessageItemVo() {}
    public SendTemplateMessageItemVo(String value) {
        this.value = value;
    }

    @Schema(name = "显示的文字内容", required = true)
    private String value;

    @Schema(name = "颜色")
    private String color = "#173177";
}

