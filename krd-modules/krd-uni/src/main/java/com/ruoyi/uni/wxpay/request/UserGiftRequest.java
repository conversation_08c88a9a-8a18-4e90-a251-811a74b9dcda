package com.ruoyi.uni.wxpay.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName UserGiftRequest
 * @Description 用户购买礼品卡参数
 * <AUTHOR>
 * @Date 2025/6/23 下午2:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UserGiftRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(name = "礼品卡id")
    private Long id;

    @Schema(name = "支付方式| weixin = 微信")
    private String payType = "weixin";

    @Schema(name = "来源 |  routine = 小程序")
    @JsonProperty(value = "from")
    private String fromType;

    @Schema(name = "客户端ip")
    @JsonIgnore
    private String clientIp;

    @Schema(name = "用户id")
    @JsonIgnore
    private Integer userId;
}
