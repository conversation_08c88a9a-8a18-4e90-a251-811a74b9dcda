package com.ruoyi.uni.wxpay.strategy;

import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;

/**
 * @Author: suhai
 * @Date: 2025/6/19
 * @Description:支付策略接口
 */
public interface PaymentStrategy {
    /**
     * 执行支付
     * @param order 订单
     * @param ip 客户端IP
     * @return 支付结果
     */
    OrderPayResultResponse pay(FrontOrders order, String ip);

    /**
     * 获取支付类型标识
     * @return 支付类型
     */
    String getPayType();
}
