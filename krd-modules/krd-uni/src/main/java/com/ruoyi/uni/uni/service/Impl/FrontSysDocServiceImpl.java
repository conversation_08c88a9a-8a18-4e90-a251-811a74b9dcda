package com.ruoyi.uni.uni.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.FrontSysDoc;
import com.ruoyi.system.api.mapper.FrontSysDocMapper;
import com.ruoyi.uni.uni.service.IFrontSysDocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 文档管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class FrontSysDocServiceImpl implements IFrontSysDocService
{
    @Autowired
    private FrontSysDocMapper frontSysDocMapper;

    /**
     * 查询文档管理
     *
     * @param id 文档管理主键
     * @return 文档管理
     */
    @Override
    public FrontSysDoc selectFrontSysDocById(Long id)
    {
        return frontSysDocMapper.selectFrontSysDocById(id);
    }

    /**
     * 查询文档管理列表
     *
     * @param frontSysDoc 文档管理
     * @return 文档管理
     */
    @Override
    public List<FrontSysDoc> selectFrontSysDocList(FrontSysDoc frontSysDoc)
    {
        return frontSysDocMapper.selectFrontSysDocList(frontSysDoc);
    }

    /**
     * 新增文档管理
     *
     * @param frontSysDoc 文档管理
     * @return 结果
     */
    @Override
    public int insertFrontSysDoc(FrontSysDoc frontSysDoc)
    {
        frontSysDoc.setCreateTime(DateUtils.getNowDate());
        return frontSysDocMapper.insertFrontSysDoc(frontSysDoc);
    }

    /**
     * 修改文档管理
     *
     * @param frontSysDoc 文档管理
     * @return 结果
     */
    @Override
    public int updateFrontSysDoc(FrontSysDoc frontSysDoc)
    {
        frontSysDoc.setUpdateTime(DateUtils.getNowDate());
        return frontSysDocMapper.updateFrontSysDoc(frontSysDoc);
    }

    /**
     * 批量删除文档管理
     *
     * @param ids 需要删除的文档管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontSysDocByIds(Long[] ids)
    {
        return frontSysDocMapper.deleteFrontSysDocByIds(ids);
    }

    /**
     * 删除文档管理信息
     *
     * @param id 文档管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontSysDocById(Long id)
    {
        return frontSysDocMapper.deleteFrontSysDocById(id);
    }

    @Override
    public FrontSysDoc selectFrontSysDocByDocType(String typeName) {

        LambdaQueryWrapper<FrontSysDoc> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontSysDoc::getDocType, typeName);
        queryWrapper.orderByDesc(FrontSysDoc::getCreateTime);
        List<FrontSysDoc> frontSysDocs = frontSysDocMapper.selectList(queryWrapper);
        if (frontSysDocs != null && frontSysDocs.size() > 0){
            return frontSysDocs.get(0);
        }
        return null;
    }
}
