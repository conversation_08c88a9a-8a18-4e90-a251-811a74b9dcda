package com.ruoyi.uni.uni.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 经期记录对象 front_period_info
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Schema(name = "用户经期记录")
@Data
@TableName("front_period_info")
public class FrontPeriodInfo
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /**  */
    @Excel(name = "")
    private Long userId;

    /** 经期 */
    @Excel(name = "经期日历数组")
    private String period;

    @Excel(name = "经期月份")
    private String month;

    /** 排卵期 */
    @Excel(name = "排卵期")
    private String ovulation;

    /** 排卵日 */
    @Excel(name = "排卵日")
    private String ovulaDay;

    /** 预测经期 */
    @Excel(name = "预测经期")
    private String predPeriod;

    /** 每月经期开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "每月经期开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date periodStart;

    /** 每月经期结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "每月经期结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date periodEnd;

    /** 比上周期提前延期了多少天 */
    @Excel(name = "比上周期提前延期了多少天")
    private Long compareTime;

    /** 经期天数 */
    @Excel(name = "经期天数")
    private Long menstrualPeriodDays;

    /** 周期天数 */
    @Excel(name = "周期天数")
    private Long cycleDays;


    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 备注 */
    private String remark;
}
