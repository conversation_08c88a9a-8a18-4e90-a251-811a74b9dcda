package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.uni.uni.service.IFrontSysDocService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文档管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/doc")
@Tag(name = "文档管理Controller", description = "文档管理Controller")
public class FrontSysDocController extends BaseController
{
    @Autowired
    private IFrontSysDocService frontSysDocService;

    /**
     * 获取文档管理详细信息
     */
    @GetMapping(value = "/getByType")
    @Operation(description = "根据类型获取文档管理信息")
    public AjaxResult getInfo(@RequestParam("type") String type)
    {
        return success(frontSysDocService.selectFrontSysDocByDocType(type));
    }



}
