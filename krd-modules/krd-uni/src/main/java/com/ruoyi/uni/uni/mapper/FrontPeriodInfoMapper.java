package com.ruoyi.uni.uni.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.uni.uni.domain.FrontPeriodInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 经期记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Mapper
public interface FrontPeriodInfoMapper extends BaseMapper<FrontPeriodInfo>
{
    /**
     * 查询经期记录
     *
     * @param id 经期记录主键
     * @return 经期记录
     */
    public FrontPeriodInfo selectFrontPeriodInfoById(Long id);

    /**
     * 查询经期记录列表
     *
     * @param frontPeriodInfo 经期记录
     * @return 经期记录集合
     */
    public List<FrontPeriodInfo> selectFrontPeriodInfoList(FrontPeriodInfo frontPeriodInfo);

    /**
     * 新增经期记录
     *
     * @param frontPeriodInfo 经期记录
     * @return 结果
     */
    public int insertFrontPeriodInfo(FrontPeriodInfo frontPeriodInfo);

    /**
     * 修改经期记录
     *
     * @param frontPeriodInfo 经期记录
     * @return 结果
     */
    public int updateFrontPeriodInfo(FrontPeriodInfo frontPeriodInfo);

    /**
     * 删除经期记录
     *
     * @param id 经期记录主键
     * @return 结果
     */
    public int deleteFrontPeriodInfoById(Long id);

    /**
     * 批量删除经期记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontPeriodInfoByIds(Long[] ids);
}
