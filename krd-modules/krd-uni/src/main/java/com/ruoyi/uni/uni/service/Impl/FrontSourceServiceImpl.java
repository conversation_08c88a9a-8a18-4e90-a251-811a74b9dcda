package com.ruoyi.uni.uni.service.Impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.SysConfig;
import com.ruoyi.system.api.mapper.FrontSourceMapper;
import com.ruoyi.system.api.mapper.SysConfigMapper;
import com.ruoyi.uni.uni.service.IFrontSourceService;
import com.ruoyi.uni.uni.service.IUniUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * @ClassName FrontSourceServiceImpl
 * @Description 用户积分处理业务接口实现类
 * <AUTHOR>
 * @Date 2025/6/18 上午11:30
 */
@Slf4j
@Service
public class FrontSourceServiceImpl implements IFrontSourceService {

    @Autowired
    private FrontSourceMapper frontSourceMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private IUniUserService uniUserService;

    @Override
    public BigDecimal getUserMemberLevelDiscount() {
        Long userId = uniUserService.getUserId();
        // 获取用户积分
        Integer point = frontSourceMapper.selectFrontUserPoint(userId);

        BigDecimal discount = BigDecimal.ZERO;

        // 计算用户积分 位于那个会员等级
        if (point != null){
            SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique("member_config");
            String configValue = sysConfig.getConfigValue();
            JSONObject jsonObject = JSONObject.parseObject(configValue);
            JSONArray keys = jsonObject.getJSONArray("memberLevels");

            // 循环判断 积分位于那个等级
            for (int i = 0; i < keys.size(); i++) {
                JSONObject jsonObject1 = keys.getJSONObject(i);
                if (point >= jsonObject1.getInteger("points")){
                    // 获取会员等级折扣
                    discount = new BigDecimal(jsonObject1.getString("discount"));
                    continue;
                }
                break;
            }

        }

        return discount;
    }
}
