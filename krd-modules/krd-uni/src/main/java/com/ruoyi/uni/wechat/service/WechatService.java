package com.ruoyi.uni.wechat.service;

import com.ruoyi.uni.uni.domain.vo.WeChatMiniAuthorizeVo;

/**
 * @ClassName WechatService
 * @Description 微信相关处理业务类
 * <AUTHOR>
 * @Date 2025/5/16 上午10:43
 */
public interface WechatService {

    /**
     * 小程序登录凭证校验
     * @return 小程序登录校验对象
     */
    WeChatMiniAuthorizeVo miniAuthCode(String code);

    //解密手机号
    String decryptPhone(String code);

    String getMiniAccessToken();
}
