package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.uni.uni.domain.family.FrontFamily;
import com.ruoyi.uni.uni.service.IUniFrontFamilyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.DeleteMapping;

/**
 * @ClassName FrontFamilyUniController
 * @Description 用户家庭接口控制器
 * <AUTHOR>
 * @Date 2025/5/19 下午12:14
 */
@Slf4j
@Tag(name = "用户家庭接口控制器", description = "小程序用户家庭接口控制器")
@RestController
@RequestMapping("/uni/user/family")
public class UniFrontFamilyController extends BaseController {

    @Autowired
    private IUniFrontFamilyService uniFrontFamilyService;

    @Operation(description = "获取用户家庭成员列表")
    @GetMapping("/getList")
    public AjaxResult getList(){
        return AjaxResult.success(uniFrontFamilyService.getList());
    }

    @Operation(description = "添加用户家庭成员")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FrontFamily frontFamily){
        return AjaxResult.success(uniFrontFamilyService.add(frontFamily));
    }

    @Operation(description = "修改用户家庭成员")
    @PostMapping("/update")
    public AjaxResult update(@RequestBody FrontFamily frontFamily){
        return AjaxResult.success(uniFrontFamilyService.update(frontFamily));
    }

    @Operation(description = "删除用户家庭成员")
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id){
        return AjaxResult.success(uniFrontFamilyService.delete(id));
    }

}
