package com.ruoyi.uni.check.service.impl;

import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.CommonCheckPreCreateOrderResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.CommonCreateOrderRequest;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.CommonProductInfo;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.Contact;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.Response;
import com.lop.open.api.sdk.request.ECAP.EcapV1OrdersPrecheckLopRequest;
import com.lop.open.api.sdk.response.ECAP.EcapV1OrdersPrecheckLopResponse;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.uni.check.config.JDLConfig;
import com.ruoyi.uni.check.domain.dto.JDPrecheckOrderDTO;
import com.ruoyi.uni.check.service.JDEcapV1OrdersPrecheckLopService;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 下单前置校验服务
 * <AUTHOR>
 * @date 2025/6/5 15:31
 */
@Service
public class JDEcapV1OrdersPrecheckLopServiceImpl implements JDEcapV1OrdersPrecheckLopService {
    
    @Resource
    private ObjectProvider<JDLConfig> jdlConfigProvider;

    @Override
    public R<CommonCheckPreCreateOrderResponse> ecapV1OrdersPrecheck(JDPrecheckOrderDTO precheckOrderDTO) {
        try {
            JDLConfig jdlConfig = jdlConfigProvider.getObject();
            System.out.println(jdlConfig);
            //收件人信息
//            Contact receiverContact = new Contact();
//            receiverContact.setFullAddress(jdlConfig.getReceiverContact_fullAddress());
//            receiverContact.setName(jdlConfig.getReceiverContact_name());
//            receiverContact.setMobile(jdlConfig.getReceiverContact_mobile());

            //产品信息
            CommonProductInfo productInfo = new CommonProductInfo();
            productInfo.setProductCode(jdlConfig.getProductsReq_productCode());
            //入参对象
            EcapV1OrdersPrecheckLopRequest request = new EcapV1OrdersPrecheckLopRequest();
            request.addLopPlugin(jdlConfig.getLopPlugin());
            //公共下单参数
            CommonCreateOrderRequest requestDTO = new CommonCreateOrderRequest();
            requestDTO.setOrderOrigin(jdlConfig.getOrderOrigin());
            requestDTO.setCustomerCode(jdlConfig.getCustomerCode());
            requestDTO.setProductsReq(productInfo);//产品信息
            requestDTO.setSenderContact(precheckOrderDTO.getSenderContact());//发货人信息
            requestDTO.setReceiverContact(precheckOrderDTO.getReceiverContact());//收件人信息

            request.setRequest(requestDTO);

            EcapV1OrdersPrecheckLopResponse response = jdlConfig.getClient().execute(request);
            String code = response.getCode();
            if ("0".equals(code)){//成功
                Response<CommonCheckPreCreateOrderResponse> result = response.getResult();
                if(result.getSuccess()){
                    return R.ok(result.getData());
                }
                return R.fail(result.getMsg() + "(" + result.getSubMsg() + ")");
            }
            return R.fail(response.getMsg());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
