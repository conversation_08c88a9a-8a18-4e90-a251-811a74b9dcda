package com.ruoyi.uni.check.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.uni.check.domain.dto.JDCreateOrderDTO;
import com.ruoyi.uni.check.service.FrontNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 样本回寄
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-04 10:40:54
 */
//@Slf4j
@RestController
@RequestMapping("check/samplesend")
public class SampleSendController {
    @Autowired
    private FrontNumberService frontNumberService;

    /**
     * 获取需要邮寄的套餐列表
     * @return
     */
    @RequestMapping("/sendList")
    public R sendList(){
        return R.ok(frontNumberService.sendList());
    }

    /**
     * 查询取件地址列表
     * @return
     */
    @RequestMapping("/getAddressList")
    public R getAddressList(){
        return R.ok(frontNumberService.getAddressList());
    }

    /**
     * 样品回寄接口
     * @return
     */
    @PostMapping("/reshipping")
    public R reshipping(@RequestBody JDCreateOrderDTO createOrderDTO){
        return R.ok(frontNumberService.reshipping(createOrderDTO));
    }

}
