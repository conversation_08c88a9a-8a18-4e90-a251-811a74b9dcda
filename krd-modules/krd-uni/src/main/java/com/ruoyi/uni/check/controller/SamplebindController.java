package com.ruoyi.uni.check.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.uni.check.domain.dto.SampleBindSaveDTO;
import com.ruoyi.uni.check.service.FrontNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 样本绑定
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-04 10:40:54
 */
@RestController
@RequestMapping("check/samplebind")
public class SamplebindController {
    @Autowired
    private FrontNumberService frontNumberService;

    /**
     * 根据二维码获取套餐
     * @param itemCode
     * @return
     */
    @RequestMapping("/pakeage/{itemCode}")
    public R pakeageModel(@PathVariable("itemCode") String itemCode){
        return R.ok(frontNumberService.getPakeageByItemCode(itemCode));
    }

    /**
     * 保存绑定套餐
     * @param saveDTO
     * @return
     */
    @PostMapping("/pakeage/save")
    public R pakeageSave(@RequestBody SampleBindSaveDTO saveDTO){
        frontNumberService.pakeageSave(saveDTO);
        return R.ok(null,"绑定套餐成功");
    }

}
