package com.ruoyi.uni.uni.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.system.api.domain.FrontVideo;

import java.util.List;

/**
 * 采集视频Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface IFrontVideoService
{
    /**
     * 查询采集视频
     *
     * @param id 采集视频主键
     * @return 采集视频
     */
    public FrontVideo selectFrontVideoById(Long id) throws JsonProcessingException;

    /**
     * 查询采集视频列表
     *
     * @param frontVideo 采集视频
     * @return 采集视频集合
     */
    public List<FrontVideo> selectFrontVideoList(FrontVideo frontVideo);



}
