package com.ruoyi.uni.system.service.impl;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.SysConfig;
import com.ruoyi.system.api.mapper.SysConfigMapper;
import com.ruoyi.uni.system.service.GetConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName GetConfigServiceImpl
 * @Description 获取配置服务实现类
 * <AUTHOR>
 * @Date 2025/6/12 上午11:50
 */
@Service
@Slf4j
public class GetConfigServiceImpl implements GetConfigService {

    @Autowired
    private SysConfigMapper configMapper;

    @Override
    public SysConfig selectConfigByKeyUniObj(String configKey) {
        SysConfig retConfig = configMapper.checkConfigKeyUnique(configKey);
        if (StringUtils.isNotNull(retConfig))
        {
            return retConfig;
        }
        return null;
    }
}
