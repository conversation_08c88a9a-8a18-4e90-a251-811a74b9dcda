package com.ruoyi.uni.uni.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.uni.uni.domain.family.FrontFamily;

import java.util.List;

/**
 * @ClassName IFrontFamilyController
 * @Description 用户家庭管理业务类
 * <AUTHOR>
 * @Date 2025/5/19 下午12:20
 */
public interface IUniFrontFamilyService extends IService<FrontFamily> {

    /**
     * 获取用户家庭成员列表
     * @return
     */
    List<FrontFamily> getList();

    /**
     * 添加用户家庭成员
     * @param frontFamily
     * @return
     */
    Boolean add(FrontFamily frontFamily);

    /**
     * 修改用户家庭成员
     * @param frontFamily
     * @return
     */
    Boolean update(FrontFamily frontFamily);

    /**
     * 删除用户家庭成员
     * @param id
     * @return
     */
    Boolean delete(Long id);
}
