package com.ruoyi.uni;

import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 */ //TIP 要<b>运行</b>代码，请按 <shortcut actionId="Run"/> 或
// 点击装订区域中的 <icon src="AllIcons.Actions.Execute"/> 图标。
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class RuoYiUniApplication {
    public static void main(String[] args)
    {
        SpringApplication.run(RuoYiUniApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  小程序模块启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}
