package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.CblTableDataInfo;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontOrdersInvoice;
import com.ruoyi.uni.uni.service.IFrontOrdersService;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.vo.FrontOrdersVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单总Controller
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/orders")
@Tag(name = "FrontOrdersController", description ="订单总控制类")
public class FrontOrdersController extends BaseController
{
    @Autowired
    private IFrontOrdersService frontOrdersService;

    /**
     * 查询订单总列表
     */
//    @RequiresPermissions("front:orders:list")
    @Operation(description = "查询订单总列表")
    @GetMapping("/list")
    public TableDataInfo list(FrontOrdersVo.FrontOrdersSearch vo)
    {
        startPage();
        List<FrontOrders> list = frontOrdersService.selectFrontOrdersList(vo);
        return getDataTable(list);
    }


    @Operation(description = "小程序查询订单总列表")
    @GetMapping("/list/app")
    public CblTableDataInfo list(FrontOrdersVo.UniFrontOrdersSearch vo)
    {
        startPage();
        List<FrontOrders> list = frontOrdersService.selectUniOrdersList(vo);
        return getTableUni(list);
    }

    /**
     * 获取订单总详细信息
     */
    @RequiresPermissions("front:orders:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontOrdersService.selectFrontOrdersById(id));
    }

    /**
     * 新增订单总
     */
    @RequiresPermissions("front:orders:add")
    @Log(title = "订单总", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FrontOrders frontOrders)
    {
        return toAjax(frontOrdersService.insertFrontOrders(frontOrders));
    }

    /**
     * 修改订单总
     */
    @RequiresPermissions("front:orders:edit")
    @Log(title = "订单总", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontOrders frontOrders)
    {
        return toAjax(frontOrdersService.updateFrontOrders(frontOrders));
    }

    /**
     * 删除订单总
     */
    @RequiresPermissions("front:orders:remove")
	@DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(frontOrdersService.deleteFrontOrdersById(id));
    }

    //小程序查看我的订单

    @Operation(summary = "小程序下单")
    @PostMapping("/add/app")
    public AjaxResult addApp(@RequestBody FrontOrdersVo.FrontOrdersAddInfo vo){
        return success("成功",frontOrdersService.addOrder(vo));
    }

    @Operation(summary = "查看订单详情")
    @GetMapping("/getDetail")
    public AjaxResult getDetail(@RequestParam String orderNo){
        return success(frontOrdersService.selectOrderDetailByOrderNo(orderNo));
    }

    @Operation(summary = "取消订单")
    @PostMapping("/cancel/{orderNo}")
    public AjaxResult cancel(@PathVariable String orderNo){
        return success(frontOrdersService.cancelOrder(orderNo));
    }

    @Operation(summary = "订单修改地址")
    @PostMapping("/updateAddress")
    public AjaxResult updateAddress(@RequestBody FrontOrdersVo.UpdateAddressInfo vo){
        return success(frontOrdersService.updateAddress(vo));
    }

    @Operation(summary = "完成订单")
    @PostMapping("/finish/{orderNo}")
    public AjaxResult finish(@PathVariable String orderNo){
        return success(frontOrdersService.finishOrder(orderNo));
    }

    @Operation(summary = "订单评价")
    @PostMapping("/comment")
    public AjaxResult comment(@RequestBody FrontOrdersVo.CommentInfo vo){
        return success(frontOrdersService.comment(vo));
    }

    @Operation(summary = "查看评价")
    @GetMapping("/getComment")
    public AjaxResult getComment(@ModelAttribute FrontOrdersVo.CommentInfo vo){
        return success(frontOrdersService.getComment(vo));
    }


    @Operation(summary = "订单支付")
    @PostMapping("/payment")
    public AjaxResult payment(@RequestBody FrontOrdersVo.UpdateAddressInfo vo){
        return success(frontOrdersService.payment(vo));
    }

    @Operation(summary = "订单提交退款")
    @PostMapping("/refund")
    public AjaxResult refund(@RequestBody FrontOrdersVo.RefundInfo vo){
        return success("退单号",frontOrdersService.refundOrder(vo));
    }

    @Operation(summary = "查询订单对应商品售后参数")
    @PostMapping("/getAfterInfo")
    public AjaxResult getAfterInfo(@RequestBody FrontOrdersVo.RefundList vo){
        return success(frontOrdersService.getAfterInfo(vo));
    }

    @Operation(summary = "查询订单对应退款详情")
    @GetMapping("/getRefundInfo")
    public AjaxResult getRefundInfo(@RequestParam("orderNo") String orderNo){
        return success(frontOrdersService.getRefundInfo(orderNo));
    }

    @Operation(summary = "撤销订单售后")
    @PostMapping("/cancelRefundOrder/{orderNo}")
    public AjaxResult cancelRefundOrder(@PathVariable(value = "orderNo") String orderNo){
        return success(frontOrdersService.cancelRefundOrder(orderNo));
    }

    @Operation(summary = "更新售后订单物流信息")
    @PostMapping("/updateRefundOrder")
    public AjaxResult updateRefundOrder(@RequestBody FrontOrdersVo.ReturnExpressInfo vo){
        return success(frontOrdersService.updateRefundOrder(vo));
    }

    @Operation(summary = "删除订单")
    @DeleteMapping("/deleteByOrderNo/{orderNo}")
    public AjaxResult delete(@PathVariable String orderNo){
        return success(frontOrdersService.deleteOrder(orderNo));
    }

    @Operation(summary = "订单发票申请")
    @PostMapping("/invoice")
    public AjaxResult invoice(@RequestBody FrontOrdersInvoice invoice){
        return success(frontOrdersService.invoiceApply(invoice));
    }

    @Operation(summary = "查询最新发票信息")
    @GetMapping("/getInvoice")
    public AjaxResult getInvoice(){
        return success(frontOrdersService.selectInvoiceByUserId());
    }

    @Operation(summary = "再来一单")
    @PostMapping("/again/{orderNo}")
    public AjaxResult again(@PathVariable String orderNo){
        return success("订单号",frontOrdersService.againOrder(orderNo));
    }

    @Operation(summary = "查看发票")
    @GetMapping("/selectInvoiceByOrderId/{orderId}")
    public AjaxResult getInvoiceList(@PathVariable Long orderId){
        return success(frontOrdersService.selectInvoiceByOrderId(orderId));
    }


}
