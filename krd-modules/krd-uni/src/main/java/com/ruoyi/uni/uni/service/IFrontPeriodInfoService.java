package com.ruoyi.uni.uni.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.uni.uni.domain.FrontPeriodInfo;

import java.util.List;

/**
 * 经期记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface IFrontPeriodInfoService
{
    /**
     * 查询经期记录
     *
     * @param id 经期记录主键
     * @return 经期记录
     */
    public FrontPeriodInfo selectFrontPeriodInfoById(Long id);

    /**
     * 查询经期记录列表
     *
     * @param frontPeriodInfo 经期记录
     * @return 经期记录集合
     */
    public List<FrontPeriodInfo> selectFrontPeriodInfoList(FrontPeriodInfo frontPeriodInfo);

    /**
     * 小程序根据用户月份查询预测经期
     *
     * @return 结果
     */
    public FrontPeriodInfo selectFrontPeriodInfoByMonth(String inputMonth) throws JsonProcessingException;

    //修改经期设置
    public int updateFrontPeriodInfo(FrontUser frontUser);



}
