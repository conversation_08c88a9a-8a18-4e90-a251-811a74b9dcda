package com.ruoyi.uni.uni.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName WeChatMiniAuthorizeVo
 * @Description 微信小程序用户授权返回数据
 * <AUTHOR>
 * @Date 2025/5/15 下午5:24
 */
@Data
@Schema(description = "微信小程序用户授权返回数据")
public class WeChatMiniAuthorizeVo implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(name = "会话密钥")
    private String sessionKey;

    @Schema(name = "用户唯一标识")
    private String openId;

    @Schema(name = "用户在开放平台的唯一标识符，若当前小程序已绑定到微信开放平台帐号下会返回")
    private String unionId;

    @Schema(name = "错误码")
    private String errCode;

    @Schema(name = "错误信息")
    private String errMsg;
}

