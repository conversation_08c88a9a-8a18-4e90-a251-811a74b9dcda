package com.ruoyi.uni.check.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.uni.check.domain.dto.JDCreateOrderDTO;
import com.ruoyi.uni.check.service.FrontNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 样本状态
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-04 10:40:54
 */
//@Slf4j
@RestController
@RequestMapping("check/samplestatus")
public class SampleStatusController {
    @Autowired
    private FrontNumberService frontNumberService;

    /**
     * 查询样本状态列表
     * @return
     */
    @RequestMapping("/getSampleStatusList")
    public R getSampleStatusList(String searchValue){
        return R.ok(frontNumberService.getSampleStatusList(searchValue));
    }

    /**
     * 查询样本状态
     * @return
     */
    @RequestMapping("/getSampleStatusByItemCode")
    public R getSampleStatusByItemCode(String itemCode){
        return R.ok(frontNumberService.getSampleStatusByItemCode(itemCode));
    }

}
