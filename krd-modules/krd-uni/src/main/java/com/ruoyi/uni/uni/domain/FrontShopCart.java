package com.ruoyi.uni.uni.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 购物车对象 front_shop_cart
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@Schema(description = "购物车对象 front_shop_cart")
@TableName("front_shop_cart")
public class FrontShopCart implements Serializable
{

    /** 购物车记录ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "购物车记录ID")
    private Long id;

    /** 关联用户ID */
    @Schema(description = "关联用户ID")
    private Long userId;

    /** 关联商品ID */
    @Schema(description = "关联商品ID")
    private Long goodsId;

    /** 关联规格ID */
    @Schema(description = "关联规格ID")
    private Long specId;

    /** 商品数量 */
    @Schema(description = "商品数量")
    private Long quantity;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
}
