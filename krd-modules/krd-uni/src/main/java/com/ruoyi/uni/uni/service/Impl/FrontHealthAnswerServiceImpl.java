package com.ruoyi.uni.uni.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.FrontHealthAnswer;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.mapper.FrontHealthAnswerMapper;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.uni.uni.service.IFrontHealthAnswerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


/**
 * 健康测试管理用户回答Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class FrontHealthAnswerServiceImpl implements IFrontHealthAnswerService
{
    @Autowired
    private FrontHealthAnswerMapper frontHealthAnswerMapper;

    @Autowired
    private FrontUserMapper frontUserMapper;

    /**
     * 查询健康测试管理用户回答
     *
     * @param id 健康测试管理用户回答主键
     * @return 健康测试管理用户回答
     */
    @Override
    public FrontHealthAnswer selectFrontHealthAnswerById(Long id)
    {
        return frontHealthAnswerMapper.selectFrontHealthAnswerById(id);
    }

    /**
     * 查询健康测试管理用户回答列表
     *
     * @param frontHealthAnswer 健康测试管理用户回答
     * @return 健康测试管理用户回答
     */
    @Override
    public List<FrontHealthAnswer> selectFrontHealthAnswerList(FrontHealthAnswer frontHealthAnswer)
    {
        return frontHealthAnswerMapper.selectFrontHealthAnswerList(frontHealthAnswer);
    }

    /**
     * 新增健康测试管理用户回答
     *
     * @param frontHealthAnswer 健康测试管理用户回答
     * @return 结果
     */
    @Override
   public int insertFrontHealthAnswer(FrontHealthAnswer frontHealthAnswer) {
    try {
        FrontHealthAnswer existAnswer = frontHealthAnswerMapper.selectOne(new LambdaQueryWrapper<FrontHealthAnswer>()
                .eq(FrontHealthAnswer::getUid, frontHealthAnswer.getUid())
                .eq(FrontHealthAnswer::getType, frontHealthAnswer.getType()));

        if (existAnswer != null && existAnswer.getType() == 1) {
            Long userId = SecurityUtils.getUserId();
            FrontUser frontUser = frontUserMapper.selectById(userId);
            LambdaUpdateWrapper<FrontUser> updateWrapper = new LambdaUpdateWrapper<FrontUser>();
            updateWrapper.eq(FrontUser::getId, userId);
            updateWrapper.set(FrontUser::getIsAnswerHealth, 1);
            updateWrapper.set(FrontUser::getIntegral,frontUser.getIntegral().add(BigDecimal.valueOf(10)));
            frontUserMapper.update(null, updateWrapper);
            return frontHealthAnswerMapper.update(frontHealthAnswer, new LambdaUpdateWrapper<FrontHealthAnswer>()
                    .eq(FrontHealthAnswer::getUid, frontHealthAnswer.getUid()));
        } else {
            return frontHealthAnswerMapper.insert(frontHealthAnswer);
        }
    } catch (Exception e) {
        e.printStackTrace();
        return -1;
    }
}


    /**
     * 修改健康测试管理用户回答
     *
     * @param frontHealthAnswer 健康测试管理用户回答
     * @return 结果
     */
    @Override
    public int updateFrontHealthAnswer(FrontHealthAnswer frontHealthAnswer)
    {
        return frontHealthAnswerMapper.updateFrontHealthAnswer(frontHealthAnswer);
    }

    /**
     * 批量删除健康测试管理用户回答
     *
     * @param ids 需要删除的健康测试管理用户回答主键
     * @return 结果
     */
    @Override
    public int deleteFrontHealthAnswerByIds(Long[] ids)
    {
        return frontHealthAnswerMapper.deleteFrontHealthAnswerByIds(ids);
    }

    /**
     * 删除健康测试管理用户回答信息
     *
     * @param id 健康测试管理用户回答主键
     * @return 结果
     */
    @Override
    public int deleteFrontHealthAnswerById(Long id)
    {
        return frontHealthAnswerMapper.deleteFrontHealthAnswerById(id);
    }
}
