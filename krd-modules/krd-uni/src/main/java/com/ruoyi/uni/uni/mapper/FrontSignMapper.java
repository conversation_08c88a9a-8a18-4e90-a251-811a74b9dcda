package com.ruoyi.uni.uni.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.uni.uni.domain.FrontSign;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户签到Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Mapper
public interface FrontSignMapper extends BaseMapper<FrontSign>
{
    /**
     * 查询用户签到
     *
     * @param id 用户签到主键
     * @return 用户签到
     */
    public FrontSign selectFrontSignById(String id);

    /**
     * 查询用户签到列表
     *
     * @param frontSign 用户签到
     * @return 用户签到集合
     */
    public List<FrontSign> selectFrontSignList(FrontSign frontSign);

    /**
     * 新增用户签到
     *
     * @param frontSign 用户签到
     * @return 结果
     */
    public int insertFrontSign(FrontSign frontSign);

    /**
     * 修改用户签到
     *
     * @param frontSign 用户签到
     * @return 结果
     */
    public int updateFrontSign(FrontSign frontSign);

    /**
     * 删除用户签到
     *
     * @param id 用户签到主键
     * @return 结果
     */
    public int deleteFrontSignById(String id);

    /**
     * 批量删除用户签到
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontSignByIds(String[] ids);
}
