package com.ruoyi.uni.uni.service;

import com.ruoyi.system.api.domain.FrontAdvert;
import com.ruoyi.uni.uni.domain.vo.UniGoodsVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName UniFrontGoodsService
 * @Description 小程序商城相关业务接口
 * <AUTHOR>
 * @Date 2025/6/4 下午5:31
 */
public interface UniFrontGoodsService {

    /**
     * 首页产品
     *
     * @param num 1-首页大图展示 2-首页小图展示
     * @return
     */
    List<UniGoodsVo.RecommendGoods> getIndexImageList(Integer num,HttpServletRequest  request);

    /**
     * 获取商品主页广告
     *
     * @param type 广告类型
     * @return
     */
    List<FrontAdvert> getGoodsAdvertList(Long type);

    /**
     * 获取商品分类
     * @return
     */
    List<UniGoodsVo.Gategory> getGategoryList();



    /**
     * 获取商品详情
     *
     * @param id 商品id
     * @return
     */
    UniGoodsVo.GoodsDetail getGoodsById(Long id, HttpServletRequest request);

    /**
     * 获取商品列表 / 分类商品列表
     *
     * @param vo vo
     * @return
     */
    List<UniGoodsVo.RecommendGoods> getGoodsList(UniGoodsVo.GoodsListSearch vo,HttpServletRequest request);

    /**
     * 获取商品评价列表
     * @param id
     * @return
     */
    List<UniGoodsVo.EvaluateDetail> getGoodsEvaluateList(Long id);
}

