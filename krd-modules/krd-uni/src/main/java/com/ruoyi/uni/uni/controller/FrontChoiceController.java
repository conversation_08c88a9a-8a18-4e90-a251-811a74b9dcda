package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.api.domain.FrontChoice;
import com.ruoyi.uni.uni.service.IFrontChoiceService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 精选内容Controller
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/choice")
@Tag(name = "精选内容Controller", description = "精选内容Controller")
public class FrontChoiceController extends BaseController
{
    @Autowired
    private IFrontChoiceService frontChoiceService;

    /**
     * 查询精选内容列表
     */
    @GetMapping("/list")
    @Tag(name = "查询精选内容列表", description = "查询精选内容列表")
    public AjaxResult list(FrontChoice frontChoice)
    {

        List<FrontChoice> list = frontChoiceService.selectFrontChoiceList(frontChoice);
        return success(list);
    }


    /**
     * 获取精选内容详细信息
     */
    @GetMapping(value = "/{id}")
    @Tag(name = "获取精选内容详细信息", description = "获取精选内容详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontChoiceService.selectFrontChoiceById(id));
    }


}
