package com.ruoyi.uni.wxpay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.HashMap;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description: 微信模板发送类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TemplateMessageVo {
    @Schema(name = "OPENID", required = true)
    private String touser;

    @Schema(name = "模板ID", required = true)
    private String template_id;

    @Schema(name = "模板跳转链接（海外帐号没有跳转能力）")
    private String url;

    @Schema(name = "发送内容")
    private HashMap<String, SendTemplateMessageItemVo> data;
}
