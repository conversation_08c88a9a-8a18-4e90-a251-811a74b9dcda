package com.ruoyi.uni.check.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 编码管理
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-04 10:40:54
 */
@Data
@TableName("front_number")
public class FrontNumberEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Long id;
	/**
	 * 项目编码：
3位(项目)+4位(自定义)+6位(序号)
注：项目和自定义作为前缀，序号顺序生成，最大99万
	 */
	private String itemCode;
	/**
	 * 套餐ID
	 */
	private Long packageId;
	/**
	 * 自定义编码
	 */
	private String number;
	/**
	 * 二维码数量
	 */
	private Integer qrcount;
	/**
	 * 条形码数量
	 */
	private Integer barcount;
	/**
	 * 下单用户id
	 */
	private Long orderUserId;
	/**
	 * 下单家庭成员id
	 */
	private Long orderFamilyId;
	/**
	 * 下单时间
	 */
	private Date orderTime;
	/**
	 * 检测人id（see：front_family）
	 */
	private Long checkFamilyId;
	/**
	 * 检测人年龄(单位：岁)
	 */
	private Integer checkFamilyAge;
	/**
	 * 检测人体重(单位：kg)
	 */
	private Double checkFamilyWeight;
	/**
	 * 检测人身高(单位：cm)
	 */
	private Double checkFamilyHeight;
	/**
	 * 京东物流商家订单号：front_jd_order.id
	 */
	private String jdOrderId;
	/**
	 * 检测机构id（see：front_check）
	 */
	private Long checkId;
	/**
	 * 编码生成时间
	 */
	private Date codeCreateTime;
	/**
	 * 套码打印时间
	 */
	private Date printTime;
	/**
	 * 绑定时间
	 */
	private Date bindingTime;
	/**
	 * 回寄时间
	 */
	private Date shippTime;
	/**
	 * 接收时间
	 */
	private Date receiveTime;
	/**
	 * 样本处理时间
	 */
	private Date handleTime;
	/**
	 * 样本处理备注
	 */
	private String handleRemark;
	/**
	 * 报告审核人id
	 */
	private Long reportReviewFamilyId;
	/**
	 * 报告审核时间
	 */
	private Date reportReviewTime;
	/**
	 * 报告审核备注
	 */
	private String reportReviewNotes;
	/**
	 * 报告复核人id
	 */
	private Long reportReReviewFamilyId;
	/**
	 * 报告复核时间
	 */
	private Date reportReReviewTime;
	/**
	 * 报告复核备注
	 */
	private String reportReReviewNotes;
	/**
	 * 报告状态
01-未出报告
02-未审核（已出报告）
03-已审核-不通过
04-已审核-通过
05-已复核-不通过
06-已复核-已发布
	 */
	private String reportStatus;
	/**
	 * 编码状态
01-已生成
02-套码已打印
03-转管码已打印
	 */
	private String numberStatus;
	/**
	 * 样本状态
01-未绑定
02-已绑定
03-已回寄（物流状态）
04-已接收（转管码已打印）
05-不合格
06-检测中（合格）

	 */
	private String sampleStatus;
	/**
	 * 流程总状态
01-已生成
02-套码已打印
03-已绑定
04-已回寄（物流状态）
05-已接收（转管码已打印）
06-已处理（不合格，合格）
07-未审核（已出报告）
08-已审核（不通过，通过）
09-已复核（不通过，已发布）

	 */
	private String processStatus;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;
	/**
	 * 
	 */
	private String remark;

}
