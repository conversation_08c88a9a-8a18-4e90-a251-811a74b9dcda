package com.ruoyi.uni.check.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonCreateOrderResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderStatusV1.CommonOrderStatusResponse;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.uni.check.config.JDLConfig;
import com.ruoyi.uni.check.domain.dto.CreateContactDTO;
import com.ruoyi.uni.check.domain.dto.JDCreateOrderDTO;
import com.ruoyi.uni.check.domain.dto.SampleBindSaveDTO;
import com.ruoyi.uni.check.domain.dto.SampleStatusDTO;
import com.ruoyi.uni.check.domain.entity.FrontJdOrderEntity;
import com.ruoyi.uni.check.domain.entity.FrontNumberEntity;
import com.ruoyi.uni.check.domain.entity.FrontReportResultEntity;
import com.ruoyi.uni.check.domain.entity.FrontTmpPactarResultEntity;
import com.ruoyi.uni.check.domain.vo.AddressVO;
import com.ruoyi.uni.check.domain.vo.BindPakeageVO;
import com.ruoyi.uni.check.domain.vo.ReportBaseInfoVO;
import com.ruoyi.uni.check.domain.vo.ReportPactarVO;
import com.ruoyi.uni.check.domain.vo.ReportPreviewVO;
import com.ruoyi.uni.check.domain.vo.ReportResultVO;
import com.ruoyi.uni.check.domain.vo.SampleStatusVO;
import com.ruoyi.uni.check.domain.vo.SendPakeageVO;
import com.ruoyi.uni.check.enums.ProcessStatusEnum;
import com.ruoyi.uni.check.enums.ReportStatusEnum;
import com.ruoyi.uni.check.enums.SampleStatusEnum;
import com.ruoyi.uni.check.mapper.FrontJdOrderMapper;
import com.ruoyi.uni.check.mapper.FrontNumberMapper;
import com.ruoyi.uni.check.mapper.FrontReportResultMapper;
import com.ruoyi.uni.check.mapper.FrontTmpPactarMapper;
import com.ruoyi.uni.check.mapper.FrontTmpPactarResultMapper;
import com.ruoyi.uni.check.service.FrontNumberService;
import com.ruoyi.uni.check.service.JDEcapV1OrdersCreateLopService;
import com.ruoyi.uni.uni.utils.oss.OssUrlCleanerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service("frontNumberService")
public class FrontNumberServiceImpl extends ServiceImpl<FrontNumberMapper, FrontNumberEntity> implements FrontNumberService {

    @Resource
    private FrontNumberMapper frontNumberMapper;
    @Resource
    private TokenService tokenService;
    @Resource
    private FrontJdOrderMapper frontJdOrderMapper;
    @Resource
    private ObjectProvider<JDLConfig> jdlConfigProvider;
    @Resource
    private JDEcapV1OrdersCreateLopService jdEcapV1OrdersCreateLopService;
    @Resource
    private OssUrlCleanerUtil ossUrlCleanerUtil;
    @Resource
    private FrontTmpPactarMapper frontTmpPactarMapper;
    @Resource
    private FrontTmpPactarResultMapper frontTmpPactarResultMapper;
    @Autowired
    private FrontReportResultMapper frontReportResultMapper;

    @Override
    public BindPakeageVO getPakeageByItemCode(String itemCode) {
        BindPakeageVO model = frontNumberMapper.getPakeageByItemCode(itemCode);
        sampleBindPrecheck(model);
        model.setFirstPic(ossUrlCleanerUtil.getSignatureUrl(model.getFirstPic()));
        return model;
    }

    @Override
    public void pakeageSave(SampleBindSaveDTO saveDTO) {
        LoginUser user = tokenService.getLoginUser();
        if(Objects.isNull(user)){
            throw new ServiceException("请先登录",204);
        }
        BindPakeageVO model = frontNumberMapper.getPakeageByItemCode(saveDTO.getItemCode());
        sampleBindPrecheck(model);
        frontNumberMapper.update(null,new LambdaUpdateWrapper<FrontNumberEntity>()
                .set(FrontNumberEntity::getSampleStatus,SampleStatusEnum.STATUS_02.getCode())
                .set(FrontNumberEntity::getProcessStatus, ProcessStatusEnum.STATUS_03.getCode())
                .set(FrontNumberEntity::getOrderUserId,user.getUserid())
                .set(FrontNumberEntity::getCheckFamilyId,saveDTO.getCheckFamilyId())
                .set(FrontNumberEntity::getCheckFamilyAge,saveDTO.getCheckFamilyAge())
                .set(FrontNumberEntity::getCheckFamilyWeight,saveDTO.getCheckFamilyWeight())
                .set(FrontNumberEntity::getCheckFamilyHeight,saveDTO.getCheckFamilyHeight())
                .set(FrontNumberEntity::getBindingTime,new Date())
                .eq(FrontNumberEntity::getItemCode,saveDTO.getItemCode()));
    }

    /**
     * 样本绑定预检查
     * @param model
     */
    private static void sampleBindPrecheck(BindPakeageVO model) {
        if(Objects.isNull(model)){
            throw new ServiceException("二维码无效",204);
        }
        if(!SampleStatusEnum.STATUS_01.getCode().equals(model.getSampleStatus())) {
            throw new ServiceException("样本已绑定，请勿重复绑定", 204);
        }
    }

    @Override
    public List<SendPakeageVO> sendList() {
        LoginUser user = tokenService.getLoginUser();
        if(Objects.isNull(user)){
            throw new ServiceException("请先登录",204);
        }
        List<SendPakeageVO> list = frontNumberMapper.sendList(user.getUserid());
        for (SendPakeageVO model : list) {
            model.setFirstPic(ossUrlCleanerUtil.getSignatureUrl(model.getFirstPic()));
        }
        return list;
    }

    @Override
    public JSONObject getAddressList() {
        LoginUser user = tokenService.getLoginUser();
        if(Objects.isNull(user)){
            throw new ServiceException("请先登录",204);
        }
        List<AddressVO> addressList = frontNumberMapper.getAddressList(user.getUserid());
        JDLConfig jdlConfig = jdlConfigProvider.getObject();//获取京东最新配置
        return new JSONObject()
                .fluentPut("addressList",addressList)
                .fluentPut("receiverContact", new JSONObject()
                        .fluentPut("name",jdlConfig.getReceiverContact_name())
                        .fluentPut("company",jdlConfig.getReceiverContact_company())
                        .fluentPut("mobile",jdlConfig.getReceiverContact_mobile())
                        .fluentPut("phone",jdlConfig.getReceiverContact_phone())
                        .fluentPut("fullAddress",jdlConfig.getReceiverContact_fullAddress())
                );
    }

    @Transactional
    @Override
    public JSONObject reshipping(JDCreateOrderDTO createOrderDTO) {
        LoginUser user = tokenService.getLoginUser();
        if(Objects.isNull(user)){
            throw new ServiceException("请先登录",204);
        }
        List<String> itemCodes = createOrderDTO.getItemCodes();
        if(CollectionUtils.isEmpty(itemCodes)){
            throw new ServiceException("请选择要回寄的样本",204);
        }
        List<FrontNumberEntity> list = frontNumberMapper.selectList(new LambdaQueryWrapper<FrontNumberEntity>()
                .in(FrontNumberEntity::getItemCode, itemCodes));
        if(CollectionUtils.isEmpty(list)){
            throw new ServiceException("无效的样本",204);
        }
        List<String> notBindList = list.stream()
                .filter(item -> SampleStatusEnum.STATUS_01.getCode().equals(item.getSampleStatus()))
                .map(FrontNumberEntity::getItemCode).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(notBindList)){
            String codes = String.join(",", notBindList);
            throw new ServiceException("【" + codes + "】样本未绑定，请先绑定再选择回寄",204);
        }

        List<String> bindList = list.stream()
                .filter(item -> SampleStatusEnum.STATUS_02.getCode().equals(item.getSampleStatus()))
                .map(FrontNumberEntity::getItemCode).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(bindList)){
            String codes = String.join(",", bindList);
            log.info("【{}】样本已绑定，开始回寄样本", codes);
        }

        List<String> reshippingList = list.stream()
                .filter(item ->
                        Objects.nonNull(item.getJdOrderId()) &&
                        (SampleStatusEnum.STATUS_03.getCode().equals(item.getSampleStatus())
                        || SampleStatusEnum.STATUS_04.getCode().equals(item.getSampleStatus())
                        || SampleStatusEnum.STATUS_05.getCode().equals(item.getSampleStatus())
                        || SampleStatusEnum.STATUS_06.getCode().equals(item.getSampleStatus())))
                .map(FrontNumberEntity::getItemCode).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(reshippingList)){
            String codes = String.join(",", reshippingList);
            throw new ServiceException("【" + codes + "】样本已回寄，请勿重复操作",204);
        }
        //回寄绑定的样品，保存订单号，更新样品状态
        frontNumberMapper.update(null,new LambdaUpdateWrapper<FrontNumberEntity>()
                .set(FrontNumberEntity::getJdOrderId, createOrderDTO.getOrderId())
                .set(FrontNumberEntity::getSampleStatus, SampleStatusEnum.STATUS_03.getCode())
                .set(FrontNumberEntity::getProcessStatus, ProcessStatusEnum.STATUS_04.getCode())
                .set(FrontNumberEntity::getShippTime,  new Date())
                .in(FrontNumberEntity::getItemCode, bindList));

        //京东下单
        R<CommonCreateOrderResponse> jdResult = jdEcapV1OrdersCreateLopService.ecapV1OrdersCreateLop(createOrderDTO);
        if(R.isSuccess(jdResult)){
            //京东下单成功
            CommonCreateOrderResponse response = jdResult.getData();
            CreateContactDTO senderContact = createOrderDTO.getSenderContact();
            CreateContactDTO receiverContact = createOrderDTO.getReceiverContact();

            //插入订单信息
            frontJdOrderMapper.insert(FrontJdOrderEntity.builder()
                    .id(createOrderDTO.getOrderId())
                    .orderCode(response.getOrderCode())
                    .waybillCode(response.getWaybillCode())
                    .pickupPromiseTime(new Date(response.getPickupPromiseTime()))
                    .deliveryPromiseTime(new Date(response.getDeliveryPromiseTime()))
                    .senderContact(JSONObject.parseObject(JSONObject.toJSONString(senderContact, SerializerFeature.WriteMapNullValue)))
                    .receiverContact(JSONObject.parseObject(JSONObject.toJSONString(receiverContact, SerializerFeature.WriteMapNullValue)))
                    .createOrderResponse(JSONObject.parseObject(JSONObject.toJSONString(response, SerializerFeature.WriteMapNullValue)))
                            .createBy(user.getUsername())
                            .createTime(new Date())
                            .createBy(user.getUsername())
                            .updateTime(new Date())
                    .build());
            return new JSONObject()
                    .fluentPut("orderCode",response.getOrderCode())
                    .fluentPut("waybillCode",response.getWaybillCode())
                    .fluentPut("pickupPromiseTime", DateFormatUtils.format(response.getPickupPromiseTime(),"yyyy-MM-dd HH:mm:ss"))
                    .fluentPut("deliveryPromiseTime",DateFormatUtils.format(response.getDeliveryPromiseTime(),"yyyy-MM-dd HH:mm:ss"))
                    .fluentPut("senderContact",senderContact)
                    .fluentPut("receiverContact",receiverContact)
                    ;
        }else {
            throw new ServiceException(jdResult.getMsg(),204);
        }
    }

    @Override
    public List<SampleStatusVO> getSampleStatusList(String searchValue) {
        LoginUser user = tokenService.getLoginUser();
        if(Objects.isNull(user)){
            throw new ServiceException("请先登录",204);
        }
        List<SampleStatusVO> list = frontNumberMapper.getSampleStatusList(user.getUserid(), searchValue);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>(0);
        }
        list.forEach(item -> {

            item.setFirstPic(ossUrlCleanerUtil.cleanUrlsToString(item.getFirstPic()));

            if(SampleStatusEnum.STATUS_01.getCode().equals(item.getSampleStatus())){
                item.setProgIndex(0);
                item.setProgName("待绑定");
            }
            if(SampleStatusEnum.STATUS_02.getCode().equals(item.getSampleStatus())){
                item.setProgIndex(1);
                item.setProgName("绑定");
            }
            if(SampleStatusEnum.STATUS_03.getCode().equals(item.getSampleStatus())){
                item.setProgIndex(2);
                item.setProgName("回寄样本");
            }
            if(SampleStatusEnum.STATUS_04.getCode().equals(item.getSampleStatus())
                    || SampleStatusEnum.STATUS_05.getCode().equals(item.getSampleStatus())){
                item.setProgIndex(3);
                item.setProgName("到货");
            }
            if(SampleStatusEnum.STATUS_06.getCode().equals(item.getSampleStatus())
                    && !ReportStatusEnum.STATUS_06.getCode().equals(item.getReportStatus())){
                item.setProgIndex(4);
                item.setProgName("检测中");
            }
            if(SampleStatusEnum.STATUS_06.getCode().equals(item.getSampleStatus())
                    && ReportStatusEnum.STATUS_06.getCode().equals(item.getReportStatus())){
                item.setProgIndex(5);
                item.setProgName("检测完成");
            }
        });
        return list;
    }

    @Override
    public JSONObject getSampleStatusByItemCode(String itemCode) {
        LoginUser user = tokenService.getLoginUser();
        if(Objects.isNull(user)){
            throw new ServiceException("请先登录",204);
        }
        SampleStatusDTO dto = frontNumberMapper.getSampleStatusByItemCode(user.getUserid(),itemCode);
        SampleStatusVO ssvo = SampleStatusVO.builder()
                .firstPic(ossUrlCleanerUtil.cleanUrlsToString(dto.getFirstPic()))
                .itemCode(dto.getItemCode())
                .pakeageName(dto.getPakeageName())
                .subtitle(dto.getSubtitle())
                .checkFamilyName(dto.getCheckFamilyName())
                .sampleStatus(dto.getSampleStatus())
                .build();

        //京东物流状态描述
        String jdStatusDesc = "";
        if(SampleStatusEnum.STATUS_03.getCode().equals(dto.getSampleStatus())){
            R<CommonOrderStatusResponse> jdResult = jdEcapV1OrdersCreateLopService.ecapV1OrdersStatusGetLopByWaybillCode(dto.getWaybillCode());
            if(R.isSuccess(jdResult)){
                jdStatusDesc = jdResult.getData().getStatusDesc();
            }
        }

        List<JSONObject> statusList = new ArrayList<>();
        final String STATUS = "status";//状态
        final String CONTENT = "content";//描述文本
        final String TIMESTAMP = "timestamp";//时间戳
        statusList.add(new JSONObject()
                .fluentPut(STATUS, "02")
                .fluentPut(CONTENT, "绑定")
                .fluentPut(TIMESTAMP, dto.getBindingTime()));
        statusList.add(new JSONObject()
                .fluentPut(STATUS, "03")
                .fluentPut(CONTENT, "回寄样本")
                .fluentPut(TIMESTAMP, dto.getShippTime())
                .fluentPut("jdStatusDesc",jdStatusDesc)
                .fluentPut("orderCode",dto.getOrderCode())
                .fluentPut("waybillCode",dto.getWaybillCode())
                .fluentPut("pickupPromiseTime", dto.getPickupPromiseTime())
        );
        statusList.add(new JSONObject()
                .fluentPut(STATUS, "04")
                .fluentPut(CONTENT, "到货")
                .fluentPut(TIMESTAMP, dto.getReceiveTime()));
        statusList.add(new JSONObject()
                .fluentPut(STATUS, "06")
                .fluentPut(CONTENT, "检测中（样本检测，预计3天完成）")
                .fluentPut(TIMESTAMP, SampleStatusEnum.STATUS_06.getCode().equals(dto.getSampleStatus()) ? dto.getHandleTime() : null));
        statusList.add(new JSONObject()
                .fluentPut(STATUS, "0606")
                .fluentPut(CONTENT, "检测完成")
                .fluentPut(TIMESTAMP, ReportStatusEnum.STATUS_06.getCode().equals(dto.getReportStatus()) ? dto.getReportReReviewTime() : null));

        return new JSONObject().fluentPut("statusList", statusList)
                .fluentPut("sampleInfo",ssvo);
    }

    @Override
    public ReportPreviewVO reportPreview(String itemCode) {
        ReportBaseInfoVO baseInfo = frontNumberMapper.selectReportBaseInfo(itemCode);
        Assert.notNull(baseInfo,"报告不存在");

        List<ReportPactarVO> pactarList = frontTmpPactarMapper.selectPactarList(itemCode,baseInfo.getTmpId());

        List<FrontReportResultEntity> reportList = frontReportResultMapper.selectList(new LambdaQueryWrapper<FrontReportResultEntity>()
                .eq(FrontReportResultEntity::getItemCode, itemCode));

        List<ReportResultVO> reportResultList = new ArrayList<>(reportList.size());
        if(!CollectionUtils.isEmpty(reportList)){
            reportList.forEach(reportItem -> {
                ReportResultVO redto = new ReportResultVO();
                BeanUtils.copyProperties(reportItem,redto);

                List<FrontTmpPactarResultEntity> tmpPactarResultList = frontTmpPactarResultMapper.selectList(new LambdaQueryWrapper<FrontTmpPactarResultEntity>()
                        .eq(FrontTmpPactarResultEntity::getPactarId, reportItem.getPactarId()));
                //按照起始值升序排序
                tmpPactarResultList.sort(Comparator.comparing(FrontTmpPactarResultEntity::getResultRangeStartVal));
                redto.setTmpPactarResultList(tmpPactarResultList);

                reportResultList.add(redto);
            });
        }
        return ReportPreviewVO.builder()
                .itemCode(itemCode)
                .baseInfo(baseInfo)
                .pactarList(pactarList)
                .reportList(reportResultList)
                .build();
    }
}