package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontHealthAnswer;
import com.ruoyi.uni.uni.service.IFrontHealthAnswerService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 健康测试管理用户回答Controller
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/answer")
@Tag(name = "健康检测问卷用户回答", description = "健康检测问卷用户回答")
public class FrontHealthAnswerController extends BaseController
{
    @Autowired
    private IFrontHealthAnswerService frontHealthAnswerService;

    /**
     * 查询健康测试管理用户回答列表
     */
    @RequiresPermissions("system:answer:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontHealthAnswer frontHealthAnswer)
    {
        startPage();
        List<FrontHealthAnswer> list = frontHealthAnswerService.selectFrontHealthAnswerList(frontHealthAnswer);
        return getDataTable(list);
    }

    /**
     * 导出健康测试管理用户回答列表
     */
    @RequiresPermissions("system:answer:export")
    @Log(title = "健康测试管理用户回答", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontHealthAnswer frontHealthAnswer)
    {
        List<FrontHealthAnswer> list = frontHealthAnswerService.selectFrontHealthAnswerList(frontHealthAnswer);
        ExcelUtil<FrontHealthAnswer> util = new ExcelUtil<FrontHealthAnswer>(FrontHealthAnswer.class);
        util.exportExcel(response, list, "健康测试管理用户回答数据");
    }

    /**
     * 获取健康测试管理用户回答详细信息
     */
    @RequiresPermissions("system:answer:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontHealthAnswerService.selectFrontHealthAnswerById(id));
    }

    /**
     * 新增健康测试管理用户回答
     */
    @Log(title = "健康测试管理用户回答", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontHealthAnswer frontHealthAnswer)
    {
        return toAjax(frontHealthAnswerService.insertFrontHealthAnswer(frontHealthAnswer));
    }

    /**
     * 修改健康测试管理用户回答
     */
    @RequiresPermissions("system:answer:edit")
    @Log(title = "健康测试管理用户回答", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontHealthAnswer frontHealthAnswer)
    {
        return toAjax(frontHealthAnswerService.updateFrontHealthAnswer(frontHealthAnswer));
    }

    /**
     * 删除健康测试管理用户回答
     */
    @RequiresPermissions("system:answer:remove")
    @Log(title = "健康测试管理用户回答", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontHealthAnswerService.deleteFrontHealthAnswerByIds(ids));
    }
}
