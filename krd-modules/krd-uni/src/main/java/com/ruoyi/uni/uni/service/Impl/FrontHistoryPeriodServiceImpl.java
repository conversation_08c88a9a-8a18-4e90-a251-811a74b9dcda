package com.ruoyi.uni.uni.service.Impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.uni.uni.domain.FrontHistoryPeriod;
import com.ruoyi.uni.uni.mapper.FrontHistoryPeriodMapper;
import com.ruoyi.uni.uni.service.IFrontHistoryPeriodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 用户历史经期记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
public class FrontHistoryPeriodServiceImpl implements IFrontHistoryPeriodService
{
    @Autowired
    private FrontHistoryPeriodMapper frontHistoryPeriodMapper;

    /**
     * 查询用户历史经期记录
     *
     * @param id 用户历史经期记录主键
     * @return 用户历史经期记录
     */
    @Override
    public FrontHistoryPeriod selectFrontHistoryPeriodById(Long id)
    {
        return frontHistoryPeriodMapper.selectFrontHistoryPeriodById(id);
    }

    /**
     * 查询用户历史经期记录列表
     *
     * @param frontHistoryPeriod 用户历史经期记录
     * @return 用户历史经期记录
     */
    @Override
    public List<FrontHistoryPeriod> selectFrontHistoryPeriodList(FrontHistoryPeriod frontHistoryPeriod)
    {
        return frontHistoryPeriodMapper.selectFrontHistoryPeriodList(frontHistoryPeriod);
    }

    /**
     * 新增用户历史经期记录
     *
     * @param frontHistoryPeriod 用户历史经期记录
     * @return 结果
     */
    @Override
    public int insertFrontHistoryPeriod(FrontHistoryPeriod frontHistoryPeriod)
    {
        frontHistoryPeriod.setCreateTime(DateUtils.getNowDate());
        return frontHistoryPeriodMapper.insertFrontHistoryPeriod(frontHistoryPeriod);
    }

    /**
     * 修改用户历史经期记录
     *
     * @param frontHistoryPeriod 用户历史经期记录
     * @return 结果
     */
    @Override
    public int updateFrontHistoryPeriod(FrontHistoryPeriod frontHistoryPeriod)
    {
        return frontHistoryPeriodMapper.updateFrontHistoryPeriod(frontHistoryPeriod);
    }

    /**
     * 批量删除用户历史经期记录
     *
     * @param ids 需要删除的用户历史经期记录主键
     * @return 结果
     */
    @Override
    public int deleteFrontHistoryPeriodByIds(Long[] ids)
    {
        return frontHistoryPeriodMapper.deleteFrontHistoryPeriodByIds(ids);
    }

    /**
     * 删除用户历史经期记录信息
     *
     * @param id 用户历史经期记录主键
     * @return 结果
     */
    @Override
    public int deleteFrontHistoryPeriodById(Long id)
    {
        return frontHistoryPeriodMapper.deleteFrontHistoryPeriodById(id);
    }
}
