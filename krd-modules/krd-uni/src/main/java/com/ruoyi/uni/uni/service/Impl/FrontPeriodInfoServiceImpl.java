package com.ruoyi.uni.uni.service.Impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.uni.uni.domain.FrontHistoryPeriod;
import com.ruoyi.uni.uni.domain.FrontPeriodInfo;
import com.ruoyi.uni.uni.mapper.FrontHistoryPeriodMapper;
import com.ruoyi.uni.uni.mapper.FrontPeriodInfoMapper;
import com.ruoyi.uni.uni.service.IFrontPeriodInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * 经期记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Service
public class FrontPeriodInfoServiceImpl implements IFrontPeriodInfoService {
    @Autowired
    private FrontPeriodInfoMapper frontPeriodInfoMapper;

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Autowired
    private FrontUserMapper userMapper;

    @Autowired
    private FrontHistoryPeriodMapper frontHistoryPeriodMapper;

    /**
     * 查询经期记录
     *
     * @param id 经期记录主键
     * @return 经期记录
     */
    @Override
    public FrontPeriodInfo selectFrontPeriodInfoById(Long id) {
        return frontPeriodInfoMapper.selectFrontPeriodInfoById(id);
    }

    /**
     * 查询经期记录列表
     *
     * @param frontPeriodInfo 经期记录
     * @return 经期记录
     */
    @Override
    public List<FrontPeriodInfo> selectFrontPeriodInfoList(FrontPeriodInfo frontPeriodInfo) {
        return frontPeriodInfoMapper.selectFrontPeriodInfoList(frontPeriodInfo);
    }


    @Override
    public int updateFrontPeriodInfo(FrontUser frontUser) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        //修改了经期 要添加用户历史经期记录
        // todo 不用管这里用户的经期时间是否小于大于第一次日期 不用管 只要有就添加
        FrontHistoryPeriod frontHistoryPeriod = new FrontHistoryPeriod();
        frontHistoryPeriod.setUid(userId);
        frontHistoryPeriod.setPeriodLength(frontUser.getPeriodLength());
        frontHistoryPeriod.setPeriodCycle(frontUser.getPeriodCycle());
        frontHistoryPeriod.setNearPeriodDate(frontUser.getNearPeriodDate());
        frontHistoryPeriod.setCreateTime(new Date());
        frontHistoryPeriodMapper.insert(frontHistoryPeriod);
        return updateUserBasicInfo(frontUser , userId);
    }



    private int updateUserBasicInfo(FrontUser frontUser , Long userId) {
        return frontUserMapper.update(null ,
                new LambdaUpdateWrapper<FrontUser>()
                        .eq(FrontUser::getId , userId)
                        .set(frontUser.getPeriodCycle() != null , FrontUser::getPeriodCycle , frontUser.getPeriodCycle())
                        .set(frontUser.getPeriodLength() != null , FrontUser::getPeriodLength , frontUser.getPeriodLength())
                        .set(frontUser.getNearPeriodDate() != null , FrontUser::getNearPeriodDate , frontUser.getNearPeriodDate())
                        .set("1".equals(frontUser.getHealthType()) , FrontUser::getIsGoPeriod , frontUser.getIsGoPeriod()));
    }


    /**
     * 新增经期记录 后端换算周期
     *
     * @return 结果
     */
    @Override
    public FrontPeriodInfo selectFrontPeriodInfoByMonth(String inputMonth) throws JsonProcessingException {
        FrontPeriodInfo frontPeriodInfo = new FrontPeriodInfo();
        Long userId = SecurityUtils.getUserId();
        frontPeriodInfo.setUserId(userId);

        FrontUser frontUser = userMapper.selectFrontUserById(userId);
        if (frontUser == null) {
            throw new IllegalArgumentException("用户信息未找到");
        }
        // 获取周期长度和经期天数
        Long periodCycle = null;
        Long periodLength = null;
        Date nearPeriodDate = null;
        //--------------根据用户的历史经期记录生成经期record--------------
        generateHistoryPeriod(userId);
        //从当前用户历史经期记录中获取周期长度和经期天数 如果在之前就取历史最近的那一次记录
        FrontHistoryPeriod frontHistoryPeriod = frontHistoryPeriodMapper.selectFrontHistoryPeriodByMonth(inputMonth , userId);
        if (frontHistoryPeriod != null) {
            periodLength = frontHistoryPeriod.getPeriodLength();
            periodCycle = frontHistoryPeriod.getPeriodCycle();
            nearPeriodDate = frontHistoryPeriod.getNearPeriodDate();
        } else {
            periodCycle = frontUser.getPeriodCycle();
            periodLength = frontUser.getPeriodLength();
            nearPeriodDate = frontUser.getNearPeriodDate();
        }
        LocalDate localNearPeriodDate = nearPeriodDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        // 目标月份
        YearMonth targetYM = YearMonth.parse(inputMonth , DateTimeFormatter.ofPattern("yyyy-MM"));
        // 构建周期链：从最近一次月经开始，逐步生成每个月的月经开始日，直到目标月份
        LocalDate currentPeriodStart = localNearPeriodDate;
        YearMonth currentYM = YearMonth.from(currentPeriodStart);
        while (currentYM.isBefore(targetYM)) {
            // 下一个月的月经开始日 = 本月月经开始日 + 周期长度
            currentPeriodStart = currentPeriodStart.plusDays(periodCycle);
            currentYM = YearMonth.from(currentPeriodStart);
        }
        // 现在 currentPeriodStart 是目标月份的月经开始日
        LocalDate periodStartInTargetMonth = currentPeriodStart;
        // 如果当前周期的开始月不是目标月，说明没有找到对应周期（比如用户输入了太远的月份）
        if (!YearMonth.from(periodStartInTargetMonth).equals(targetYM)) {
            throw new IllegalArgumentException("无法计算该月份的周期信息");
        }
        // 设置基础信息
        frontPeriodInfo.setCycleDays(periodCycle);
        frontPeriodInfo.setMenstrualPeriodDays(periodLength);

        // 1. 计算目标月份的经期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-M-d");

        List<String> periodDays = new ArrayList<>();
        for (int i = 0; i < periodLength; i++) {
            LocalDate day = periodStartInTargetMonth.plusDays(i);
            periodDays.add(day.format(formatter));
        }
        frontPeriodInfo.setPeriod(new ObjectMapper().writeValueAsString(periodDays));

        // 2. 排卵日 = 下一次月经开始日 - 14 天
        LocalDate nextPeriodStart = periodStartInTargetMonth.plusDays(periodCycle);
        LocalDate ovulationDay = nextPeriodStart.minusDays(14);
        frontPeriodInfo.setOvulaDay(ovulationDay.format(formatter));

        // 3. 计算排卵期 = 排卵日前5天 至 后4天
        List<String> ovulationPeriod = new ArrayList<>();
        for (int i = -5; i <= 4; i++) {
            LocalDate day = ovulationDay.plusDays(i);
            if (YearMonth.from(day).equals(targetYM)) {
                ovulationPeriod.add(day.format(formatter));
            }
        }
        frontPeriodInfo.setOvulation(new ObjectMapper().writeValueAsString(ovulationPeriod));

        // 4. 预测下次月经范围 = 本次月经 + 周期长度 开始，持续经期长度天
        LocalDate earliestNextPeriod = periodStartInTargetMonth.plusDays(periodCycle);
        LocalDate latestNextPeriod = earliestNextPeriod.plusDays(periodLength);

        List<String> predPeriodDays = new ArrayList<>();
        LocalDate currentDate = earliestNextPeriod;
        while (!currentDate.isAfter(latestNextPeriod)) {
            predPeriodDays.add(currentDate.format(formatter));
            currentDate = currentDate.plusDays(1);
        }
        frontPeriodInfo.setPredPeriod(new ObjectMapper().writeValueAsString(predPeriodDays));

        return frontPeriodInfo;
    }


    /**
     * 生成用户每个月的历史经期记录（基于初始经期日期 + 固定周期类推）
     */
    public List<FrontPeriodInfo> generateHistoryPeriod(Long userId) {
        // 1. 获取用户首次记录
        FrontHistoryPeriod firstRecord = frontHistoryPeriodMapper.selectInitInfo(userId);
        if (firstRecord == null) {
            throw new RuntimeException("用户未录入任何经期记录");
        }

        Date startDate = firstRecord.getNearPeriodDate();  // 初始经期开始日期
        Long cycleDays = firstRecord.getPeriodCycle();     // 周期天数，如 28 天
        Long menstrualDays = firstRecord.getPeriodLength(); // 经期持续天数，如 5 天
        if (startDate == null || cycleDays == null || menstrualDays == null) {
            return Collections.emptyList();

        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        List<FrontPeriodInfo> historyList = new ArrayList<>();

        // 获取当前系统时间
        Calendar now = Calendar.getInstance();
        // 计算需要回溯多少个月
        int monthsToGenerate = calculateMonthsBetween(calendar.getTime() , now.getTime());
        calendar.setTime(startDate);
        Date prevStartDate = null;

        for (int i = 0; i < monthsToGenerate; i++) {
            FrontPeriodInfo vo = new FrontPeriodInfo();

            Date currentStartDate = calendar.getTime();
            vo.setPeriodStart(currentStartDate);

            // 设置结束日期：开始日期 + 经期天数 - 1
            Calendar endCal = (Calendar) calendar.clone();
            endCal.add(Calendar.DAY_OF_MONTH , menstrualDays.intValue() - 1);
            vo.setPeriodEnd(endCal.getTime());

            // 设置经期天数和周期天数
            vo.setMenstrualPeriodDays(menstrualDays);
            vo.setCycleDays(cycleDays);

            // 如果不是第一个周期，计算与上一周期的间隔差异
            if (prevStartDate != null) {
                long daysDiff = daysBetween(prevStartDate , currentStartDate);
                vo.setCompareTime(daysDiff - cycleDays);
            } else {
                vo.setCompareTime(0L);
            }
            historyList.add(vo);
            prevStartDate = currentStartDate;
            calendar.add(Calendar.DAY_OF_MONTH , cycleDays.intValue());
        }

        return historyList;
    }


    /**
     * 计算两个日期之间的天数差
     */
    private long daysBetween(Date start , Date end) {
        return (end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000);
    }

    // 辅助方法：计算两个日期之间相差几个月
    private int calculateMonthsBetween(Date startDate , Date endDate) {
        Calendar start = Calendar.getInstance();
        start.setTime(startDate);
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);
        int diffYear = end.get(Calendar.YEAR) - start.get(Calendar.YEAR);
        int diffMonth = end.get(Calendar.MONTH) - start.get(Calendar.MONTH);
        return diffYear * 12 + diffMonth + 1; // 包括当月
    }

}
