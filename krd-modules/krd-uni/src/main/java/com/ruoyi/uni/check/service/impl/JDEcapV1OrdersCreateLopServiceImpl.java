package com.ruoyi.uni.check.service.impl;

import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.C2BAddedSettleTypeInfo;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonCargoInfo;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonCreateOrderRequest;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonCreateOrderResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonGoodsInfo;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonProductInfo;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.Response;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderStatusV1.CommonOrderStatusRequest;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderStatusV1.CommonOrderStatusResponse;
import com.lop.open.api.sdk.request.ECAP.EcapV1OrdersCreateLopRequest;
import com.lop.open.api.sdk.request.ECAP.EcapV1OrdersStatusGetLopRequest;
import com.lop.open.api.sdk.response.ECAP.EcapV1OrdersCreateLopResponse;
import com.lop.open.api.sdk.response.ECAP.EcapV1OrdersStatusGetLopResponse;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.uni.check.config.JDLConfig;
import com.ruoyi.uni.check.domain.dto.JDCreateOrderDTO;
import com.ruoyi.uni.check.service.JDEcapV1OrdersCreateLopService;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 下单接口服务
 * <AUTHOR>
 * @date 2025/6/5 15:36
 */
@Service
public class JDEcapV1OrdersCreateLopServiceImpl implements JDEcapV1OrdersCreateLopService {

    @Resource
    private ObjectProvider<JDLConfig> jdlConfigProvider;

    @Override
    public R<CommonCreateOrderResponse> ecapV1OrdersCreateLop(JDCreateOrderDTO createOrderDTO) {
        try {
            JDLConfig jdlConfig = jdlConfigProvider.getObject();
            //收件人信息
//            Contact receiverContact = new Contact();
//            receiverContact.setFullAddress(jdlConfig.getReceiverContact_fullAddress());
//            receiverContact.setName(jdlConfig.getReceiverContact_name());
//            receiverContact.setMobile(jdlConfig.getReceiverContact_mobile());

            //产品信息
            CommonProductInfo productInfo = new CommonProductInfo();
            productInfo.setProductCode(jdlConfig.getProductsReq_productCode());
            //入参对象
            EcapV1OrdersCreateLopRequest request = new EcapV1OrdersCreateLopRequest();
            request.addLopPlugin(jdlConfig.getLopPlugin());
            //公共下单参数
            CommonCreateOrderRequest requestDTO = new CommonCreateOrderRequest();
            requestDTO.setOrderOrigin(jdlConfig.getOrderOrigin());
            requestDTO.setCustomerCode(jdlConfig.getCustomerCode());
            requestDTO.setProductsReq(productInfo);//产品信息
            requestDTO.setSenderContact(createOrderDTO.getSenderContact());//发货人信息
            requestDTO.setReceiverContact(createOrderDTO.getReceiverContact());//收件人信息

            requestDTO.setOrderId(createOrderDTO.getOrderId());

            requestDTO.setSettleType(jdlConfig.getSettleType());

            //货物信息
            CommonCargoInfo cargo = new CommonCargoInfo();
            cargo.setName(jdlConfig.getCargoes_0_name());
            cargo.setQuantity(jdlConfig.getCargoes_0_quantity());
            cargo.setWeight(jdlConfig.getCargoes_0_weight());
            cargo.setVolume(jdlConfig.getCargoes_0_volume());
            requestDTO.setCargoes(Collections.singletonList(cargo));

            CommonGoodsInfo good = new CommonGoodsInfo();
            good.setName(jdlConfig.getGoods_0_name());
            good.setQuantity(jdlConfig.getGoods_0_quantity());
            requestDTO.setGoods(Collections.singletonList(good));

            //C2B详细费用收费方式
            C2BAddedSettleTypeInfo c2bInfo = new C2BAddedSettleTypeInfo();
            c2bInfo.setBasicFreigthSettleType(jdlConfig.getC2bAddedSettleTypeInfo_basicFreigthSettleType());
            c2bInfo.setPackageServiceSettleType(jdlConfig.getC2bAddedSettleTypeInfo_packageServiceSettleType());
            c2bInfo.setGuaranteeMoneyServiceSettleType(jdlConfig.getC2bAddedSettleTypeInfo_guaranteeMoneyServiceSettleType());
            requestDTO.setC2bAddedSettleTypeInfo(c2bInfo);

            //揽件时间段
            requestDTO.setPickupStartTime(createOrderDTO.getPickupStartTime());
            requestDTO.setPickupEndTime(createOrderDTO.getPickupEndTime());
            requestDTO.setRemark(jdlConfig.getRemark());

            request.setRequest(requestDTO);
            EcapV1OrdersCreateLopResponse response = jdlConfig.getClient().execute(request);
            String code = response.getCode();
            if ("0".equals(code)){//成功
                Response<CommonCreateOrderResponse> result = response.getResult();
                if(result.getSuccess()){
                    return R.ok(result.getData());
                }
                return R.fail(result.getMsg() + "(" + result.getSubMsg() + ")");
            }
            return R.fail(response.getMsg());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @Override
    public R<CommonOrderStatusResponse> ecapV1OrdersStatusGetLopByWaybillCode(String waybillCode) {
        try {
            JDLConfig jdlConfig = jdlConfigProvider.getObject();
            CommonOrderStatusRequest requestDTO = new CommonOrderStatusRequest();
            requestDTO.setWaybillCode(waybillCode);
            requestDTO.setOrderOrigin(jdlConfig.getOrderOrigin());
            requestDTO.setCustomerCode(jdlConfig.getCustomerCode());

            EcapV1OrdersStatusGetLopRequest request = new EcapV1OrdersStatusGetLopRequest();
            request.addLopPlugin(jdlConfig.getLopPlugin());
            request.setRequest(requestDTO);

            EcapV1OrdersStatusGetLopResponse response = jdlConfig.getClient().execute(request);
            String code = response.getCode();
            if ("0".equals(code)){//成功
                com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderStatusV1.Response<CommonOrderStatusResponse> result = response.getResult();
                if(result.getSuccess()){
                    return R.ok(result.getData());
                }
                return R.fail(result.getMsg());
            }
            return R.fail(response.getMsg());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @Override
    public R<CommonOrderStatusResponse> ecapV1OrdersStatusGetLopByOrderCode(String orderCode) {
        try {
            JDLConfig jdlConfig = jdlConfigProvider.getObject();
            CommonOrderStatusRequest requestDTO = new CommonOrderStatusRequest();
            requestDTO.setOrderCode(orderCode);
            requestDTO.setOrderOrigin(jdlConfig.getOrderOrigin());
            requestDTO.setCustomerCode(jdlConfig.getCustomerCode());

            EcapV1OrdersStatusGetLopRequest request = new EcapV1OrdersStatusGetLopRequest();
            request.addLopPlugin(jdlConfig.getLopPlugin());
            request.setRequest(requestDTO);

            EcapV1OrdersStatusGetLopResponse response = jdlConfig.getClient().execute(request);
            String code = response.getCode();
            if ("0".equals(code)){//成功
                com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderStatusV1.Response<CommonOrderStatusResponse> result = response.getResult();
                if(result.getSuccess()){
                    return R.ok(result.getData());
                }
                return R.fail(result.getMsg());
            }
            return R.fail(response.getMsg());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
