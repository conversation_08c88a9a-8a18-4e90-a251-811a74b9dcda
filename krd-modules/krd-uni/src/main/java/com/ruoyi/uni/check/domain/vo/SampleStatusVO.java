package com.ruoyi.uni.check.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 样品状态VO
 * <AUTHOR>
 * @date 2025/6/4 11:03
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SampleStatusVO {

    /**
     * 套餐图片
     */
    private String firstPic;

    /**
     * 项目编码
     */
    private String itemCode;

    /**
     * 套餐名称
     */
    private String pakeageName;

    /**
     * 副标题
     */
    private String subtitle;

    /**
     * 检测人姓名
     */
    private String checkFamilyName;

    /**
     * 样品状态
     */
    private String sampleStatus;

    /**
     * 报告状态
     */
    private String reportStatus;

    /**
     * 当前进度
     */
    private int progIndex=0;
    /**
     * 进度名称
     */
    private String progName;
    /**
     * 总进度
     */
    private int progTotal=5;

}
