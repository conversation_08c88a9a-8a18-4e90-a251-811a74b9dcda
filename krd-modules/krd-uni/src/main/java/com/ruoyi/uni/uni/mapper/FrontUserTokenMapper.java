package com.ruoyi.uni.uni.mapper;


import com.ruoyi.uni.uni.domain.user.FrontUserToken;
import org.apache.ibatis.annotations.Mapper;

/**
 * @ClassName FrontUserTokenMapper
 * @Description 用户关联唯一标识 持久层
 * <AUTHOR>
 * @Date 2025/5/15 下午5:50
 */
@Mapper
public interface FrontUserTokenMapper {

    /**
     * 根据用户id查询
     * @param userId 用户id
     * @return
     */
    FrontUserToken selectByUserId(Long userId);

    /**
     * 根据openId查询
     * @param openId openid
     * @return
     */
    FrontUserToken selectOpenIdOne(String openId);

    /**
     * 新增
     * @param frontUserToken
     * @return
     */
    int insertFrontUserToken(FrontUserToken frontUserToken);
}
