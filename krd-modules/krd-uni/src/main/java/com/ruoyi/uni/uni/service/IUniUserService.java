package com.ruoyi.uni.uni.service;


import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.vo.UniUserVo;

import java.util.List;

/**
 * @ClassName UserUniService
 * @Description 用户相关操作 业务接口
 * <AUTHOR>
 * @Date 2025/5/19 上午10:20
 */
public interface IUniUserService {

    /**
     * 获取用户id
     * @return
     */
    Long getUserId();

    /**
     * 获取用户详情
     * @return
     */
    FrontUser getUserInfo();

    /**
     * 修改用户信息
     * @param frontUser
     * @return
     */
    int updateUser(FrontUser frontUser);

    /**
     * 获取用户余额详情
     * @param date 年月
     * @return list
     */
    List<UniUserVo.UserMoneyDetail> listMoneyDetail(String date);

    /**
     * 获取用户收入支出详情
     * @param date 年月
     * @return
     */
    UniUserVo.UserIncomeExpenditure listIncomeExpenditure(String date);

    /**
     * 获取用户积分详情
     * @return
     */
    List<UniUserVo.UserIntegralDetail> listIntegralDetail();

    /**
     * 获取用户礼品卡列表
     * @param type 1:可用 2:不可用
     * @return
     */
    List<UniUserVo.UserGiftCardList> listGiftCardList(Integer type);

    /**
     * 获取用户礼品卡使用记录
     * @param id 用户对应礼品卡ID
     * @Param date 年月
     * @return
     */
    List<UniUserVo.UserGiftCardUseRecord> listGiftCardUseRecord(Integer id, String date);


    /**
     * 获取用户礼品卡购买列表
     * @return
     */
    List<UniUserVo.UserGiftCardBuyList> listGiftCardBuyList();

    /**
     * 获取用户优惠券列表
     * @param type 3:待领取 0:可使用 1:已使用 2:已失效
     * @return
     */
    List<UniUserVo.UserCouponList> listCouponList(Integer type);

    /**
     * 用户领取优惠券
     * @param vo 优惠券
     * @return
     */
    int receiveCoupon(UniUserVo.UserCouponList vo);
}
