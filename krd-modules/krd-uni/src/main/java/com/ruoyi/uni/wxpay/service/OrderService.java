package com.ruoyi.uni.wxpay.service;

import com.ruoyi.uni.wxpay.request.OrderPayRequest;
import com.ruoyi.uni.wxpay.request.UserGiftRequest;
import com.ruoyi.uni.wxpay.request.UserRechargeRequest;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description:
 */
public interface OrderService {

    /**
     * 订单支付
     * @param orderPayRequest 支付参数
     * @param ip    ip
     * @return OrderPayResultResponse
     */
    OrderPayResultResponse payment(OrderPayRequest orderPayRequest, String ip);


    /**
     * 充值
     * @return UserSpreadOrderResponse;
     */
    OrderPayResultResponse recharge(UserRechargeRequest request);

    /**
     * 礼品卡购买
     * @return UserSpreadOrderResponse;
     */
    OrderPayResultResponse gift(UserGiftRequest request);
}
