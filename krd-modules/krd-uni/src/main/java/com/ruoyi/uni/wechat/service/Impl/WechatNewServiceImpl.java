package com.ruoyi.uni.wechat.service.Impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.system.api.domain.WechatExceptions;
import com.ruoyi.system.api.domain.WechatPayInfo;
import com.ruoyi.system.api.utils.CommonUtil;
import com.ruoyi.system.api.utils.XmlUtil;
import com.ruoyi.system.api.vo.CreateOrderRequestVo;
import com.ruoyi.system.api.vo.CreateOrderResponseVo;
import com.ruoyi.uni.uni.domain.user.PhoneInfo;
import com.ruoyi.uni.uni.domain.vo.WeChatMiniAuthorizeVo;
import com.ruoyi.uni.wechat.service.WechatExceptionsService;
import com.ruoyi.uni.wechat.service.WechatNewService;
import com.ruoyi.uni.wechat.service.WechatPayInfoService;
import com.ruoyi.uni.wechat.util.RestTemplateUtil;
import com.ruoyi.uni.wxpay.pojo.WeChatOauthToken;
import com.ruoyi.uni.wxpay.vo.TemplateMessageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description: 微信公用服务实现类
 */

@Service
@RequiredArgsConstructor
public class WechatNewServiceImpl implements WechatNewService {

    private final RestTemplateUtil restTemplateUtil;

    private final WechatExceptionsService wechatExceptionsService;

    private final WechatPayInfoService wechatPayInfoService;

    @Override
    public PhoneInfo getUserPhone(String accessToken , String code) {
        return null;
    }

    @Override
    public String getPublicAccessToken() {
        return "";
    }

    @Override
    public String getMiniAccessToken() {
        return "";
    }

    @Override
    public WeChatOauthToken getOauth2AccessToken(String code) {
        return null;
    }

    @Override
    public WeChatMiniAuthorizeVo miniAuthCode(String code) {
        return null;
    }

    @Override
    public String createQrCode(String page , String scene) {
        return "";
    }



    @Override
    public CreateOrderResponseVo unifiedOrder(CreateOrderRequestVo vo) {
        try {
            String url = PayConstants.WX_PAY_API_URL + PayConstants.WX_PAY_API_URI;
            String request = XmlUtil.objectToXml(vo);
            String xml = restTemplateUtil.postXml(url, request);
            HashMap<String, Object> map = XmlUtil.xmlToMap(xml);
            if (null == map) {
                throw new GlobalException("微信下单失败！");
            }

            // 保存微信预下单
            WechatPayInfo wechatPayInfo = createWechatPayInfo(vo);

            CreateOrderResponseVo responseVo = CommonUtil.mapToObj(map, CreateOrderResponseVo.class);
            if ("FAIL".equals(responseVo.getReturnCode().toUpperCase())) {
                throw new GlobalException("微信下单失败1！" +  responseVo.getReturnMsg());
            }

            if ("FAIL".equals(responseVo.getResultCode().toUpperCase())) {
                throw new GlobalException("微信下单失败2！" + responseVo.getErrCodeDes());
            }
            wechatPayInfoService.save(wechatPayInfo);
            responseVo.setExtra(vo.getScene_info());
            return responseVo;
        } catch (Exception e) {
            e.printStackTrace();
            throw new GlobalException(e.getMessage());
        }
    }

    @Override
    public Boolean sendMiniSubscribeMessage(TemplateMessageVo templateMessage) {
        return null;
    }

    @Override
    public JSONObject getPublicCustomMenu() {
        return null;
    }

    @Override
    public Boolean createPublicCustomMenu(String data) {
        return null;
    }

    @Override
    public Boolean deletePublicCustomMenu() {
        return null;
    }

    @Override
    public String qyapiAddMaterialUrl(String type) {
        return "";
    }

    @Override
    public Boolean delPublicMyTemplate(String templateId) {
        return null;
    }

    @Override
    public String apiAddPublicTemplate(String templateIdShort) {
        return "";
    }

    @Override
    public Boolean delRoutineMyTemplate(String priTmplId) {
        return null;
    }

    @Override
    public String apiAddRoutineTemplate(String tempKey , List<Integer> kidList) {
        return "";
    }

    /**
     * 生成微信订单表对象
     * @param vo 预下单数据
     * @return WechatPayInfo
     */
    private WechatPayInfo createWechatPayInfo(CreateOrderRequestVo vo) {
        WechatPayInfo payInfo = new WechatPayInfo();
        payInfo.setAppId(vo.getAppid());
        payInfo.setMchId(vo.getMch_id());
        payInfo.setDeviceInfo(vo.getDevice_info());
        payInfo.setOpenId(vo.getOpenid());
        payInfo.setNonceStr(vo.getNonce_str());
        payInfo.setSign(vo.getSign());
        payInfo.setSignType(vo.getSign_type());
        payInfo.setBody(vo.getBody());
        payInfo.setDetail(vo.getDetail());
        payInfo.setAttach(vo.getAttach());
        payInfo.setOutTradeNo(vo.getOut_trade_no());
        payInfo.setFeeType(vo.getFee_type());
        payInfo.setTotalFee(vo.getTotal_fee());
        payInfo.setSpbillCreateIp(vo.getSpbill_create_ip());
        payInfo.setTimeStart(vo.getTime_start());
        payInfo.setTimeExpire(vo.getTime_expire());
        payInfo.setNotifyUrl(vo.getNotify_url());
        payInfo.setTradeType(vo.getTrade_type());
        payInfo.setProductId(vo.getProduct_id());
        payInfo.setSceneInfo(vo.getScene_info());
        return payInfo;
    }

    /**
     * 微信支付异常处理
     * @param map 微信返回数据
     * @param remark 备注
     */
    private void wxPayExceptionDispose(HashMap<String, Object> map, String remark) {
        WechatExceptions wechatExceptions = new WechatExceptions();
        String returnCode = (String) map.get("return_code");
        if ("FAIL".equals(returnCode.toUpperCase())) {
            wechatExceptions.setErrcode("-100");
            wechatExceptions.setErrmsg(map.get("return_msg").toString());
        } else {
            wechatExceptions.setErrcode(map.get("err_code").toString());
            wechatExceptions.setErrmsg(map.get("err_code_des").toString());
        }
        wechatExceptions.setData(JSONObject.toJSONString(map));
        wechatExceptions.setRemark(remark);
        wechatExceptions.setCreateTime(DateUtil.date());
        wechatExceptions.setUpdateTime(DateUtil.date());
        wechatExceptionsService.save(wechatExceptions);
    }
}
