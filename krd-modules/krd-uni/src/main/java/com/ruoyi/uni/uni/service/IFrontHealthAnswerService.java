package com.ruoyi.uni.uni.service;


import com.ruoyi.system.api.domain.FrontHealthAnswer;

import java.util.List;

/**
 * 健康测试管理用户回答Service接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IFrontHealthAnswerService
{
    /**
     * 查询健康测试管理用户回答
     *
     * @param id 健康测试管理用户回答主键
     * @return 健康测试管理用户回答
     */
    public FrontHealthAnswer selectFrontHealthAnswerById(Long id);

    /**
     * 查询健康测试管理用户回答列表
     *
     * @param frontHealthAnswer 健康测试管理用户回答
     * @return 健康测试管理用户回答集合
     */
    public List<FrontHealthAnswer> selectFrontHealthAnswerList(FrontHealthAnswer frontHealthAnswer);

    /**
     * 新增健康测试管理用户回答
     *
     * @param frontHealthAnswer 健康测试管理用户回答
     * @return 结果
     */
    public int insertFrontHealthAnswer(FrontHealthAnswer frontHealthAnswer);

    /**
     * 修改健康测试管理用户回答
     *
     * @param frontHealthAnswer 健康测试管理用户回答
     * @return 结果
     */
    public int updateFrontHealthAnswer(FrontHealthAnswer frontHealthAnswer);

    /**
     * 批量删除健康测试管理用户回答
     *
     * @param ids 需要删除的健康测试管理用户回答主键集合
     * @return 结果
     */
    public int deleteFrontHealthAnswerByIds(Long[] ids);

    /**
     * 删除健康测试管理用户回答信息
     *
     * @param id 健康测试管理用户回答主键
     * @return 结果
     */
    public int deleteFrontHealthAnswerById(Long id);
}
