package com.ruoyi.uni.uni.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.uni.uni.domain.FrontPeriodInfo;
import com.ruoyi.uni.uni.service.IFrontPeriodInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 经期记录Controller
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@RestController
@RequestMapping("/info")
@Tag(name = "小程序经期记录接口", description = "小程序经期记录接口")
public class FrontPeriodInfoController extends BaseController
{
    @Autowired
    private IFrontPeriodInfoService frontPeriodInfoService;

    /**
     * 查询经期记录列表
     */
    @GetMapping("/list")
    @Operation(description = "查询经期记录列表")
    public AjaxResult list(FrontPeriodInfo frontPeriodInfo)
    {
        List<FrontPeriodInfo> list = frontPeriodInfoService.selectFrontPeriodInfoList(frontPeriodInfo);
        return success(list);
    }


    /**
     * 获取经期记录详细信息
     */
    @GetMapping(value = "/{id}")
    @Operation(description = "获取经期记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontPeriodInfoService.selectFrontPeriodInfoById(id));
    }

    /**
     * 小程序根据用户月份查询预测经期
     */
    @Log(title = "小程序根据用户月份查询预测经期", businessType = BusinessType.INSERT)
    @GetMapping("/queryPeriodInfo")
    @Operation(description = "小程序根据用户月份查询预测经期")
    public AjaxResult queryPeriodInfo(@RequestParam(value = "inputMonth") String inputMonth) throws JsonProcessingException {
        return success(frontPeriodInfoService.selectFrontPeriodInfoByMonth(inputMonth));
    }

    //经期记录----个人经期设置
    @Log(title = "经期记录", businessType = BusinessType.INSERT)
    @PostMapping("/editPeriodInfo")
    @Operation(description = "经期记录----个人经期设置")
    public AjaxResult editPeriodInfo(@RequestBody FrontUser frontUser)
    {
        return toAjax(frontPeriodInfoService.updateFrontPeriodInfo(frontUser));
    }


}
