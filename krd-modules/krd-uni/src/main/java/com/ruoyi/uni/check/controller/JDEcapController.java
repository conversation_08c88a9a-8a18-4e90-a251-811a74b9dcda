package com.ruoyi.uni.check.controller;

import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.CommonCheckPreCreateOrderResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.CommonPickupSliceTimeResponse;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.uni.check.domain.dto.JDCreateOrderDTO;
import com.ruoyi.uni.check.domain.dto.JDPrecheckOrderDTO;
import com.ruoyi.uni.check.service.JDEcapV1OrdersCreateLopService;
import com.ruoyi.uni.check.service.JDEcapV1OrdersPrecheckLopService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;


/**
 * 京东物流接口
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-04 10:40:54
 */
//@Slf4j
@RestController
@RequestMapping("jd/ecap")
public class JDEcapController {
    @Resource
    private JDEcapV1OrdersPrecheckLopService jdEcapV1OrdersPrecheckLopService;
    @Resource
    private JDEcapV1OrdersCreateLopService jdEcapV1OrdersCreateLopService;

    /**
     * 下单前置校验
     * @return
     */
    @PostMapping("/precheckLop")
    public R precheckLop(@RequestBody JDPrecheckOrderDTO precheckOrderDTO){
        R<CommonCheckPreCreateOrderResponse> result = jdEcapV1OrdersPrecheckLopService.ecapV1OrdersPrecheck(precheckOrderDTO);
        if(R.isSuccess(result)){
            List<CommonPickupSliceTimeResponse> pickupSliceTimes = result.getData().getPickupSliceTimes();
            pickupSliceTimes.sort(Comparator.comparing(CommonPickupSliceTimeResponse::getDateKey));
            return R.ok(pickupSliceTimes);//只返回预计揽件时间
        }
        return R.fail(result.getMsg());
    }

    /**
     * 下单接口
     * @return
     */
    @PostMapping("/createLop")
    public R createLop(@RequestBody JDCreateOrderDTO createOrderDTO){
        return jdEcapV1OrdersCreateLopService.ecapV1OrdersCreateLop(createOrderDTO);
    }

    /**
     * 订单状态查询(根据运单号查询)
     * @return
     */
    @GetMapping("/statusGetLopByWaybillCode/{waybillCode}")
    public R statusGetLopByWaybillCode(@PathVariable("waybillCode") String waybillCode){
        return jdEcapV1OrdersCreateLopService.ecapV1OrdersStatusGetLopByWaybillCode(waybillCode);
    }

    /**
     * 订单状态查询(根据订单号查询)
     * @return
     */
    @GetMapping("/statusGetLopByOrderCode/{orderCode}")
    public R statusGetLopByOrderCode(@PathVariable("orderCode") String orderCode){
        return jdEcapV1OrdersCreateLopService.ecapV1OrdersStatusGetLopByOrderCode(orderCode);
    }

}
