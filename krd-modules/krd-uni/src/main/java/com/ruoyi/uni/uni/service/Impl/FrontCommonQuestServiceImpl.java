package com.ruoyi.uni.uni.service.Impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.FrontCommonQuest;
import com.ruoyi.system.api.mapper.FrontCommonQuestMapper;
import com.ruoyi.uni.uni.service.IFrontCommonQuestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 常见问题Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class FrontCommonQuestServiceImpl implements IFrontCommonQuestService
{
    @Autowired
    private FrontCommonQuestMapper frontCommonQuestMapper;

    /**
     * 查询常见问题
     *
     * @param id 常见问题主键
     * @return 常见问题
     */
    @Override
    public FrontCommonQuest selectFrontCommonQuestById(Long id)
    {
        return frontCommonQuestMapper.selectFrontCommonQuestById(id);
    }

    /**
     * 查询常见问题列表
     *
     * @param frontCommonQuest 常见问题
     * @return 常见问题
     */
    @Override
    public List<FrontCommonQuest> selectFrontCommonQuestList(FrontCommonQuest frontCommonQuest)
    {
        return frontCommonQuestMapper.selectUniFrontCommonQuestList(frontCommonQuest);
    }

    /**
     * 新增常见问题
     *
     * @param frontCommonQuest 常见问题
     * @return 结果
     */
    @Override
    public int insertFrontCommonQuest(FrontCommonQuest frontCommonQuest)
    {
        frontCommonQuest.setCreateTime(DateUtils.getNowDate());
        return frontCommonQuestMapper.insertFrontCommonQuest(frontCommonQuest);
    }

    /**
     * 修改常见问题
     *
     * @param frontCommonQuest 常见问题
     * @return 结果
     */
    @Override
    public int updateFrontCommonQuest(FrontCommonQuest frontCommonQuest)
    {
        frontCommonQuest.setUpdateTime(DateUtils.getNowDate());
        return frontCommonQuestMapper.updateFrontCommonQuest(frontCommonQuest);
    }

    /**
     * 批量删除常见问题
     *
     * @param ids 需要删除的常见问题主键
     * @return 结果
     */
    @Override
    public int deleteFrontCommonQuestByIds(Long[] ids)
    {
        return frontCommonQuestMapper.deleteFrontCommonQuestByIds(ids);
    }

    /**
     * 删除常见问题信息
     *
     * @param id 常见问题主键
     * @return 结果
     */
    @Override
    public int deleteFrontCommonQuestById(Long id)
    {
        return frontCommonQuestMapper.deleteFrontCommonQuestById(id);
    }
}
