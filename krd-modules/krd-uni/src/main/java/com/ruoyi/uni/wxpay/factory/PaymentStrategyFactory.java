package com.ruoyi.uni.wxpay.factory;

import com.ruoyi.uni.wxpay.strategy.PaymentStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付策略工厂
 */
@Service
@RequiredArgsConstructor
public class PaymentStrategyFactory {

    private final List<PaymentStrategy> paymentStrategies;
    private final Map<String, PaymentStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (PaymentStrategy strategy : paymentStrategies) {
            strategyMap.put(strategy.getPayType(), strategy);
        }
    }

    /**
     * 获取支付策略
     * @param payType 支付类型
     * @return 支付策略
     */
    public PaymentStrategy getStrategy(String payType) {
        return strategyMap.get(payType);
    }
}
