package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.CblTableDataInfo;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.vo.UniUserVo;
import com.ruoyi.uni.uni.service.IUniUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName UserUniController
 * @Description 小程序用户相关接口控制器
 * <AUTHOR>
 * @Date 2025/5/19 上午10:17
 */
@Slf4j
@Tag(name = "用户相关操作及详情", description = "小程序用户相关接口控制器")
@RestController
@RequestMapping("/uni/user")
public class UniUserController extends BaseController {

    @Autowired
    private IUniUserService userService;

    @Operation(description = "获取用户详情消息")
    @GetMapping("/getUserInfo")
    public AjaxResult getUserInfo(){
        return success(userService.getUserInfo());
    }

    @Operation(description = "修改用户信息")
    @PostMapping("/updateUser")
    public AjaxResult updateUser(@RequestBody FrontUser frontUser){
        return toAjax(userService.updateUser(frontUser));
    }

    @Operation(description = "获取用户余额明细")
    @GetMapping("/listMoneyDetail")
    public CblTableDataInfo listMoneyDetail(@RequestParam("date") String date){
        startPage();
        return getTableUni(userService.listMoneyDetail(date));
    }

    @Operation(summary = "获取用户收入支出明细")
    @GetMapping("/listIncomeExpenditure")
    public AjaxResult listIncomeExpenditure(@RequestParam("date") String date){
        return success(userService.listIncomeExpenditure(date));
    }

    @Operation(summary = "获取用户积分明细")
    @GetMapping("/listIntegralDetail")
    public CblTableDataInfo listIntegralDetail(){
        startPage();
        return getTableUni(userService.listIntegralDetail());
    }

    @Operation(summary = "获取用户礼品卡列表")
    @GetMapping("/listGiftCardList")
    public CblTableDataInfo listGiftCardList(@RequestParam("type") Integer type){
        startPage();
        return getTableUni(userService.listGiftCardList(type));
    }

    @Operation(summary = "获取用户礼品卡使用记录")
    @GetMapping("/listGiftCardUseRecord")
    public CblTableDataInfo listGiftCardUseRecord(@RequestParam("id") Integer id, @RequestParam("date") String date){
        startPage();
        return getTableUni(userService.listGiftCardUseRecord(id,date));
    }

    @Operation(summary = "获取用户礼品卡购买列表")
    @GetMapping("/listGiftCardBuyList")
    public CblTableDataInfo listGiftCardBuyList(){
        startPage();
        return getTableUni(userService.listGiftCardBuyList());
    }

    @Operation(summary = "获取用户优惠券列表 0:可使用 1:已使用 2:已失效 3:待领取")
    @GetMapping("/listCouponList")
    public CblTableDataInfo listCouponList(@RequestParam("type") Integer type){
        startPage();
        return getTableUni(userService.listCouponList(type));
    }

    @Operation(summary = "领取优惠券")
    @PostMapping("/receiveCoupon")
    public AjaxResult receiveCoupon(@RequestBody UniUserVo.UserCouponList vo){
        return toAjax(userService.receiveCoupon(vo));
    }

}
