package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.CblTableDataInfo;
import com.ruoyi.uni.uni.domain.FrontShopCart;
import com.ruoyi.uni.uni.domain.vo.FrontShopCartVo;
import com.ruoyi.uni.uni.service.IFrontShopCartService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @ClassName FrontShopCartController
 * @Description 小程序购物车控制类
 * <AUTHOR>
 * @Date 2025/6/9 上午10:44
 */
@Slf4j
@Tag(name = "FrontShopCartController", description = "小程序购物车控制类")
@RestController
@RequestMapping("/uni/shopCart")
public class FrontShopCartController extends BaseController {

    @Autowired
    private IFrontShopCartService IFrontShopCartService;

    @Operation(summary = "查询购物车列表")
    @GetMapping("/list")
    public CblTableDataInfo list() {
        startPage();
        return getTableUni(IFrontShopCartService.selectFrontShopCartList());
    }

    @Operation(summary = "添加购物车")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FrontShopCart frontShopCart) {
        return IFrontShopCartService.add(frontShopCart) ? success() : error();
    }

    @Operation(summary = "修改购物车")
    @PostMapping("/update")
    public AjaxResult update(@RequestBody FrontShopCart frontShopCart) {
        return IFrontShopCartService.update(frontShopCart) ? success() : error();
    }

    @Operation(summary = "删除购物车")
    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable Long[] ids) {
        return IFrontShopCartService.delete(ids) ? success() : error();
    }

    @Operation(summary =  "批量添加购物车")
    @PostMapping("/addBatch")
    public AjaxResult addBatch(@RequestBody FrontShopCartVo.CartListInfo vo){
        return IFrontShopCartService.addBatch(vo) ? success() : error();
    }

    @Operation(summary = "查询购物车数量")
    @GetMapping("/count")
    public AjaxResult count(){
        return success(IFrontShopCartService.count());
    }
}
