package com.ruoyi.uni.check.domain.dto;

import com.ruoyi.uni.check.domain.vo.SampleStatusVO;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/8 23:25
 */
@Data
public class SampleStatusDTO extends SampleStatusVO {

    /**
     * 绑定时间（绑定时间）
     */
    private String bindingTime;
    /**
     * 样品回寄时间（回寄时间）
     */
    private String shippTime;
    /**
     * 到货时间（接收时间）
     */
    private String receiveTime;
    /**
     * 检测中时间（样本处理时间，必须合格，否则为空）
     */
    private String handleTime;
    /**
     * 检测完成时间（报告复核时间，必须发布，否则为空）
     */
    private String reportReReviewTime;
    /**
     * 报告状态
     * 01-未出报告
     * 02-未审核（已出报告）
     * 03-已审核-不通过
     * 04-已审核-通过
     * 05-已复核-不通过
     * 06-已复核-已发布
     */
    private String reportStatus;

    /**
     * 京东物流订单号
     */
    private String orderCode;
    /**
     * 京东物流运单号
     */
    private String waybillCode;
    /**
     * 预计揽收时间
     */
    private String pickupPromiseTime;
}
