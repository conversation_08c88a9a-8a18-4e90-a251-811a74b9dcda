package com.ruoyi.uni.uni.domain.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName FrontUserToken
 * @TableName front_user_token
 * @Description 用户关联唯一标识
 * <AUTHOR>
 * @Date 2025/5/15 下午5:46
 */
@Schema(name = "用户关联唯一标识")
@Data
public class FrontUserToken implements Serializable {

    /**
    * 主键ID
    */
    @NotBlank(message="[主键ID]不能为空")
    @Schema(name = "主键ID")
    private Long id;
    /**
    * 用户id
    */
    @NotBlank(message="[用户id]不能为空")
    @Schema(name = "用户id")
    private Long userId;
    /**
    * 微信返回用户唯一标识
    */
    @NotBlank(message="[微信返回用户唯一标识]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @Schema(name = "微信返回用户唯一标识")
    @Length(max= 255,message="编码长度不能超过255")
    private String openId;
    /**
    * 创建时间
    */
    @Schema(name = "创建时间")
    private LocalDateTime createTime;
}
