package com.ruoyi.uni.uni.domain.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName FrontFamily
 * @TableName front_family
 * @Description 家庭成员
 * <AUTHOR>
 * @Date 2025/5/19 上午12:09
 */
@Data
@Schema(description = "家庭成员")
@TableName("front_family")
public class FrontFamily implements Serializable {

    /**
    * 
    */
    @Schema(description = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 
    */
    @Schema(description = "用户id —— 家庭唯一标识")
    private Long userId;
    /**
    * 姓名
    */
    @Schema(description = "姓名")
    private String name;
    /**
    * 0-女 1-男
    */
    @Schema(description ="0-女 1-男")
    private Integer sex;
    /**
    * 年龄
    */
    @Schema(description ="年龄")
    private Integer age;
    /**
    * 出生日期
    */
    @Schema(description ="出生日期 时间戳")
    private String date;
    /**
    * 体重
    */
    @Schema(description ="体重")
    private Double weight;
    /**
    * 身高
    */
    @Schema(description ="身高")
    private Double height;
    /**
    * 0-false 1-true
    */
    @Schema(description =" 是否为本人 0-false 1-true")
    private Integer isSelf;
    /**
    * 创建时间
    */
    @Schema(description ="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
    * 更新时间
    */
    @Schema(description ="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
    * 创建人
    */
    @Schema(description ="创建人")
    private String createBy;
    /**
    * 修改人
    */
    @Schema(description ="修改人")
    private String updateBy;

}
