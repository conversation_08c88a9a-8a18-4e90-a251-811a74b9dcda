package com.ruoyi.uni.uni.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.FrontHealthAnswer;
import com.ruoyi.system.api.domain.FrontQuestion;
import com.ruoyi.system.api.mapper.FrontHealthAnswerMapper;
import com.ruoyi.system.api.mapper.FrontQuestionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 问卷管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class FrontQuestionServiceImpl implements IFrontQuestionService
{
    @Autowired
    private FrontQuestionMapper frontQuestionMapper;

    @Autowired
    private FrontHealthAnswerMapper frontHealthAnswerMapper;

    /**
     * 查询问卷管理
     *
     * @param id 问卷管理主键
     * @return 问卷管理
     */
    @Override
    public FrontQuestion selectFrontQuestionById(Long id)
    {
        return frontQuestionMapper.selectFrontQuestionById(id);
    }

    /**
     * 查询问卷管理列表
     *
     * @param frontQuestion 问卷管理
     * @return 问卷管理
     */
    @Override
    public List<FrontQuestion> selectFrontQuestionList(FrontQuestion frontQuestion)
    {
        return frontQuestionMapper.selectFrontQuestionList(frontQuestion);
    }

    @Override
    public List<FrontQuestion> selectUniQuestionList( ) {
    try {
        List<FrontQuestion> frontQuestions = frontQuestionMapper.selectUniQuestionList();
        Long userId = SecurityUtils.getUserId();
        LambdaQueryWrapper<FrontHealthAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontHealthAnswer::getUid, userId);
        queryWrapper.eq(FrontHealthAnswer::getType, 2);
        frontQuestions.forEach(frontQuestion -> {
            queryWrapper.eq(FrontHealthAnswer::getQuestId, frontQuestion.getId());
            List<FrontHealthAnswer> existAnswer = frontHealthAnswerMapper.selectList(queryWrapper);
            frontQuestion.setIsAnswer(!existAnswer.isEmpty() ? 1 : 0);
            queryWrapper.clear();
        });
        return frontQuestions;
    } catch (Exception e) {
        return null;
    }
}


    /**
     * 新增问卷管理
     *
     * @param frontQuestion 问卷管理
     * @return 结果
     */
    @Override
    public int insertFrontQuestion(FrontQuestion frontQuestion)
    {
        frontQuestion.setCreateTime(DateUtils.getNowDate());
        return frontQuestionMapper.insertFrontQuestion(frontQuestion);
    }

    /**
     * 修改问卷管理
     *
     * @param frontQuestion 问卷管理
     * @return 结果
     */
    @Override
    public int updateFrontQuestion(FrontQuestion frontQuestion)
    {
        frontQuestion.setUpdateTime(DateUtils.getNowDate());
        return frontQuestionMapper.updateFrontQuestion(frontQuestion);
    }

    /**
     * 批量删除问卷管理
     *
     * @param ids 需要删除的问卷管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontQuestionByIds(Long[] ids)
    {
        return frontQuestionMapper.deleteFrontQuestionByIds(ids);
    }

    /**
     * 删除问卷管理信息
     *
     * @param id 问卷管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontQuestionById(Long id)
    {
        return frontQuestionMapper.deleteFrontQuestionById(id);
    }

    @Override
    public Map<String, Object> getQuestionCount(Long id) {

        return Collections.emptyMap();
    }
}
