package com.ruoyi.uni.check.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.uni.check.service.FrontNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 报告控制器
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-04 10:40:54
 */
//@Slf4j
@RestController
@RequestMapping("check/report")
public class ReportController {
    @Autowired
    private FrontNumberService frontNumberService;

    /**
     * 报告预览
     * @param itemCode
     * @return
     */
    @GetMapping("/preview/{itemCode}")
    public R reportPreview(@PathVariable("itemCode") String itemCode)
    {
        return R.ok(frontNumberService.reportPreview(itemCode));
    }

}
