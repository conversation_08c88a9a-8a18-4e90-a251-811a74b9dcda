package com.ruoyi.uni.uni.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.FrontGoods;
import com.ruoyi.system.api.domain.FrontGoodsSpec;
import com.ruoyi.system.api.mapper.FrontGoodsSpecMapper;
import com.ruoyi.uni.uni.domain.FrontShopCart;
import com.ruoyi.uni.uni.domain.vo.FrontShopCartVo;
import com.ruoyi.uni.uni.mapper.FrontShopCartMapper;
import com.ruoyi.uni.uni.mapper.UniFrontGoodsMapper;
import com.ruoyi.uni.uni.service.IFrontShopCartService;
import com.ruoyi.uni.uni.service.IFrontSourceService;
import com.ruoyi.uni.uni.utils.oss.OssUrlCleanerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @ClassName IFrontShopCartServiceImpl
 * @Description 小查询购物车服务实现类
 * <AUTHOR>
 * @Date 2025/6/9 上午10:33
 */
@Slf4j
@Service
public class IFrontShopCartServiceImpl implements IFrontShopCartService {

    @Autowired
    private FrontShopCartMapper frontShopCartMapper;

    @Autowired
    private UniFrontGoodsMapper uniFrontGoodsMapper;

    @Autowired
    private FrontGoodsSpecMapper frontGoodsSpecMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    @Autowired
    private IFrontSourceService frontSourceService;

    @Override
    public List<FrontShopCartVo.CartList> selectFrontShopCartList() {
        // 获取会员折扣
        BigDecimal discount = frontSourceService.getUserMemberLevelDiscount();
        List<FrontShopCartVo.CartList> list = new ArrayList<>();
        // 获取对应用户的购物车
        List<FrontShopCart> frontShopCartList = frontShopCartMapper.selectList(new LambdaQueryWrapper<FrontShopCart>().eq(FrontShopCart::getUserId, SecurityUtils.getUserId()));
        if (frontShopCartList != null){
            frontShopCartList.forEach(frontShopCart -> {
                FrontShopCartVo.CartList cartList = new FrontShopCartVo.CartList();

                // 获取对应商品对象
                FrontGoods goods = uniFrontGoodsMapper.selectOne(new LambdaQueryWrapper<FrontGoods>().eq(FrontGoods::getId, frontShopCart.getGoodsId()));

                cartList.setId(frontShopCart.getId());
                cartList.setGoodsType(goods.getGoodsType());
                cartList.setGoodsId(frontShopCart.getGoodsId());
                cartList.setAmount(frontShopCart.getQuantity());
                cartList.setGoodsName(goods.getName());
                if (frontShopCart.getSpecId() != null){
                    FrontGoodsSpec frontGoodsSpec = frontGoodsSpecMapper.selectOne(new LambdaQueryWrapper<FrontGoodsSpec>().eq(FrontGoodsSpec::getId, frontShopCart.getSpecId()));
                    cartList.setSpecId(frontShopCart.getSpecId());
                    cartList.setSpecName(frontGoodsSpec.getTitle());
                    if (discount.compareTo(BigDecimal.ZERO) != 0){
                        cartList.setPrice(frontGoodsSpec.getPrice().multiply(discount).setScale(2, RoundingMode.HALF_UP));
                    }else {
                        cartList.setPrice(frontGoodsSpec.getPrice());
                    }
                }else {
                    if (discount.compareTo(BigDecimal.ZERO) != 0){
                        cartList.setPrice(goods.getPrice().multiply(discount).setScale(2, RoundingMode.HALF_UP));
                    }else {
                        cartList.setPrice(goods.getPrice());
                    }
                }

                cartList.setImage(ossUrlCleanerUtil.getSignatureUrl(goods.getFirstPic()));

                list.add(cartList);
            });
            return list;
        }
        return list;
    }

    @Override
    public Boolean add(FrontShopCart frontShopCart) {
        // 查询对应用户的购物车
        LambdaQueryWrapper<FrontShopCart> queryWrapper = new LambdaQueryWrapper<FrontShopCart>()
                .eq(FrontShopCart::getUserId, SecurityUtils.getUserId())
                .eq(FrontShopCart::getGoodsId, frontShopCart.getGoodsId());

        if (frontShopCart.getSpecId() != null){
            queryWrapper.eq(FrontShopCart::getSpecId, frontShopCart.getSpecId());
        }else {
            queryWrapper.isNull(FrontShopCart::getSpecId);
        }

        FrontShopCart shopCart = frontShopCartMapper.selectOne(queryWrapper);
        if (shopCart == null){
            FrontShopCart cart = new FrontShopCart();
            cart.setUserId(SecurityUtils.getUserId());
            cart.setGoodsId(frontShopCart.getGoodsId());
            cart.setSpecId(frontShopCart.getSpecId() == null ? null : frontShopCart.getSpecId());
            cart.setQuantity(frontShopCart.getQuantity());
            cart.setCreateTime(DateUtils.getLocalDateTime());
            frontShopCartMapper.insert(cart);
            return true;
        }else {
            shopCart.setQuantity(shopCart.getQuantity() + frontShopCart.getQuantity());
            return frontShopCartMapper.updateById(shopCart) > 0;
        }
    }

    @Override
    public Boolean update(FrontShopCart frontShopCart) {
        frontShopCart.setUpdateTime(DateUtils.getLocalDateTime());
        return frontShopCartMapper.updateById(frontShopCart) > 0;
    }

    @Override
    public Boolean delete(Long[] ids) {
        return frontShopCartMapper.deleteBatchIds(Arrays.asList(ids)) > 0;
    }

    @Override
    @Transactional
    public Boolean addBatch(FrontShopCartVo.CartListInfo vo) {
        if (vo.getVoList() != null){
            // 遍历需要添加到购物车到收藏数据
            vo.getVoList().forEach(item -> {
                LambdaQueryWrapper<FrontShopCart> queryWrapper = new LambdaQueryWrapper<FrontShopCart>()
                        .eq(FrontShopCart::getGoodsId, item.getGoodsId())
                        .eq(FrontShopCart::getUserId, SecurityUtils.getUserId());

                if (item.getSpecId() != null){
                    queryWrapper.eq(FrontShopCart::getSpecId, item.getSpecId());
                }else {
                    queryWrapper.isNull(FrontShopCart::getSpecId);
                }

                // 查询用户对应购物车商品
                FrontShopCart frontShopCart = frontShopCartMapper.selectOne(queryWrapper);

                // 判断是否存在
                if (frontShopCart != null){
                    frontShopCart.setQuantity(frontShopCart.getQuantity() + 1);
                    frontShopCartMapper.updateById(frontShopCart);
                }else {
                    frontShopCart = new FrontShopCart();
                    frontShopCart.setUserId(SecurityUtils.getUserId());
                    frontShopCart.setGoodsId(item.getGoodsId());
                    frontShopCart.setSpecId(item.getSpecId() == null ? null : item.getSpecId());
                    frontShopCart.setQuantity(1L);
                    frontShopCart.setCreateTime(DateUtils.getLocalDateTime());
                    frontShopCartMapper.insert(frontShopCart);
                }
            });
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Long count() {
        return frontShopCartMapper.selectCount(new LambdaQueryWrapper<FrontShopCart>().eq(FrontShopCart::getUserId, SecurityUtils.getUserId()));
    }
}
