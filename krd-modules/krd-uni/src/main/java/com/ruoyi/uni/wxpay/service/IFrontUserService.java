package com.ruoyi.uni.wxpay.service;

import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.resp.FrontUserMoneyResp;
import com.ruoyi.system.api.domain.vo.FrontEntity;

import java.util.List;

/**
 * 用户Service接口
 *
 * @date 2025-05-09
 */
public interface IFrontUserService
{
    /**
     * 查询用户
     *
     * @param id 用户主键
     * @return 用户
     */
    public FrontUser selectFrontUserById(Long id);

    /**
     * 查询用户列表
     *
     * @param frontUser 用户
     * @return 用户集合
     */
    public List<FrontEntity> selectFrontUserList(FrontEntity frontUser);

    /**
     * 新增用户
     *
     * @param frontUser 用户
     * @return 结果
     */
    public int insertFrontUser(FrontUser frontUser);

    /**
     * 修改用户
     *
     * @param frontUser 用户
     * @return 结果
     */
    public int updateFrontUser(FrontUser frontUser);

    /**
     * 批量删除用户
     *
     * @param ids 需要删除的用户主键集合
     * @return 结果
     */
    public int deleteFrontUserByIds(Long[] ids);

    /**
     * 删除用户信息
     *
     * @param id 用户主键
     * @return 结果
     */
    public int deleteFrontUserById(Long id);

    /**
     *  查询钱包明细
     * @param type 0-余额 1-礼品卡 2-优惠券 3-积分
     * @return List
     */
    List<FrontUserMoneyResp> listMoneyPackageInfo(String type, Long userId);


    /**
     * 查询用户
     *
     * @param id 用户主键
     * @return 用户
     */
     FrontUser queryFrontUserById(Long id);
}
