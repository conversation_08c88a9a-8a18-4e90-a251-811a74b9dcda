package com.ruoyi.uni.uni.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.uni.uni.domain.family.FrontFamily;
import com.ruoyi.uni.uni.mapper.UniFrontFamilyMapper;
import com.ruoyi.uni.uni.service.IUniFrontFamilyService;
import com.ruoyi.uni.uni.service.IUniUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName UniFamilyServiceImpl
 * @Description 用户家庭管理业务实现类
 * <AUTHOR>
 * @Date 2025/5/19 下午2:01
 */
@Slf4j
@Service
public class UniFamilyServiceImpl extends ServiceImpl<UniFrontFamilyMapper, FrontFamily> implements IUniFrontFamilyService {

    @Autowired
    private UniFrontFamilyMapper uniFrontFamilyMapper;

    @Autowired
    private IUniUserService uniUserService;

    @Override
    public List<FrontFamily> getList() {
        // 获取用户id
        Long userId = uniUserService.getUserId();

        //  查询条件 通过规定条件查询对应用户家庭成员
        LambdaQueryWrapper<FrontFamily> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontFamily::getUserId,userId).orderByAsc(FrontFamily::getIsSelf).orderByAsc(FrontFamily::getCreateTime);

        return this.list(queryWrapper);
    }

    @Override
    public Boolean add(FrontFamily frontFamily) {
        // 获取用户id
        Long userId = uniUserService.getUserId();

        // 判断是否为本人
        if (frontFamily.getIsSelf() == 1){
            updateIsSelf(userId);
        }
        frontFamily.setUserId(userId);
        return this.save(frontFamily);
    }

    @Override
    public Boolean update(FrontFamily frontFamily) {
        if (frontFamily.getIsSelf() == 1){
            // 获取用户id
            Long userId = uniUserService.getUserId();
            updateIsSelf(userId);
        }
        return this.updateById(frontFamily);
    }

    @Override
    public Boolean delete(Long id) {
        return this.removeById(id);
    }

    private void updateIsSelf(Long id){
        // 查询是否存在本人
        LambdaQueryWrapper<FrontFamily> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontFamily::getUserId,id).eq(FrontFamily::getIsSelf,1);
        FrontFamily one = this.getOne(queryWrapper);
        if (one != null){
            // 将前本人改为非本人 每个家庭本人唯一
            one.setIsSelf(0);
            this.updateById(one);
        }
    }
}
