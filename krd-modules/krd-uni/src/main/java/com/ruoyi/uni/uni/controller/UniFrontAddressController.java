package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.uni.uni.domain.Address;
import com.ruoyi.uni.uni.service.IUniFrontAddressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName UniFrontAddressController
 * @Description 用户地址管理
 * <AUTHOR>
 * @Date 2025/5/19 下午3:25
 */
@Slf4j
@Tag(name = "用户地址管理", description = "小程序用户地址管理接口控制器")
@RestController
@RequestMapping("/uni/user/address")
public class UniFrontAddressController extends BaseController {

    @Autowired
    private IUniFrontAddressService uniFrontAddressService;

    @Operation(description = "获取用户地址列表")
    @GetMapping("/getList")
    public AjaxResult getList() {
        return success(uniFrontAddressService.getList());
    }

    @Operation(description = "添加用户地址")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody Address address) {
        return success(uniFrontAddressService.add(address));
    }

    @Operation(description = "修改用户地址")
    @PostMapping("/update")
    public AjaxResult update(@RequestBody Address address) {
        return success(uniFrontAddressService.update(address));
    }

    @Operation(description = "删除用户地址")
    @PostMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id) {
        return success(uniFrontAddressService.delete(id));
    }
}
