package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.CblTableDataInfo;
import com.ruoyi.uni.uni.domain.vo.UniGoodsVo;
import com.ruoyi.uni.uni.service.UniFrontGoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName UniFrontGoodsController
 * @Description 小程序商城控制器
 * <AUTHOR>
 * @Date 2025/6/4 下午5:23
 */
@Slf4j
@RestController
@Tag(name =  "小程序商城", description = "小程序商城接口")
@RequestMapping("/uni/goods")
public class UniFrontGoodsController extends BaseController {

    @Autowired
    private UniFrontGoodsService uniFrontGoodsService;

    @Operation(description = "首页产品推荐 0-权威推荐 1-首页大图 2-首页小图 3-商城")
    @GetMapping("/indexImageList")
    public CblTableDataInfo getIndexImageList(@RequestParam(value = "num")Integer num,HttpServletRequest  request) {
        startPage();
        return getTableUni(uniFrontGoodsService.getIndexImageList(num,request));
    }

    @Operation(description = "分类列表")
    @GetMapping("/gategoryList")
    public AjaxResult getGategoryList() {
        return success(uniFrontGoodsService.getGategoryList());
    }

    @Operation(description = "商城广告推荐 1-商城广告 2-经期记录广告 3-个人中心广告 4-首页广告")
    @GetMapping("/goodsAdvertList")
    public AjaxResult getGoodsAdvertList(@RequestParam(value = "type")Long type) {
        return success(uniFrontGoodsService.getGoodsAdvertList(type));
    }

    @Operation(description = "商品详情")
    @GetMapping("/goodsDetail")
    public AjaxResult getGoodsById(@RequestParam(value = "id")Long id, HttpServletRequest request) {
        return success(uniFrontGoodsService.getGoodsById(id,request));
    }

    @Operation(description = "商品列表/ 对应分类商品列表")
    @GetMapping("/goodsList")
    public CblTableDataInfo getGoodsList(UniGoodsVo.GoodsListSearch vo,HttpServletRequest request) {
        startPage();
        List<UniGoodsVo.RecommendGoods> goodsList = uniFrontGoodsService.getGoodsList(vo,request);
        return getTableUni(goodsList);
    }

    @Operation(description = "商品评价列表")
    @GetMapping("/goodsEvaluateList")
    public CblTableDataInfo getGoodsEvaluateList(@RequestParam(value = "id")Long id) {
        startPage();
        return getTableUni(uniFrontGoodsService.getGoodsEvaluateList(id));
    }
}
