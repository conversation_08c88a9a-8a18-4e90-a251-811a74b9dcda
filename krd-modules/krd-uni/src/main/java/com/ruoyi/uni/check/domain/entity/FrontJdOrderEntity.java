package com.ruoyi.uni.check.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.alibaba.fastjson.JSONObject;
import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 京东订单信息表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-08 17:55:12
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName(value = "front_jd_order",  autoResultMap = true)
public class FrontJdOrderEntity implements Serializable {

	private static final long serialVersionUID = 1931651212373315586L;

	/**
	 * 商家快递订单ID
	 */
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 京东物流订单号
	 */
	private String orderCode;
	/**
	 * 京东物流运单号
	 */
	private String waybillCode;
	/**
	 * 预计揽收时间
	 */
	private Date pickupPromiseTime;
	/**
	 * 预计送达时间
	 */
	private Date deliveryPromiseTime;
	/**
	 * 寄件人信息
	 */
	@TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
	private JSONObject senderContact;
	/**
	 * 收件人信息
	 */
	@TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
	private JSONObject receiverContact;
	/**
	 * 返回信息
	 */
	@TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
	private JSONObject createOrderResponse;
	/**
	 * 状态编码：100（已接单）；390（已下发）；403（控单中）；406（解除控单成功）；420（已揽收）；430（运输中）；440（派送中）；480（已拦截）；500（异常终止）；510（妥投）；530（拒收）；690（已取消）
	 */
	private String orderStatus;
	/**
	 * 状态描述：100（已接单）；390（已下发）；420（已揽收）；430（运输中）；440（派送中）；480（已拦截）；500（异常终止）；510（妥投）；530（拒收）；690（已取消）
	 */
	private String orderStatusDesc;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;

}
