package com.ruoyi.uni.check.service;

import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.CommonCheckPreCreateOrderResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.Contact;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.Response;
import com.lop.open.api.sdk.response.ECAP.EcapV1OrdersPrecheckLopResponse;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.uni.check.domain.dto.JDPrecheckOrderDTO;

/**
 * <AUTHOR>
 * @date 2025/6/5 15:31
 */
public interface JDEcapV1OrdersPrecheckLopService {

    R<CommonCheckPreCreateOrderResponse> ecapV1OrdersPrecheck(JDPrecheckOrderDTO precheckOrderDTO);
}
