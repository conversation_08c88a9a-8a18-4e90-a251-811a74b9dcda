package com.ruoyi.uni.uni.service;

import com.ruoyi.uni.uni.domain.resp.LoginResponse;
import com.ruoyi.uni.uni.domain.resp.WeChatLoginResponse;

import javax.servlet.http.HttpServletRequest;


/**
 * @ClassName WeChatLoginService
 * @Description 微信授权登录业务类
 * <AUTHOR>
 * @Date 2025/5/15 下午4:50
 */
public interface IWeChatLoginService {

    /**
     * 微信登录小程序授权登录
     * @param response 登录参数
     * @return LoginResponse
     */
    LoginResponse weChatAuthorizeProgramLogin(WeChatLoginResponse response, HttpServletRequest httpServletRequest);

    /**
     * 获取微信登录授权码
     * @param token
     * @return true/false
     */
    boolean getWeChatAuthorizeCode(String token);
}
