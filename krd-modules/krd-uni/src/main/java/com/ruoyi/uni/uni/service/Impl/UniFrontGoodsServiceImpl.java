package com.ruoyi.uni.uni.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.*;
import com.ruoyi.system.api.mapper.*;
import com.ruoyi.uni.uni.domain.FrontShopCollect;
import com.ruoyi.uni.uni.domain.vo.UniGoodsVo;
import com.ruoyi.uni.uni.mapper.FrontShopCollectMapper;
import com.ruoyi.uni.uni.mapper.UniFrontGoodsMapper;
import com.ruoyi.uni.uni.service.IFrontSourceService;
import com.ruoyi.uni.uni.service.IUniUserService;
import com.ruoyi.uni.uni.service.UniFrontGoodsService;
import com.ruoyi.uni.uni.utils.oss.OssUrlCleanerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName UniFrontGoodsServiceImpl
 * @Description 小程序商城业务接口实现类
 * <AUTHOR>
 * @Date 2025/6/4 下午5:31
 */
@Slf4j
@Service
public class UniFrontGoodsServiceImpl implements UniFrontGoodsService {

    @Autowired
    private UniFrontGoodsMapper uniFrontGoodsMapper;

    @Autowired
    private FrontAdvertMapper frontAdvertMapper;

    @Autowired
    private FrontGoodsSpecMapper frontGoodsSpecMapper;

    @Autowired
    private FrontEvaluateMapper frontEvaluateMapper;

    @Autowired
    private FrontCouponMapper frontCouponMapper;

    @Autowired
    private FrontGategoryMapper frontGategoryMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    @Autowired
    private FrontUserMapper userMapper;

    @Autowired
    private FrontShopCollectMapper shopCollectMapper;

    @Autowired
    private IUniUserService userService;

    @Autowired
    private IFrontSourceService frontSourceService;

    @Autowired
    private FrontCouponInfoMapper couponInfoMapper;

    @Override
    public List<UniGoodsVo.RecommendGoods> getIndexImageList(Integer num,HttpServletRequest  request) {
        String header = request.getHeader("authorization");
        BigDecimal discount;
        if (header !=  null){
            // 获取会员折扣
            discount = frontSourceService.getUserMemberLevelDiscount();
        } else {
            discount = BigDecimal.ZERO;
        }

        // 查询首页大图展示产品数据
        List<FrontGoods> frontGoods = uniFrontGoodsMapper.selectList(new LambdaQueryWrapper<FrontGoods>().eq(FrontGoods::getFeaturedFirst,num).eq(FrontGoods::getIsStatus,1));

        List<UniGoodsVo.RecommendGoods> recommendGoods = new ArrayList<>();

        frontGoods.forEach(item ->{
            UniGoodsVo.RecommendGoods goods = new UniGoodsVo.RecommendGoods();
            goods.setId(item.getId());
            goods.setGoodsName(item.getName());
            if (num == 3 || num == 0){
                goods.setGoodsIndexImg(ossUrlCleanerUtil.getSignatureUrl(item.getFirstPic()));
            }else {
                goods.setGoodsIndexImg(ossUrlCleanerUtil.getSignatureUrl(item.getIndexImage())
                );
            }
            goods.setPrice(item.getPrice());
            if (discount.compareTo(BigDecimal.ZERO) != 0){
                // 计算折扣价格保留俩位小数
                goods.setVipPrice(item.getPrice().multiply(discount).setScale(2, RoundingMode.HALF_UP));
            }else {
                goods.setVipPrice(item.getPrice());
            }
            recommendGoods.add(goods);
        });
        return recommendGoods;
    }

    @Override
    public List<FrontAdvert> getGoodsAdvertList(Long type) {
        List<FrontAdvert> adverts = frontAdvertMapper.selectList(new LambdaQueryWrapper<FrontAdvert>()
                .eq(FrontAdvert::getAdType, type)
                .ne(FrontAdvert::getStatus, 0));
        adverts.forEach(item -> {
            item.setMaterial(ossUrlCleanerUtil.getSignatureUrl(item.getMaterial()));
        });
        return adverts;
    }

    @Override
    public List<UniGoodsVo.Gategory> getGategoryList() {
        List<UniGoodsVo.Gategory> gategoryList = new ArrayList<>();
        // 查询商品顶级分类
        List<FrontGategory> frontGategories = frontGategoryMapper.selectList(new LambdaQueryWrapper<FrontGategory>().eq(FrontGategory::getParentId, 0).eq(FrontGategory::getIsShow, 1));

        frontGategories.forEach(item ->{
            UniGoodsVo.Gategory gategory = new UniGoodsVo.Gategory();
            gategory.setName(item.getTitle());
            List<FrontGategory> frontGategories1 = frontGategoryMapper.selectList(new LambdaQueryWrapper<FrontGategory>().eq(FrontGategory::getParentId, item.getId()).eq(FrontGategory::getIsShow, 1));
            frontGategories1.forEach(item1 ->{
                item1.setIcon(ossUrlCleanerUtil.getSignatureUrl(item1.getIcon()));
            });
            gategory.setGategoryList(frontGategories1);

            gategoryList.add(gategory);
        });

        return gategoryList;
    }

    @Override
    public UniGoodsVo.GoodsDetail getGoodsById(Long id, HttpServletRequest request) {
        // 获取会员折扣
        BigDecimal discount;
        String header = request.getHeader("authorization");
        if (header != null){
            discount = frontSourceService.getUserMemberLevelDiscount();
        }else {
            discount = BigDecimal.ZERO;
        }
        UniGoodsVo.GoodsDetail goodsDetail = new UniGoodsVo.GoodsDetail();
        // 获取商品详情
        FrontGoods frontGoods = uniFrontGoodsMapper.selectById(id);

        // 获取商品对应规格
        List<FrontGoodsSpec> frontGoodsSpecList = frontGoodsSpecMapper.selectList(new LambdaQueryWrapper<FrontGoodsSpec>().eq(FrontGoodsSpec::getGoodsId, id));

        frontGoodsSpecList.forEach(item ->{
            if (discount.compareTo(BigDecimal.ZERO) !=0){
                item.setVipPrice(item.getPrice().multiply(discount).setScale(2, RoundingMode.HALF_UP));
            }else {
                item.setVipPrice(item.getPrice());
            }
            item.setPic(ossUrlCleanerUtil.getSignatureUrl(item.getPic()));
        });

        frontGoods.setFrontGoodsSpecList(frontGoodsSpecList);

        if (frontGoods.getFirstPic() != null){
            frontGoods.setFirstPic(ossUrlCleanerUtil.getSignatureUrl(frontGoods.getFirstPic()));
        }

        if (frontGoods.getBanner() != null){
            frontGoods.setBanner(ossUrlCleanerUtil.getSignatureUrl(frontGoods.getBanner()));
        }

        if (frontGoods.getDetails() != null){
            frontGoods.setDetails(ossUrlCleanerUtil.getSignatureEditor(frontGoods.getDetails()));
        }

        if (discount.compareTo(BigDecimal.ZERO) !=0){
            frontGoods.setVipPrice(frontGoods.getPrice().multiply(discount).setScale(2, RoundingMode.HALF_UP));
        }else {
            frontGoods.setVipPrice(frontGoods.getPrice());
        }

        goodsDetail.setFrontGoods(frontGoods);

        // 查询商品对应加精评论以及评论总数
        List<FrontEvaluate> frontEvaluates = frontEvaluateMapper.selectList(new LambdaQueryWrapper<FrontEvaluate>()
                .eq(FrontEvaluate::getIsRefine, 1)
                .eq(FrontEvaluate::getGoodsId, id)
                .orderByAsc(FrontEvaluate::getUpdateTime)
                .last("limit 3"));

        goodsDetail.setEvaluateList(getEvaluateList(frontEvaluates));
        goodsDetail.setCommentNum(frontEvaluateMapper.selectCount(new LambdaQueryWrapper<FrontEvaluate>()
                .eq(FrontEvaluate::getGoodsId, id)
                .eq(FrontEvaluate::getIsShow, 1)));

        Long userId = null;

        // 处理优惠券
        List<StoreFrontCoupon> couponList = new ArrayList<>();

        if (header != null){
            userId = userService.getUserId();

            // 登录之后
            List<StoreFrontCoupon> frontCoupons = frontCouponMapper.selectList(null);

            frontCoupons.forEach(item ->{
                // 查询当前用户对应当前优惠券数量 和优惠券对应数量
                Long userCount = couponInfoMapper.selectCount(new LambdaQueryWrapper<FrontCouponInfo>().eq(FrontCouponInfo::getCouponId, item.getId()).eq(FrontCouponInfo::getUserId, userService.getUserId()));
                Long count = couponInfoMapper.selectCount(new LambdaQueryWrapper<FrontCouponInfo>().eq(FrontCouponInfo::getCouponId, item.getId()));

                if (userCount >= item.getLimits() || count >= item.getTotal()){
                    return;
                }

                // 0为长期有效
                if ("0".equals(item.getExpirationDate())){
                    if (StringUtils.isNotEmpty(item.getUsed())){
                        StoreFrontCoupon storeFrontCoupon = getStoreFrontCoupon(item, id);
                        if (storeFrontCoupon != null){
                            couponList.add(storeFrontCoupon);
                        }
                    }
                }else {
                    String[] split = item.getExpirationDate().split(",");

                    // split长度为1是固定具体时间
                    if (split.length == 1){
                        // 计算出剩余过期时间
                        LocalDateTime plusDays = item.getCreateTime().plusDays(Integer.parseInt(item.getExpirationDate().isEmpty() ? "0" : split[0]));

                        // 判断是否在当前时间之前
                        if (plusDays.isAfter(LocalDateTime.now())){
                            if (StringUtils.isNotEmpty(item.getUsed())){
                                StoreFrontCoupon storeFrontCoupon = getStoreFrontCoupon(item, id);
                                if (storeFrontCoupon != null){
                                    couponList.add(storeFrontCoupon);
                                }
                            }
                        }
                    }

                    // split长度为2是时间数组
                    if (split.length == 2){
                        // 判断当前时间是否在指定时间范围内
                        if (LocalDate.now().isAfter(LocalDate.parse(split[0])) && LocalDate.now().isBefore(LocalDate.parse(split[1]))){
                            if (StringUtils.isNotEmpty(item.getUsed())){
                                StoreFrontCoupon storeFrontCoupon = getStoreFrontCoupon(item, id);
                                if (storeFrontCoupon != null){
                                    couponList.add(storeFrontCoupon);
                                }
                            }
                        }
                    }
                }

            });
        }


        goodsDetail.setStoreFrontCouponList(couponList);

        LambdaQueryWrapper<FrontShopCollect> wrapper = new LambdaQueryWrapper<FrontShopCollect>().eq(FrontShopCollect::getGoodsId, id);

        if (userId != null){
            wrapper.eq(FrontShopCollect::getUserId, userId);
            // 查询用户产品收藏状态
            Long count = shopCollectMapper.selectCount(wrapper);
            goodsDetail.setIsCollect(count > 0);
        }

        // 获取详情推荐商品
        List<UniGoodsVo.RecommendGoods> recommendGoods = new ArrayList<>();

        for (String s : frontGoods.getKeywords().split(",")) {
            List<FrontGoods> frontGoodsList = uniFrontGoodsMapper.selectList(new LambdaQueryWrapper<FrontGoods>().like(FrontGoods::getKeywords, s));
            if (frontGoodsList != null){
                setGoodsIndexImg(recommendGoods, frontGoodsList,discount);
            }

        }

        // 根据recommendGoods中id去重
        recommendGoods = recommendGoods.stream().distinct().collect(Collectors.toList());

        goodsDetail.setRecommendGoodsList(recommendGoods);
        return goodsDetail;
    }

    @Override
    public List<UniGoodsVo.RecommendGoods> getGoodsList(UniGoodsVo.GoodsListSearch vo,HttpServletRequest request) {
        // 获取会员折扣
        BigDecimal discount;
        String header = request.getHeader("authorization");
        if (header != null){
            discount = frontSourceService.getUserMemberLevelDiscount();
        }else {
            discount = BigDecimal.ZERO;
        }
        List<UniGoodsVo.RecommendGoods> recommendGoods = new ArrayList<>();

        LambdaQueryWrapper<FrontGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontGoods::getIsStatus, 1);

        if (StringUtils.isNotEmpty(vo.getKeyword())){
            queryWrapper.like(FrontGoods::getName, vo.getKeyword())
                    .or()
                    .like(FrontGoods::getKeywords, vo.getKeyword());
        }
        if (vo.getId() != 0){
            queryWrapper.eq(FrontGoods::getGategoryId, vo.getId());
            queryWrapper.eq(FrontGoods::getGoodsType, 1);
        }

        queryWrapper.orderByAsc(FrontGoods::getSort);

        List<FrontGoods> frontGoods = uniFrontGoodsMapper.selectList(queryWrapper);
        setGoodsIndexImg(recommendGoods, frontGoods,discount);

        return recommendGoods;
    }

    @Override
    public List<UniGoodsVo.EvaluateDetail> getGoodsEvaluateList(Long id) {
        List<FrontEvaluate> frontEvaluates = frontEvaluateMapper.selectList(new LambdaQueryWrapper<FrontEvaluate>()
                .eq(FrontEvaluate::getIsShow, 1)
                .eq(FrontEvaluate::getGoodsId, id)
                .orderByAsc(FrontEvaluate::getUpdateTime));
        if (frontEvaluates != null){
            return getEvaluateList(frontEvaluates);
        }
        return null;
    }

    private void setGoodsIndexImg (List<UniGoodsVo.RecommendGoods> recommendGoods, List<FrontGoods> frontGoods,BigDecimal discount){
        // 获取会员折扣
        frontGoods.forEach(item ->{
            UniGoodsVo.RecommendGoods recommendGood = new UniGoodsVo.RecommendGoods();
            recommendGood.setId(item.getId());
            recommendGood.setGoodsName(item.getName());
            recommendGood.setGoodsIndexImg(ossUrlCleanerUtil.getSignatureUrl(item.getFirstPic()));
            recommendGood.setPrice(item.getPrice());
            if (discount.compareTo(BigDecimal.ZERO) != 0){
                recommendGood.setVipPrice(item.getPrice().multiply(discount).setScale(2, RoundingMode.HALF_UP));
            }else {
                recommendGood.setVipPrice(item.getPrice());
            }
            recommendGoods.add(recommendGood);
        });
    }

    /**
     * 评论列表
     */
    private List<UniGoodsVo.EvaluateDetail> getEvaluateList(List<FrontEvaluate> frontEvaluates) {
        // 组装评论数据
        List<UniGoodsVo.EvaluateDetail> frontEvaluateList = new ArrayList<>();
        frontEvaluates.forEach(item ->{
            UniGoodsVo.EvaluateDetail evaluateDetail = new UniGoodsVo.EvaluateDetail();
            evaluateDetail.setUserId(item.getUserId());
            evaluateDetail.setUserName(userMapper.selectFrontUserById(item.getUserId()).getUserName());
            evaluateDetail.setUserImg(ossUrlCleanerUtil.getSignatureUrl(userMapper.selectFrontUserById(item.getUserId()).getUserIcon()));
            evaluateDetail.setContent(item.getContent());
            evaluateDetail.setPic(ossUrlCleanerUtil.getSignatureUrl(item.getPic()));
            evaluateDetail.setTickTime(item.getTickTime());
            evaluateDetail.setStarRating(item.getStarRating());

            frontEvaluateList.add(evaluateDetail);
        });
        return frontEvaluateList;
    }

    /**
     * 获取符合商品的优惠券
     * @return
     */
    private StoreFrontCoupon getStoreFrontCoupon(StoreFrontCoupon frontCoupon,Long goodsId) {
        // 将逗号分隔的字符串拆分为列表
        List<String> usedIds = Arrays.asList(frontCoupon.getUsed().split(","));
        // 通过id查询分类ID
        FrontGoods frontGoods = uniFrontGoodsMapper.selectById(goodsId);
        // 判断优惠券是否可用
        if (frontCoupon.getUsedType() == 1 && !usedIds.contains(String.valueOf(goodsId))){
            return null;
        }else if (frontCoupon.getUsedType() == 2 && !usedIds.contains(String.valueOf(frontGoods.getGategoryId()))){
            return null;
        }else if (frontCoupon.getUsedType() == 3 && !usedIds.contains(String.valueOf(userService.getUserId()))){// 判断类型是否为指定用户
            return null;
        }else {
            return frontCoupon;
        }

    }

}
