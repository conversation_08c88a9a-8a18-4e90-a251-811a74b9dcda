package com.ruoyi.uni.uni.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.uni.uni.domain.FrontSign;
import com.ruoyi.uni.uni.domain.vo.FrontSignVo;

/**
 * 用户签到Service接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IFrontSignService extends IService<FrontSign>
{

    /**
     * 查询用户签到列表
     *
     * @return 用户签到集合
     */
    public FrontSignVo selectFrontSignList();

    /**
     * 新增用户签到
     *
     * @param frontSign 用户签到
     * @return 结果
     */
    public int insertFrontSign(FrontSign frontSign);

}
