package com.ruoyi.uni.check.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 检测指标-结果表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 18:23:57
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("front_tmp_pactar_result")
public class FrontTmpPactarResultEntity implements Serializable {

	private static final long serialVersionUID = 1934919938543611906L;

	/**
	 * 
	 */
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 模版指标id
	 */
	private String pactarId;
	/**
	 * 结果名称
	 */
	private String resultName;
	/**
	 * 结果颜色
	 */
	private String resultColor;
	/**
	 * 结果范围-开始-值
	 */
	private BigDecimal resultRangeStartVal;
	/**
	 * 结果范围-开始-符号
	 */
	private String resultRangeStartSymbol;
	/**
	 * 结果范围-结束-值
	 */
	private BigDecimal resultRangeEndVal;
	/**
	 * 结果范围-结束-符号
	 */
	private String resultRangeEndSymbol;
	/**
	 * 结果解读
	 */
	private String resultInterpret;
	/**
	 * 健康建议
	 */
	private String healthAdvice;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 是否删除0-false ,1-true
	 */
	private Integer isDel;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;

}
