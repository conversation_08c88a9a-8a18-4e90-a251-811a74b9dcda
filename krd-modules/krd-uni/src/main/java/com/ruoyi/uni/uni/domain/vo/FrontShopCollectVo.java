package com.ruoyi.uni.uni.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @ClassName FrontShopCollectVo
 * @Description 收藏 相关交互参数
 * <AUTHOR>
 * @Date 2025/6/9 下午2:47
 */
public interface FrontShopCollectVo {

    @Data
    @Schema(description = "收藏查询对象")
    class SearchParam {
        @Schema(description = "名称")
        private String name;
    }

    @Data
    @Schema(description = "添加收藏对象")
    class AddParam {
        @Schema(description = "关联商品ID")
        private Long goodsId;
        @Schema(description = "关联规格ID")
        private Long specId;
    }

    @Data
    @Schema(description = "批量添加收藏")
    class CartListInfo {
        private List<AddParam> voList;
    }

}
