package com.ruoyi.uni.uni.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.FrontGoods;
import com.ruoyi.system.api.domain.FrontGoodsSpec;
import com.ruoyi.system.api.mapper.FrontGoodsSpecMapper;
import com.ruoyi.uni.uni.domain.FrontShopCart;
import com.ruoyi.uni.uni.domain.FrontShopCollect;
import com.ruoyi.uni.uni.domain.vo.FrontShopCollectVo;
import com.ruoyi.uni.uni.domain.vo.FrontShopCartVo;
import com.ruoyi.uni.uni.mapper.FrontShopCartMapper;
import com.ruoyi.uni.uni.mapper.FrontShopCollectMapper;
import com.ruoyi.uni.uni.mapper.UniFrontGoodsMapper;
import com.ruoyi.uni.uni.service.IFrontShopCollectService;
import com.ruoyi.uni.uni.service.IFrontSourceService;
import com.ruoyi.uni.uni.utils.oss.OssUrlCleanerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @ClassName FrontShopCollectServiceImpl
 * @Description 收藏 服务实现类
 * <AUTHOR>
 * @Date 2025/6/9 上午11:04
 */
@Slf4j
@Service
public class FrontShopCollectServiceImpl implements IFrontShopCollectService {


    @Autowired
    private FrontShopCollectMapper frontShopCollectMapper;

    @Autowired
    private UniFrontGoodsMapper uniFrontGoodsMapper;

    @Autowired
    private FrontGoodsSpecMapper frontGoodsSpecMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    @Autowired
    private FrontShopCartMapper frontShopCartMapper;

    @Autowired
    private IFrontSourceService frontSourceService;

    @Override
    public List<FrontShopCartVo.CartList> selectFrontShopCollectList(FrontShopCollectVo.SearchParam vo) {
        // 计算会员折扣
        BigDecimal discount = frontSourceService.getUserMemberLevelDiscount();

        List<FrontShopCartVo.CartList> list = new ArrayList<>();
        // 获取对应用户收藏
        List<FrontShopCollect> frontShopCollectList = frontShopCollectMapper.selectList(new LambdaQueryWrapper<FrontShopCollect>().eq(FrontShopCollect::getUserId, SecurityUtils.getUserId()));
        if (frontShopCollectList != null){
            frontShopCollectList.forEach(frontShopCollect -> {
                LambdaQueryWrapper<FrontGoods> queryWrapper = new LambdaQueryWrapper<FrontGoods>().eq(FrontGoods::getId, frontShopCollect.getGoodsId());

                if (vo.getName() !=  null){
                    queryWrapper.like(FrontGoods::getName,vo.getName());
                }
                // 获取对应商品对象
                FrontGoods goods = uniFrontGoodsMapper.selectOne(queryWrapper);

                if (goods != null){
                    FrontShopCartVo.CartList cartList = new FrontShopCartVo.CartList();

                    cartList.setId(frontShopCollect.getId());
                    cartList.setGoodsId(frontShopCollect.getGoodsId());
                    cartList.setGoodsName(goods.getName());
                    if (frontShopCollect.getSpecId() != null){
                        cartList.setSpecId(frontShopCollect.getSpecId());
                        cartList.setSpecName(frontGoodsSpecMapper.selectOne(new LambdaQueryWrapper<FrontGoodsSpec>().eq(FrontGoodsSpec::getId,frontShopCollect.getSpecId())).getTitle());
                    }

                    // 判断会员折扣是否为0
                    if (discount.compareTo(BigDecimal.ZERO) != 0){
                        cartList.setPrice(goods.getPrice().multiply(discount).setScale(2, RoundingMode.HALF_UP));
                    }else {
                        cartList.setPrice(goods.getPrice());
                    }
                    cartList.setImage(ossUrlCleanerUtil.getSignatureUrl(goods.getFirstPic()));

                    list.add(cartList);
                }

            });
            return list;
        }
        return list;
    }

    @Override
    public Boolean addOrDelete(FrontShopCollect frontShopCollect) {
        LambdaQueryWrapper<FrontShopCollect> queryWrapper = new LambdaQueryWrapper<FrontShopCollect>()
                .eq(FrontShopCollect::getGoodsId, frontShopCollect.getGoodsId())
                .eq(FrontShopCollect::getUserId, SecurityUtils.getUserId());

        if (frontShopCollect.getSpecId() != null){
            queryWrapper.eq(FrontShopCollect::getSpecId, frontShopCollect.getSpecId());
        }else {
            queryWrapper.isNull(FrontShopCollect::getSpecId);
        }
        FrontShopCollect shopCollect = frontShopCollectMapper.selectOne(queryWrapper);
        if (shopCollect != null){
            frontShopCollectMapper.deleteById(shopCollect.getId());
            // 删除返回false
            return Boolean.FALSE;
        }
        frontShopCollect.setUserId(SecurityUtils.getUserId());
        frontShopCollect.setCreateTime(DateUtils.getLocalDateTime());
        return frontShopCollectMapper.insert(frontShopCollect) > 0;
    }

    @Override
    public Boolean delete(Long[] ids) {
        return frontShopCollectMapper.deleteBatchIds(Arrays.asList(ids)) > 0;
    }

    @Override
    public Boolean deleteByGoodsId(Long goodsId) {
        FrontShopCollect frontShopCollect = frontShopCollectMapper.selectOne(new LambdaQueryWrapper<FrontShopCollect>().eq(FrontShopCollect::getGoodsId, goodsId).eq(FrontShopCollect::getUserId, SecurityUtils.getUserId()));
        if (frontShopCollect != null){
            return frontShopCollectMapper.deleteById(frontShopCollect.getId()) > 0;
        }
        return false;
    }

    @Override
    @Transactional
    public Boolean addBatch(FrontShopCollectVo.CartListInfo vo) {
        if (vo.getVoList() != null){
            vo.getVoList().forEach(item -> {
                LambdaQueryWrapper<FrontShopCart> queryWrapper = new LambdaQueryWrapper<FrontShopCart>()
                        .eq(FrontShopCart::getGoodsId, item.getGoodsId())
                        .eq(FrontShopCart::getUserId, SecurityUtils.getUserId());

                LambdaQueryWrapper<FrontShopCollect> wrapper = new LambdaQueryWrapper<FrontShopCollect>()
                        .eq(FrontShopCollect::getGoodsId, item.getGoodsId())
                        .eq(FrontShopCollect::getUserId, SecurityUtils.getUserId());

                if (item.getSpecId() != null){
                    wrapper.eq(FrontShopCollect::getSpecId, item.getSpecId());
                    queryWrapper.eq(FrontShopCart::getSpecId, item.getSpecId());
                }else {
                    wrapper.isNull(FrontShopCollect::getSpecId);
                    queryWrapper.isNull(FrontShopCart::getSpecId);
                }

                // 查询收藏是否存在该商品
                Long l = frontShopCollectMapper.selectCount(wrapper);

                if (l == 0L){
                    FrontShopCollect frontShopCollect = new FrontShopCollect();
                    frontShopCollect.setUserId(SecurityUtils.getUserId());
                    frontShopCollect.setGoodsId(item.getGoodsId());
                    frontShopCollect.setSpecId(item.getSpecId());
                    frontShopCollect.setCreateTime(DateUtils.getLocalDateTime());
                    frontShopCollectMapper.insert(frontShopCollect);
                }

                // 删除购物车
                frontShopCartMapper.delete(queryWrapper);
            });
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
