package com.ruoyi.uni.wechat.service.Impl;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.WeChatConstants;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.util.RedisUtil;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.SysConfig;
import com.ruoyi.system.api.domain.WechatExceptions;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.system.api.mapper.SysConfigMapper;
import com.ruoyi.uni.uni.domain.user.FrontUserToken;
import com.ruoyi.uni.uni.domain.user.PhoneInfo;
import com.ruoyi.uni.uni.domain.vo.WeChatAccessTokenVo;
import com.ruoyi.uni.uni.domain.vo.WeChatMiniAuthorizeVo;
import com.ruoyi.uni.uni.mapper.FrontUserTokenMapper;
import com.ruoyi.uni.wechat.mapper.WechatExceptionsMapper;
import com.ruoyi.uni.wechat.service.WechatService;
import com.ruoyi.uni.wechat.util.RestTemplateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName WechatServiceImpl
 * @Description 微信处理业务实现类
 * <AUTHOR>
 * @Date 2025/5/16 上午10:44
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WechatServiceImpl implements WechatService {

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private WechatExceptionsMapper wechatExceptionsMapper;

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Autowired
    private FrontUserTokenMapper frontUserTokenMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RestTemplateUtil restTemplateUtil;

    @Override
    public WeChatMiniAuthorizeVo miniAuthCode(String code) {
        Map<String, String> weChatConfig = getWeChatConfig();

//        String appid = weChatConfig.get(WeChatConstants.WECHAT_APPID);
//        String secret = weChatConfig.get(WeChatConstants.WECHAT_APPSECRET);
        String appid="wxbbfc64e72fa3980f";
        String secret="7599688338323937681f597db2a04331";
        if (StringUtils.isBlank(appid)) {
            throw new RuntimeException("微信小程序appId未设置");
        }
        if (StringUtils.isBlank(secret)) {
            throw new RuntimeException("微信小程序secret未设置");
        }
        String url = StringUtils.format(WeChatConstants.WECHAT_MINI_SNS_AUTH_CODE2SESSION_URL, appid, secret, code);
        JSONObject data = restTemplateUtil.getData(url);
        if (ObjectUtils.isEmpty(data)) {
            throw new RuntimeException("微信平台接口异常，没任何数据返回！");
        }
        if (data.containsKey("errcode") && !"0".equals(data.getString("errcode"))) {
            if (data.containsKey("errmsg")) {
                // 保存到微信异常表
                wxExceptionDispose(data, "微信小程序登录凭证校验异常");
                throw new RuntimeException("微信接口调用失败：" + data.getString("errcode") + data.getString("errmsg"));
            }
        }
        return JSONObject.parseObject(data.toJSONString(), WeChatMiniAuthorizeVo.class);
    }

    @Override
    public String decryptPhone(String code) {
        String accessToken = getMiniAccessToken();//获取accessToken
        PhoneInfo userPhone = getUserPhone(accessToken, code);
        String phoneNumber = userPhone.getPhoneNumber();
        log.info("user phone{}.",phoneNumber);
//        LambdaQueryWrapper<FrontUser> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(FrontUser::getUserMobile,phoneNumber).last("LIMIT 1");
        FrontUser one = frontUserMapper.selectPhoneOne(phoneNumber);
        // 通过id查询用户openid
        if (one != null){
            FrontUserToken frontUserToken = frontUserTokenMapper.selectByUserId(one.getId());
            if (frontUserToken != null){
                if(StringUtils.isNotEmpty(frontUserToken.getOpenId())){
                    throw new RuntimeException("该手机号已绑定其他微信号，请更换手机号！");
                }
            }
        }
        return phoneNumber;
    }

    /**
     * 微信异常处理
     * @param jsonObject 微信返回数据
     * @param remark 备注
     */
    private void wxExceptionDispose(JSONObject jsonObject, String remark) {
        WechatExceptions wechatExceptions = new WechatExceptions();
        wechatExceptions.setErrcode(jsonObject.getString("errcode"));
        wechatExceptions.setErrmsg(StringUtils.isNotBlank(jsonObject.getString("errmsg")) ? jsonObject.getString("errmsg") : "");
        wechatExceptions.setData(jsonObject.toJSONString());
        wechatExceptions.setRemark(remark);
        wechatExceptions.setCreateTime(new Date());
        wechatExceptions.setUpdateTime(new Date());
        wechatExceptionsMapper.insertWechatExceptions(wechatExceptions);
    }

    private PhoneInfo getUserPhone(String accessToken, String code) {
        Map<String, String> weChatConfig = getWeChatConfig();

        //        String appid = weChatConfig.get(WeChatConstants.WECHAT_APPID);
//        String secret = weChatConfig.get(WeChatConstants.WECHAT_APPSECRET);
        String appid="wxbbfc64e72fa3980f";
        String secret="7599688338323937681f597db2a04331";
        if (StringUtils.isBlank(appid)) {
            throw new RuntimeException("微信小程序appId未设置");
        }

        if (StringUtils.isBlank(secret)) {
            throw new RuntimeException("微信小程序secret未设置");
        }
        String url = StringUtils.format(WeChatConstants.WECHAT_GET_USER_PHONE_URL, accessToken);
        JSONObject jsonData = new JSONObject();
        jsonData.put("code",code);
        String result = restTemplateUtil.postJsonData(url, jsonData);
        log.debug("get user phone result.{}",result);
        JSONObject data = JSONObject.parseObject(result);
        if (ObjectUtils.isEmpty(data)) {
            throw new RuntimeException("微信平台接口异常，没任何数据返回！");
        }
        if (data.containsKey("errcode") && !"0".equals(data.getString("errcode"))) {
            if (data.containsKey("errmsg")) {
                wxExceptionDispose(data, "获取用户手机号异常");
                throw new RuntimeException("微信接口调用失败：" + data.getString("errcode") + data.getString("errmsg"));
            }
        }
        JSONObject phone_info = data.getJSONObject("phone_info");
        if(phone_info == null){
            throw new RuntimeException("微信平台接口异常，没任何数据返回！");
        }
        PhoneInfo phoneInfo = new PhoneInfo();
        phoneInfo.setPhoneNumber(phone_info.getString("phoneNumber"));
        phoneInfo.setPurePhoneNumber(phone_info.getString("purePhoneNumber"));
        phoneInfo.setCountryCode(phone_info.getString("countryCode"));
        phoneInfo.setWatermark(phone_info.getJSONObject("watermark"));
        return phoneInfo;
    }

    @Override
    public String getMiniAccessToken() {
        boolean exists = redisUtil.exists(WeChatConstants.REDIS_WECAHT_MINI_ACCESS_TOKEN_KEY);
        if (exists) {
            Object accessToken = redisUtil.get(WeChatConstants.REDIS_WECAHT_MINI_ACCESS_TOKEN_KEY);
            return accessToken.toString();
        }
        Map<String, String> weChatConfig = getWeChatConfig();

        //        String appid = weChatConfig.get(WeChatConstants.WECHAT_APPID);
//        String secret = weChatConfig.get(WeChatConstants.WECHAT_APPSECRET);
        String appid="wxbbfc64e72fa3980f";
        String secret="7599688338323937681f597db2a04331";
        if (StringUtils.isBlank(appid)) {
            throw new RuntimeException("微信小程序appId未设置");
        }

        if (StringUtils.isBlank(secret)) {
            throw new RuntimeException("微信小程序secret未设置");
        }
        WeChatAccessTokenVo accessTokenVo = getAccessToken(appid, secret, "mini");
        // 缓存accessToken
        redisUtil.set(WeChatConstants.REDIS_WECAHT_MINI_ACCESS_TOKEN_KEY, accessTokenVo.getAccessToken(),
                accessTokenVo.getExpiresIn().longValue() - 1800L, TimeUnit.SECONDS);
        return accessTokenVo.getAccessToken();
    }

    /**
     * 获取微信accessToken
     * @param appId appId
     * @param secret secret
     * @param type mini-小程序，public-公众号，app-app
     * @return WeChatAccessTokenVo
     */
    private WeChatAccessTokenVo getAccessToken(String appId, String secret, String type) {
        String url = StringUtils.format(WeChatConstants.WECHAT_ACCESS_TOKEN_URL, appId, secret);
        JSONObject data = restTemplateUtil.getData(url);
        if (ObjectUtils.isEmpty(data)) {
            throw new RuntimeException("微信平台接口异常，没任何数据返回！");
        }
        if (data.containsKey("errcode") && !"0".equals(data.getString("errcode"))) {
            if (data.containsKey("errmsg")) {
                // 保存到微信异常表
                wxExceptionDispose(data, StringUtils.format("微信获取accessToken异常，{}端", type));
                throw new RuntimeException("微信接口调用失败：" + data.getString("errcode") + data.getString("errmsg"));
            }
        }
        return com.alibaba.fastjson.JSONObject.parseObject(data.toJSONString(), WeChatAccessTokenVo.class);
    }

    /**
     * 获取微信相关配置
     */
    private Map<String, String> getWeChatConfig() {
        SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique(WeChatConstants.WECHAT_CONFIG_KEY);
        String configValue = sysConfig.getConfigValue();

        // 解析configValue json 获取配置
        JSONObject jsonObject = JSONObject.parseObject(configValue);
        String appid = jsonObject.getString(WeChatConstants.WECHAT_APPID);
        String secret = jsonObject.getString(WeChatConstants.WECHAT_APPSECRET);
        Map<String, String> map = new HashMap<>();
        map.put(WeChatConstants.WECHAT_APPID, appid);
        map.put(WeChatConstants.WECHAT_APPSECRET, secret);
        return map;
    }
}
