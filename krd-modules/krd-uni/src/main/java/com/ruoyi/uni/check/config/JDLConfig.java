package com.ruoyi.uni.check.config;

import com.alibaba.fastjson.JSONObject;
import com.lop.open.api.sdk.DefaultDomainApiClient;
import com.lop.open.api.sdk.plugin.LopPlugin;
import com.lop.open.api.sdk.plugin.factory.OAuth2PluginFactory;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.SysConfig;
import com.ruoyi.system.api.mapper.SysConfigMapper;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 京东物流配置
 * <AUTHOR>
 * @date 2025/6/5 10:24
 */
@Scope("prototype")//每次获取bean都重新加载配置，保持配置实时生效
@Configuration
@Data
@Slf4j
public class JDLConfig {

    /**
     * 京东物流配置key
     */
    private final static String configKey = "jd_logistics_config";

    @Resource
    private RedisService redisService;
    @Resource
    private SysConfigMapper sysConfigMapper;

    //远程调用客户端
    private DefaultDomainApiClient client = null;
    //认证插件
    private LopPlugin lopPlugin = null;


    //客户编码
    private String customerCode = null;
    //订单来源：2为 C2B
    private Integer orderOrigin = null;
    //配送主产品编码：ed-m-0001为京东标准快递，ed-m-0002为京东特快
    private String productsReq_productCode = null;

    //付费方式
    private Integer settleType = null;
    private Integer c2bAddedSettleTypeInfo_basicFreigthSettleType = null;
    private Integer c2bAddedSettleTypeInfo_packageServiceSettleType = null;
    private Integer c2bAddedSettleTypeInfo_guaranteeMoneyServiceSettleType = null;

    //收货信息
    private String receiverContact_name = null;
    private String receiverContact_company = null;
    private String receiverContact_mobile = null;
    private String receiverContact_phone = null;
    private String receiverContact_fullAddress = null;

    //货品信息
    private String cargoes_0_name = null;
    private Integer cargoes_0_quantity = null;
    private BigDecimal cargoes_0_weight = null;
    private BigDecimal cargoes_0_volume = null;

    //商品信息
    private String goods_0_name = null;
    private Integer goods_0_quantity = null;

    //备注
    private String remark = null;

    @PostConstruct
    public void init() {
        String configValue = redisService.getCacheObject(CacheConstants.SYS_CONFIG_KEY + configKey);//获取京东物流配置
        if(!StringUtils.hasText(configValue)){
            SysConfig config = new SysConfig();
            config.setConfigKey(configKey);
            SysConfig retConfig = sysConfigMapper.selectConfig(config);
            configValue = retConfig.getConfigValue();
        }

        if(!StringUtils.hasText(configValue)){
            throw new RuntimeException("京东物流配置获取失败");
        }
        //初始化配置
        configInit(JSONObject.parseObject(configValue));
        log.info("京东物流配置初始化成功：{}",configValue);
    }

    /**
     * 配置初始化
     * @return
     */
    private void configInit(JSONObject content) {
        JDLConfig dto = content.toJavaObject(JDLConfig.class);
        BeanUtils.copyProperties(dto, this);//先拷贝属性

        //远程调用客户端
        this.client = new DefaultDomainApiClient(content.getString("serverUrl"),500,15000);
        //使用开放平台ISV/自研商家应用调用接口
        this.lopPlugin = OAuth2PluginFactory.produceLopPlugin(content.getString("appKey"), content.getString("appSecret"), content.getString("accessToken"));
    }

}
