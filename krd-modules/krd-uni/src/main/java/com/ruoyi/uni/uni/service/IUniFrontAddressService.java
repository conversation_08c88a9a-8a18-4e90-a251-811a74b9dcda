package com.ruoyi.uni.uni.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.uni.uni.domain.Address;

import java.util.List;

/**
 * @ClassName IUniFrontAddressService
 * @Description 用户端地址管理业务类
 * <AUTHOR>
 * @Date 2025/5/19 下午3:32
 */
public interface IUniFrontAddressService extends IService<Address> {

    /**
     * 获取用户地址列表
     * @return
     */
    List<Address> getList();

    /**
     * 添加用户地址
     * @param address
     * @return
     */
    Boolean add(Address address);

    /**
     * 修改用户地址
     * @param address
     * @return
     */
    Boolean update(Address address);

    /**
     * 删除用户地址
     * @param id
     * @return
     */
    Boolean delete(Long id);
}
