package com.ruoyi.uni.uni.domain.user;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName PhoneInfo
 * @Description
 * <AUTHOR>
 * @Date 2025/5/16 下午2:11
 */
@Data
@Schema(name="PhoneInfo对象", description="用户手机号信息")
public class PhoneInfo {
    @Schema(name = "用户绑定的手机号（国外手机号会有区号）")
    private String phoneNumber;
    @Schema(name = "没有区号的手机号")
    private String 	purePhoneNumber;
    @Schema(name = "区号")
    private String countryCode;
    @Schema(name = "区号")
    private JSONObject watermark;
}
