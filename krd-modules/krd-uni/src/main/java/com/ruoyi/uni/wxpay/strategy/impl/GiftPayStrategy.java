package com.ruoyi.uni.wxpay.strategy.impl;

import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.system.api.domain.FrontGiftInfo;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.vo.GiftDeduVo;
import com.ruoyi.system.api.mapper.FrontGiftInfoMapper;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;
import com.ruoyi.uni.wxpay.strategy.PaymentStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 余额支付策略实现
 */
@Service
@RequiredArgsConstructor
public class GiftPayStrategy implements PaymentStrategy {

    private final FrontGiftInfoMapper frontGiftInfoMapper;

    @Override
    public OrderPayResultResponse pay(FrontOrders order, String ip) {
        OrderPayResultResponse response = new OrderPayResultResponse();
        response.setOrderNo(order.getOrderNumber());
        response.setPayType(getPayType());

        List<GiftDeduVo> giftList = order.getGiftList();
        BigDecimal totalGiftUsedAmount = BigDecimal.ZERO; // 初始化礼品卡总使用金额

        if (giftList != null && !giftList.isEmpty()) {
            for (GiftDeduVo gift : giftList) {
                FrontGiftInfo frontGiftInfo = frontGiftInfoMapper.selectById(gift.getId());
                BigDecimal usedAmount = gift.getAmount(); // 用户下单使用礼品卡金额
                BigDecimal balance = frontGiftInfo.getBalance();

                int compareResult = usedAmount.compareTo(balance);

                if (compareResult < 0) {
                    // 使用金额 小于 当前余额，仅做扣减
                    frontGiftInfo.setBalance(balance.subtract(usedAmount));
                    frontGiftInfo.setUseTime(LocalDateTime.now());
                    frontGiftInfoMapper.updateById(frontGiftInfo);
                    totalGiftUsedAmount = totalGiftUsedAmount.add(usedAmount); // 累加使用金额
                } else if (compareResult == 0) {
                    // 使用金额 等于 当前余额，扣减并设置状态为1
                    frontGiftInfo.setBalance(BigDecimal.ZERO);
                    frontGiftInfo.setStatus("1");
                    frontGiftInfo.setUseTime(LocalDateTime.now());
                    frontGiftInfoMapper.updateById(frontGiftInfo);
                    totalGiftUsedAmount = totalGiftUsedAmount.add(usedAmount); // 累加使用金额
                } else {
                    throw new GlobalException("礼品卡余额不足：" + frontGiftInfo.getId());
                }
            }
        }
        // 设置订单支付信息
        order.setPaid(true);
        order.setPayTime(LocalDateTime.now());
        order.setGiftDeduction(totalGiftUsedAmount); // 设置礼品卡抵扣总额
        return response;
    }

    @Override
    public String getPayType() {
        return PayConstants.PAY_TYPE_GIFT;
    }
}
