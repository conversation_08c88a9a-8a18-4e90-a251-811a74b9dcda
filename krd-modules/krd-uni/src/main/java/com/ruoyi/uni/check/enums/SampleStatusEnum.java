package com.ruoyi.uni.check.enums;

import lombok.Getter;

/**
 * 样本状态
 * <AUTHOR>
 * @date 2025/5/21 18:52
 */
public enum SampleStatusEnum {
    STATUS_01("01","未绑定","默认状态"),
    STATUS_02("02","已绑定","用户扫描二维码绑定成功后"),
    STATUS_03("03","已回寄","物流状态，京东取件后"),
    STATUS_04("04","已接收","转管码已打印，扫码枪扫描打印后"),
    STATUS_05("05","不合格","处理样本标记不合格"),
    STATUS_06("06","检测中","处理样本标记合格"),
    ;
    @Getter
    private String code;
    private String name;
    private String desc;

    SampleStatusEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }
}
