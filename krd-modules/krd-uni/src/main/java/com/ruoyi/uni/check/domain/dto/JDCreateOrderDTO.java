package com.ruoyi.uni.check.domain.dto;

import com.alibaba.fastjson2.util.DateUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5 18:25
 */
@Data
public class JDCreateOrderDTO {
    private List<String> itemCodes;
    private String orderId = IdWorker.get32UUID();//创建订单号
    private CreateContactDTO senderContact;
    private CreateContactDTO receiverContact;
    //预约时间
    private String dateKey;
    //期望揽收开始时间
    private String startTime;
    //期望揽收结束时间
    private String endTime;

    public Long getPickupStartTime() {
        return DateUtils.parseDate(dateKey + " " + startTime).getTime();
    }

    public Long getPickupEndTime() {
        return DateUtils.parseDate(dateKey + " " + endTime).getTime();
    }
}
