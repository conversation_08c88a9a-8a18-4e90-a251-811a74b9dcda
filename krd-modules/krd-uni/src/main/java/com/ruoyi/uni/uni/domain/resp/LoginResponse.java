package com.ruoyi.uni.uni.domain.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName LoginResponse
 * @Description 返回登陆信息参数
 * <AUTHOR>
 * @Date 2025/5/15 下午4:59
 */
@Data
@Schema(name = "登录返回参数")
public class LoginResponse implements Serializable {

    @Schema(name = "token")
    private String token;

    @Schema(name = "token过期时间")
    private Long expiresTime;

    @Schema(name = "用户id")
    private Long id;

    @Schema(name = "用户名")
    private String userName;

    @Schema(name = "用户头像")
    private String userIcon;

    @Schema(title = "手机号码")
    private String userMobile;

    @Excel(name = "用户IP")
    private String userIp;

    /** 周期长度 */
    @Excel(name = "周期长度")
    @Schema(title = "周期长度")
    private Long periodCycle;

    /** 经期长度 */
    @Excel(name = "经期长度")
    @Schema(title = "经期长度")
    private Long periodLength;

    /** 最近一次月经开始日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "最近一次月经开始日")
    @Excel(name = "最近一次月经开始日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nearPeriodDate;

}
