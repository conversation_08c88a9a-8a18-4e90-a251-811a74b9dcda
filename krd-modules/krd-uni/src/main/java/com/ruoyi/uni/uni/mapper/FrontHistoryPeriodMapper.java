package com.ruoyi.uni.uni.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.uni.uni.domain.FrontHistoryPeriod;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户历史经期记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Mapper
public interface FrontHistoryPeriodMapper extends BaseMapper<FrontHistoryPeriod>
{
    /**
     * 查询用户历史经期记录
     *
     * @param id 用户历史经期记录主键
     * @return 用户历史经期记录
     */
    public FrontHistoryPeriod selectFrontHistoryPeriodById(Long id);

    /**
     * 查询用户历史经期记录列表
     *
     * @param frontHistoryPeriod 用户历史经期记录
     * @return 用户历史经期记录集合
     */
    public List<FrontHistoryPeriod> selectFrontHistoryPeriodList(FrontHistoryPeriod frontHistoryPeriod);

    /**
     * 新增用户历史经期记录
     *
     * @param frontHistoryPeriod 用户历史经期记录
     * @return 结果
     */
    public int insertFrontHistoryPeriod(FrontHistoryPeriod frontHistoryPeriod);

    /**
     * 修改用户历史经期记录
     *
     * @param frontHistoryPeriod 用户历史经期记录
     * @return 结果
     */
    public int updateFrontHistoryPeriod(FrontHistoryPeriod frontHistoryPeriod);

    /**
     * 删除用户历史经期记录
     *
     * @param id 用户历史经期记录主键
     * @return 结果
     */
    public int deleteFrontHistoryPeriodById(Long id);

    /**
     * 批量删除用户历史经期记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontHistoryPeriodByIds(Long[] ids);


    FrontHistoryPeriod selectFrontHistoryPeriodByMonth(@Param("inputMonth") String inputMonth, @Param("uid") Long uid);

    FrontHistoryPeriod selectInitInfo(@Param("uid") Long uid);
}
