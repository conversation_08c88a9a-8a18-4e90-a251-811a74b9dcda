package com.ruoyi.uni.uni.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName Address
 * @Description
 * <AUTHOR>
 * @Date 2025/5/19 下午4:04
 */
@Schema(name = "用户地址")
@Data
@TableName("front_address")
public class Address implements Serializable {
    @Schema(title = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @Schema(title = "用户id")
    private Long userId;
    @Schema(title = "收货人")
    private String name;
    @Schema(title = "手机号码")
    private String mobile;
    @Schema(title = "所在地区")
    private String area;
    @Schema(title = "详细地址")
    private String address;
    @Schema(title = "地址标签")
    private String tag;
    @Schema(title = "是否默认 0 - 否  1-是")
    private Long isDefault;
    @Schema(title = "是否删除 0-false 1-true")
    private Long isDel;
}
