package com.ruoyi.uni.uni.service;

import com.ruoyi.uni.uni.domain.FrontShopCollect;
import com.ruoyi.uni.uni.domain.vo.FrontShopCollectVo;
import com.ruoyi.uni.uni.domain.vo.FrontShopCartVo;

import java.util.List;

/**
 * @ClassName IFrontShopCollectService
 * @Description 收藏服务类
 * <AUTHOR>
 * @Date 2025/6/9 上午11:02
 */
public interface IFrontShopCollectService {

    /**
     * 查询收藏列表
     * @return
     */
    List<FrontShopCartVo.CartList> selectFrontShopCollectList(FrontShopCollectVo.SearchParam vo);

    /**
     * 添加收藏
     * @param frontShopCollect
     * @return true/false
     */
    Boolean addOrDelete(FrontShopCollect frontShopCollect);

    /**
     * 移除收藏 批量移除
     * @param ids
     * @return true/false
     */
    Boolean delete(Long[] ids);

    /**
     * 移除对应商城收藏
     * @param goodsId
     * @return true/false
     */
    Boolean deleteByGoodsId(Long goodsId);

    /**
     * 批量添加收藏
     * @param vo
     * @return true/false
     */
    Boolean addBatch(FrontShopCollectVo.CartListInfo vo);
}
