package com.ruoyi.uni.wxpay.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description: 微信开放平台获取accessToken对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WeChatOauthToken implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(name = "accessToken接口调用凭证")
    @TableField(value = "access_token")
    private String accessToken;

    @Schema(name = "access_token 接口调用凭证超时时间，单位（秒）")
    @TableField(value = "expires_in")
    private String expiresIn;

    @Schema(name = "用户刷新access_token")
    @TableField(value = "refresh_token")
    private String refreshToken;

    @Schema(name = "用户OpenId")
    @TableField(value = "openid")
    private String openId;

    @Schema(name = "用户授权的作用域，使用逗号（,）分隔")
    private String scope;

}
