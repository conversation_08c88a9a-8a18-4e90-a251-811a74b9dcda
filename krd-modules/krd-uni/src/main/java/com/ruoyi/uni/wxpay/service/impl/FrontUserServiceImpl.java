package com.ruoyi.uni.wxpay.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.resp.FrontUserMoneyResp;
import com.ruoyi.system.api.domain.user.StatisticsInfo;
import com.ruoyi.system.api.domain.vo.FrontEntity;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.uni.wxpay.service.IFrontUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户Service业务层处理
 *
 * @date 2025-05-09
 */
@Service
public class FrontUserServiceImpl implements IFrontUserService
{
    @Autowired
    private FrontUserMapper frontUserMapper;

    /**
     * 查询用户
     *
     * @param id 用户主键
     * @return 用户
     */
    @Override
    public FrontUser selectFrontUserById(Long id)
    {
        // 用户信息
        FrontUser frontUser = frontUserMapper.selectFrontUserById(id);
        // 统计信息
        StatisticsInfo statisticsInfo = frontUserMapper.selectStatisticsInfoByUserId(id);
        frontUser.setStatisticsInfo(statisticsInfo);
        // 收货地址
        frontUser.setFrontAddress(frontUserMapper.selectFrontAddressByUserId(id));
        // todo购买记录。新开接口

        // 检测报告
        frontUser.setCheckPackage(frontUserMapper.selectCheckPackage(id));
        return frontUser;
    }

    /**
     * 查询用户列表
     *
     * @param frontUser 用户
     * @return 用户
     */
    @Override
    public List<FrontEntity> selectFrontUserList(FrontEntity frontUser)
    {
        return frontUserMapper.selectFrontUserList(frontUser);
    }

    /**
     * 新增用户
     *
     * @param frontUser 用户
     * @return 结果
     */
    @Override
    public int insertFrontUser(FrontUser frontUser)
    {
        return frontUserMapper.insertFrontUser(frontUser);
    }

    /**
     * 修改用户
     *
     * @param frontUser 用户
     * @return 结果
     */
    @Override
    public int updateFrontUser(FrontUser frontUser)
    {
        return frontUserMapper.updateFrontUser(frontUser);
    }

    /**
     * 批量删除用户
     *
     * @param ids 需要删除的用户主键
     * @return 结果
     */
    @Override
    public int deleteFrontUserByIds(Long[] ids)
    {
        return frontUserMapper.deleteFrontUserByIds(ids);
    }

    /**
     * 删除用户信息
     *
     * @param id 用户主键
     * @return 结果
     */
    @Override
    public int deleteFrontUserById(Long id)
    {
        return frontUserMapper.deleteFrontUserById(id);
    }
    @Override
    public List<FrontUserMoneyResp> listMoneyPackageInfo(String type, Long userId) {
         return frontUserMapper.listMoneyPackageInfo(type,userId);
    }

    @Override
    public FrontUser queryFrontUserById(Long id) {
        return frontUserMapper.selectFrontUserById(id);
    }
}
