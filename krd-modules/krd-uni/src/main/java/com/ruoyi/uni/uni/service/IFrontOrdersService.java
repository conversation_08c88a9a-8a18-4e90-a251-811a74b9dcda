package com.ruoyi.uni.uni.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.FrontOrdersInvoice;
import com.ruoyi.system.api.domain.vo.FrontOrdersVo;

import java.util.List;

/**
 * 订单总Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IFrontOrdersService extends IService<FrontOrders>
{
    /**
     * 查询订单总
     *
     * @param id 订单总主键
     * @return 订单总
     */
    public FrontOrders selectFrontOrdersById(Long id);


    /**
     * 查询订单总列表
     *
     * @param vo 订单查询参数
     * @return 订单总集合
     */
    public List<FrontOrders> selectFrontOrdersList(FrontOrdersVo.FrontOrdersSearch vo);


    /**
     * 小程序查询订单总列表
     *
     * @param vo 订单查询参数
     * @return 订单总集合
     */
    public List<FrontOrders> selectUniOrdersList(FrontOrdersVo.UniFrontOrdersSearch vo);

    /**
     * 新增订单总
     *
     * @param frontOrders 订单总
     * @return 结果
     */
    public int insertFrontOrders(FrontOrders frontOrders);

    /**
     * 修改订单总
     *
     * @param frontOrders 订单总
     * @return 结果
     */
    public int updateFrontOrders(FrontOrders frontOrders);

    int updateFrontOrdersStatus(FrontOrders frontOrders);

    /**
     * 批量删除订单总
     *
     * @param ids 需要删除的订单总主键集合
     * @return 结果
     */
    public int deleteFrontOrdersByIds(Long[] ids);

    /**
     * 删除订单总信息
     *
     * @param id 订单总主键
     * @return 结果
     */
    public int deleteFrontOrdersById(Long id);

    /**
     * 根据订单编号查询
     * @param orderNum
     * @return
     */
    FrontOrders selectOrderNum(String orderNum);

    /**
     * 订单下单
     * @param vo
     * @return
     */
    String addOrder(FrontOrdersVo.FrontOrdersAddInfo vo);

    /**
     * 通过订单号查询下单返回参数
     * @param orderNo
     * @return
     */
    FrontOrdersVo.FrontOrdersAddResult selectOrderDetailByOrderNo(String orderNo);

    /**
     * 取消订单
     * @param orderNo
     * @return
     */
    Boolean cancelOrder(String orderNo);

    /**
     * 修改订单地址
     * @param vo
     * @return
     */
    Boolean updateAddress(FrontOrdersVo.UpdateAddressInfo vo);

    /**
     * 完成订单
     * @param orderNo
     * @return
     */
    Boolean finishOrder(String orderNo);

    /**
     * 订单评价
     * @param vo
     * @return
     */
    Boolean comment(FrontOrdersVo.CommentInfo vo);

    /**
     * 支付订单
     * @param vo
     * @return
     */
    Boolean payment(FrontOrdersVo.UpdateAddressInfo vo);

    /**
     * 订单退款申请提交
     * @param vo
     * @return
     */
    String refundOrder(FrontOrdersVo.RefundInfo vo);

    /**
     * 订单退款申请查询
     * @param vo
     * @return
     */
    FrontOrdersVo.RefundReturnInfo getAfterInfo(FrontOrdersVo.RefundList vo);

    /**
     * 查看订单评价
     * @param vo
     */
    FrontOrdersVo.CommentInfo getComment(FrontOrdersVo.CommentInfo vo);

    /**
     * 查看订单退款详情
     * @param vo
     * @return
     */
    FrontOrdersVo.RefundDetailInfo getRefundInfo(String orderNo);

    /**
     * 订单撤销售后
     */
    Boolean cancelRefundOrder(String orderNo);

    /**
     * 录入订单退货快递信息
     */
    Boolean updateRefundOrder(FrontOrdersVo.ReturnExpressInfo vo);

    /**
     * 删除订单
     */
    Boolean deleteOrder(String orderNo);

    /**
     * 订单发票申请
     * @param invoice 发票参数
     * @return
     */
    Boolean invoiceApply(FrontOrdersInvoice invoice);

    /**
     *查询用户最新发票信息
     * @return
     */
    FrontOrdersInvoice selectInvoiceByUserId();

    /**
     * 订单——再来一单
     * @param orderNo 订单编号
     * @return orderno
     */
    String againOrder(String orderNo);

    /**
     * 通过订单id查询发票
     * @param orderId 订单id
     * @return 发票信息
     */
    FrontOrdersVo.InvoiceInfo selectInvoiceByOrderId(Long orderId);
}
