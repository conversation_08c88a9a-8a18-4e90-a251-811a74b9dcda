package com.ruoyi.uni.uni.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName FrontShopCartVo
 * @Description 购物车 交互参数
 * <AUTHOR>
 * @Date 2025/6/9 下午12:02
 */
public interface FrontShopCartVo {

    @Data
    @Schema(description = "购物车/收藏列表参数")
    class CartList {
        @Schema(description = "购物车id")
        private Long id;
        @Schema(description = "商品类型")
        private Integer goodsType;
        @Schema(description = "商品id")
        private Long goodsId;
        @Schema(description = "规格id")
        private Long specId;
        @Schema(description = "数量")
        private Long amount;
        @Schema(description = "商品名称")
        private String goodsName;
        @Schema(description = "规格名称")
        private String specName;
        @Schema(description = "图片")
        private String image;
        @Schema(description = "价格")
        private BigDecimal price;
    }

    @Data
    @Schema(description = "购物车列表参数")
    class CartListInfo {
        private List<CartList> voList;
    }
}
