package com.ruoyi.uni.uni.service;

import com.ruoyi.uni.uni.domain.FrontHistoryPeriod;

import java.util.List;

/**
 * 用户历史经期记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IFrontHistoryPeriodService
{
    /**
     * 查询用户历史经期记录
     *
     * @param id 用户历史经期记录主键
     * @return 用户历史经期记录
     */
    public FrontHistoryPeriod selectFrontHistoryPeriodById(Long id);

    /**
     * 查询用户历史经期记录列表
     *
     * @param frontHistoryPeriod 用户历史经期记录
     * @return 用户历史经期记录集合
     */
    public List<FrontHistoryPeriod> selectFrontHistoryPeriodList(FrontHistoryPeriod frontHistoryPeriod);

    /**
     * 新增用户历史经期记录
     *
     * @param frontHistoryPeriod 用户历史经期记录
     * @return 结果
     */
    public int insertFrontHistoryPeriod(FrontHistoryPeriod frontHistoryPeriod);

    /**
     * 修改用户历史经期记录
     *
     * @param frontHistoryPeriod 用户历史经期记录
     * @return 结果
     */
    public int updateFrontHistoryPeriod(FrontHistoryPeriod frontHistoryPeriod);

    /**
     * 批量删除用户历史经期记录
     *
     * @param ids 需要删除的用户历史经期记录主键集合
     * @return 结果
     */
    public int deleteFrontHistoryPeriodByIds(Long[] ids);

    /**
     * 删除用户历史经期记录信息
     *
     * @param id 用户历史经期记录主键
     * @return 结果
     */
    public int deleteFrontHistoryPeriodById(Long id);
}
