package com.ruoyi.uni.check.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.uni.check.domain.dto.JDCreateOrderDTO;
import com.ruoyi.uni.check.domain.dto.SampleBindSaveDTO;
import com.ruoyi.uni.check.domain.entity.FrontNumberEntity;
import com.ruoyi.uni.check.domain.vo.AddressVO;
import com.ruoyi.uni.check.domain.vo.BindPakeageVO;
import com.ruoyi.uni.check.domain.vo.ReportPreviewVO;
import com.ruoyi.uni.check.domain.vo.SampleStatusVO;
import com.ruoyi.uni.check.domain.vo.SendPakeageVO;

import java.util.List;

/**
 * 编码管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-04 10:40:54
 */
public interface FrontNumberService extends IService<FrontNumberEntity> {

    BindPakeageVO getPakeageByItemCode(String itemCode);

    void pakeageSave(SampleBindSaveDTO saveDTO);

    List<SendPakeageVO> sendList();

    JSONObject getAddressList();

    JSONObject reshipping(JDCreateOrderDTO createOrderDTO);

    List<SampleStatusVO> getSampleStatusList(String searchValue);

    JSONObject getSampleStatusByItemCode(String itemCode);

    ReportPreviewVO reportPreview(String itemCode);
}

