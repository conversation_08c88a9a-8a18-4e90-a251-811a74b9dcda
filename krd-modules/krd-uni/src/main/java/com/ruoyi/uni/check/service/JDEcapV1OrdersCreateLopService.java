package com.ruoyi.uni.check.service;

import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonCreateOrderResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.Contact;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderStatusV1.CommonOrderStatusResponse;
import com.lop.open.api.sdk.response.ECAP.EcapV1OrdersCreateLopResponse;
import com.lop.open.api.sdk.response.ECAP.EcapV1OrdersStatusGetLopResponse;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.uni.check.domain.dto.JDCreateOrderDTO;

/**
 * <AUTHOR>
 * @date 2025/6/5 15:36
 */
public interface JDEcapV1OrdersCreateLopService {

    R<CommonCreateOrderResponse> ecapV1OrdersCreateLop(JDCreateOrderDTO createOrderDTO);

    R<CommonOrderStatusResponse> ecapV1OrdersStatusGetLopByWaybillCode(String waybillCode);

    R<CommonOrderStatusResponse> ecapV1OrdersStatusGetLopByOrderCode(String orderCode);
}
