package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.uni.uni.domain.resp.WeChatLoginResponse;
import com.ruoyi.uni.uni.service.IWeChatLoginService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;


/**
 * @ClassName WeChatLoginController
 * @Description 小程序微信授权登陆接口控制器
 * <AUTHOR>
 * @Date 2025/5/15 下午4:25
 */
@RestController
@Tag(name = "微信授权", description = "登录接口")
@RequestMapping("/weChatLogin")
public class WeChatLoginController extends BaseController {

    @Autowired
    private IWeChatLoginService weChatLoginService;

    @Operation(description = "微信登录小程序授权登录")
    @PostMapping(value = "/login")
    public AjaxResult programLogin(
            @RequestBody WeChatLoginResponse response, HttpServletRequest httpServletRequest) {
        return success(weChatLoginService.weChatAuthorizeProgramLogin(response,httpServletRequest));
    }

    @Operation(description =  "判断微信登录token是否过期")
    @GetMapping(value = "/isExpire")
    public AjaxResult isExpire(HttpServletRequest httpServletRequest) {
        String token = httpServletRequest.getHeader("authorization").split(" ")[1];
        return success(weChatLoginService.getWeChatAuthorizeCode(token));
    }
}
