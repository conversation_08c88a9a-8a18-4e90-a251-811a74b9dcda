package com.ruoyi.uni.uni.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.SysConfig;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.system.api.mapper.SysConfigMapper;
import com.ruoyi.uni.uni.domain.FrontSign;
import com.ruoyi.uni.uni.domain.vo.FrontSignVo;
import com.ruoyi.uni.uni.mapper.FrontSignMapper;
import com.ruoyi.uni.uni.service.IFrontSignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户签到Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Service
public class FrontSignServiceImpl extends ServiceImpl<FrontSignMapper, FrontSign> implements IFrontSignService
{
    @Autowired
    private FrontSignMapper frontSignMapper;

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Autowired
    private SysConfigMapper configMapper;



    @Override
    public FrontSignVo selectFrontSignList() {
        Long userId = SecurityUtils.getUserId();
        LambdaQueryWrapper<FrontUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontUser::getId, userId);
        FrontUser frontUser = frontUserMapper.selectOne(queryWrapper);
        FrontSignVo frontSignVo = new FrontSignVo();
        if (frontUser!= null){
            frontSignVo.setPoint(frontUser.getIntegral());//用户积分
            LambdaQueryWrapper<FrontSign> signQueryWrapper = new LambdaQueryWrapper<>();
            signQueryWrapper.eq(FrontSign::getUserId, userId);
            FrontSign frontSign = frontSignMapper.selectOne(signQueryWrapper);
            if (frontSign!= null){
                frontSignVo.setSignCount(frontSign.getContinuousDays()==null?0L:frontSign.getContinuousDays());//连续签到次数
                frontSignVo.setSign(frontSign.getCheckInDate().toLocalDate().isEqual(LocalDate.now()));
            }else{
                frontSignVo.setSignCount(0L);
                frontSignVo.setSign(false);
                frontSignVo.setPoint(frontUser.getIntegral() == null ? BigDecimal.valueOf(0) : frontUser.getIntegral());//用户积分
            }
        }
        SysConfig retConfig = configMapper.checkConfigKeyUnique("points_config");
        if (StringUtils.isNotNull(retConfig))
        {
            //用户规则积分
            String configValue = retConfig.getConfigValue();
            JsonObject jsonObject = JsonParser.parseString(configValue).getAsJsonObject();
            int dailySignIn = jsonObject.get("dailySignIn").getAsInt();
            int continuousSignInDays = jsonObject.get("continuousSignInDays").getAsInt();
            int continuousSignInReward = jsonObject.get("continuousSignInReward").getAsInt();
            frontSignVo.setDailySignIn(dailySignIn);
            frontSignVo.setContinuousSignInDays(continuousSignInDays);
            frontSignVo.setContinuousSignInReward(continuousSignInReward);
        }

        return frontSignVo;
    }

    /**
     * 新增用户签到
     *
     * @param frontSign 用户签到
     * @return 结果
     */
    @Override
    public int insertFrontSign(FrontSign frontSign)
    {
        //todo 这里需要获取系统配置的规则进行加
        Long userId = SecurityUtils.getUserId();
        LambdaQueryWrapper<FrontUser> userWrapper =  new LambdaQueryWrapper<>();
        userWrapper.eq(FrontUser::getId, userId);
        FrontUser frontUser = frontUserMapper.selectOne(userWrapper);
        LambdaQueryWrapper<FrontSign> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontSign::getUserId, userId);
        FrontSign sign = frontSignMapper.selectOne(queryWrapper);
        frontSign.setCheckInDate(LocalDateTime.now());
        SysConfig retConfig = configMapper.checkConfigKeyUnique("points_config");
        if (StringUtils.isNotNull(retConfig))
        {
            //用户规则积分
            String configValue = retConfig.getConfigValue();
            JsonObject jsonObject = JsonParser.parseString(configValue).getAsJsonObject();
            int continuousSignInDays = jsonObject.get("continuousSignInDays").getAsInt();
            int continuousSignInReward = jsonObject.get("continuousSignInReward").getAsInt();
            if(frontSign.getContinuousDays() > continuousSignInDays){
                frontUser.setIntegral(frontUser.getIntegral() == null ? BigDecimal.valueOf(0) : frontUser.getIntegral().add(BigDecimal.valueOf(continuousSignInReward)));
            }
            int dailySignIn = jsonObject.get("dailySignIn").getAsInt();
            frontUser.setIntegral(frontUser.getIntegral() == null ? BigDecimal.valueOf(0) : frontUser.getIntegral().add(BigDecimal.valueOf(dailySignIn)));
        }

        if (sign == null){
            frontSign.setUserId(userId);
            frontSignMapper.insertFrontSign(frontSign);
        }
        frontUserMapper.updateById(frontUser);
        return frontSignMapper.updateFrontSign(frontSign);
    }

}
