package com.ruoyi.uni.wechat.service;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.uni.uni.domain.user.PhoneInfo;
import com.ruoyi.uni.uni.domain.vo.WeChatMiniAuthorizeVo;
import com.ruoyi.uni.wxpay.pojo.WeChatOauthToken;
import com.ruoyi.system.api.vo.CreateOrderRequestVo;
import com.ruoyi.system.api.vo.CreateOrderResponseVo;
import com.ruoyi.uni.wxpay.vo.TemplateMessageVo;

import java.util.List;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description: 微信公用服务
 */
public interface WechatNewService {

    /**
     * 小程序获取用户手机号
     * @param accessToken
     * @param code
     * @return
     */
    PhoneInfo getUserPhone(String accessToken, String code);

    /**
     * 获取公众号accessToken
     * @return 公众号accessToken
     */
    String getPublicAccessToken();

    /**
     * 获取小程序accessToken
     * @return 小程序accessToken
     */
    String getMiniAccessToken();

    /**
     * 获取开放平台access_token
     * 通过 code 获取
     * 公众号使用
     * @return 开放平台accessToken对象
     */
    WeChatOauthToken getOauth2AccessToken(String code);


    /**
     * 小程序登录凭证校验
     * @return 小程序登录校验对象
     */
    WeChatMiniAuthorizeVo miniAuthCode(String code);


    /**
     * 生成小程序码
     * @param page 必须是已经发布的小程序存在的页面
     * @param scene 最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符
     * @return 小程序码
     */
    String createQrCode(String page, String scene);


    //充值预下单
    CreateOrderResponseVo  unifiedOrder(CreateOrderRequestVo unifiedorderVo);
    /**
     * 微信小程序发送订阅消息
     * @param templateMessage 消息对象
     * @return 是否发送成功
     */
    Boolean sendMiniSubscribeMessage(TemplateMessageVo templateMessage);

    /**
     * 获取微信公众号自定义菜单配置
     * （使用本自定义菜单查询接口可以获取默认菜单和全部个性化菜单信息）
     * @return 公众号自定义菜单
     */
    JSONObject getPublicCustomMenu();

    /**
     * 创建微信自定义菜单
     * @param data 菜单json字符串
     * @return 创建结果
     */
    Boolean createPublicCustomMenu(String data);

    /**
     * 删除微信自定义菜单
     * @return 删除结果
     */
    Boolean deletePublicCustomMenu();

    /**
     * 企业号上传其他类型永久素材
     * 获取url
     * @param type 素材类型:图片（image）、语音（voice）、视频（video），普通文件(file)
     */
    String qyapiAddMaterialUrl(String type);

    /**
     * 微信申请退款
     * @param wxRefundVo 微信申请退款对象
     * @param path 商户p12证书绝对路径
     * @return 申请退款结果对象
     */
//    WxRefundResponseVo payRefund(WxRefundVo wxRefundVo, String path);



    /**
     * 删除微信公众号模板消息
     * @return Boolean
     */
    Boolean delPublicMyTemplate(String templateId);

    /**
     * 添加公众号模板消息
     * @param templateIdShort 模板库中模板的编号，有“TM**”和“OPENTMTM**”等形式
     * @return 公众号模板编号（自己的）
     */
    String apiAddPublicTemplate(String templateIdShort);


    /**
     * 删除微信小程序订阅消息
     * @return Boolean
     */
    Boolean delRoutineMyTemplate(String priTmplId);



    /**
     * 添加小程序订阅消息
     * @param tempKey 模板编号
     * @param kidList 小程序订阅消息模板kid数组
     * @return 小程序订阅消息模板编号（自己的）
     */
    String apiAddRoutineTemplate(String tempKey, List<Integer> kidList);
}
