package com.ruoyi.uni.uni.service.Impl;

import com.ruoyi.system.api.domain.FrontChoice;
import com.ruoyi.system.api.mapper.FrontChoiceMapper;
import com.ruoyi.uni.uni.service.IFrontChoiceService;
import com.ruoyi.uni.uni.utils.oss.OssUrlCleanerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 精选内容Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class FrontChoiceServiceImpl implements IFrontChoiceService
{
    @Autowired
    private FrontChoiceMapper frontChoiceMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    /**
     * 查询精选内容
     *
     * @param id 精选内容主键
     * @return 精选内容
     */
    @Override
    public FrontChoice selectFrontChoiceById(Long id)
    {
        FrontChoice frontChoice = frontChoiceMapper.selectFrontChoiceById(id);
        frontChoice.setCover(ossUrlCleanerUtil.getSignatureUrl(frontChoice.getCover()));
        frontChoice.setInfo(ossUrlCleanerUtil.getSignatureEditor(frontChoice.getInfo()));
        return frontChoice;
    }

    /**
     * 查询精选内容列表
     *
     * @param frontChoice 精选内容
     * @return 精选内容
     */
    @Override
    public List<FrontChoice> selectFrontChoiceList(FrontChoice frontChoice)
    {
        List<FrontChoice> frontChoices = frontChoiceMapper.selectFrontChoiceList(frontChoice);
        for (FrontChoice frontChoiceList : frontChoices) {
            frontChoiceList.setCover(ossUrlCleanerUtil.getSignatureUrl(frontChoiceList.getCover()));
        }
        return frontChoices;
    }

}
