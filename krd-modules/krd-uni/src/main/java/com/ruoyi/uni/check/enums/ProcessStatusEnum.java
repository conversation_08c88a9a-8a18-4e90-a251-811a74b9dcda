package com.ruoyi.uni.check.enums;

import lombok.Getter;

/**
 * 流程总状态
 * <AUTHOR>
 * @date 2025/5/21 18:45
 */
public enum ProcessStatusEnum {

    STATUS_01("01","已生成",""),
    STATUS_02("02","套码已打印",""),
    STATUS_03("03","已绑定",""),
    STATUS_04("04","已回寄（物流状态）",""),
    STATUS_05("05","已接收（转管码已打印）",""),
    STATUS_06("06","已处理（不合格，合格）",""),
    STATUS_07("07","未审核（已出报告）",""),
    STATUS_08("08","已审核（不通过，通过）",""),
    STATUS_09("09","已复核（不通过，已发布）",""),
    ;
    @Getter
    private String code;
    private String name;
    private String desc;

    ProcessStatusEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }
}
