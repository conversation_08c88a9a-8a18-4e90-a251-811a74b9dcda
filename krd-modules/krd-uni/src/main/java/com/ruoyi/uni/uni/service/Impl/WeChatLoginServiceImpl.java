package com.ruoyi.uni.uni.service.Impl;

import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.domain.FrontLoginUser;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.uni.uni.domain.resp.LoginResponse;
import com.ruoyi.uni.uni.domain.resp.WeChatLoginResponse;
import com.ruoyi.uni.uni.domain.user.FrontUserToken;
import com.ruoyi.uni.uni.domain.vo.WeChatMiniAuthorizeVo;
import com.ruoyi.uni.uni.mapper.FrontUserTokenMapper;
import com.ruoyi.uni.uni.service.IWeChatLoginService;
import com.ruoyi.uni.wechat.service.WechatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * @ClassName WeChatLoginServiceImpl
 * @Description 微信授权登录业务实现类
 * <AUTHOR>
 * @Date 2025/5/15 下午4:55
 */
@Slf4j
@Service
public class WeChatLoginServiceImpl implements IWeChatLoginService {

    @Autowired
    private FrontUserTokenMapper frontUserTokenMapper;

    @Autowired
    private WechatService wechatService;

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Autowired
    private TokenService tokenService;

    @Override
    @Transactional
    public LoginResponse weChatAuthorizeProgramLogin(WeChatLoginResponse response, HttpServletRequest httpServletRequest) {
        WeChatMiniAuthorizeVo weChatMiniAuthorizeVo = wechatService.miniAuthCode(response.getLoginCode());
        // 检测是否存在
        FrontUserToken userToken = frontUserTokenMapper.selectOpenIdOne(weChatMiniAuthorizeVo.getOpenId());
        // 判断是否存在用户信息
        if (!ObjectUtils.isEmpty(userToken)) {
            FrontUser user = frontUserMapper.selectFrontUserById(userToken.getUserId());
            if (user.getStatus()) {
                throw new RuntimeException("当前账户已冻结！");
            }
            // 记录用户登录ip
            user.setUserIp(httpServletRequest.getRemoteAddr());
            // 更新用户ip
            frontUserMapper.updateFrontUser(user);
            return weChatAuthorizeLogin(user,httpServletRequest.getRemoteAddr());
        }

        // 用户第一次登录

        // 解密手机号
        String phone = wechatService.decryptPhone(response.getPhoneCode());

        FrontUser frontUser = new FrontUser();
        frontUser.setUserName("微信用户");
        frontUser.setUserMobile(phone);
        frontUser.setUserIcon("https://lunawell-krd.oss-cn-chengdu.aliyuncs.com/public/66517bcc-76a5-4e29-acc6-d0745e3fc90e.png");
        frontUser.setUserIp(httpServletRequest.getRemoteAddr());

        int i = frontUserMapper.insertFrontUser(frontUser);

        // 通过手机号查询用户
        FrontUser user = frontUserMapper.selectPhoneOne(frontUser.getUserMobile());

        if (i <= 0){
            throw new RuntimeException("用户信息保存失败！");
        }

        // 保存FrontUserToken
        FrontUserToken frontUserToken = new FrontUserToken();
        frontUserToken.setOpenId(weChatMiniAuthorizeVo.getOpenId());
        frontUserToken.setUserId(user.getId());
        frontUserToken.setCreateTime(LocalDateTime.now());
        frontUserTokenMapper.insertFrontUserToken(frontUserToken);
        return weChatAuthorizeLogin(user,httpServletRequest.getRemoteAddr());
    }

    @Override
    public boolean getWeChatAuthorizeCode(String token) {
        return tokenService.isTokenExpired(token);
    }


    private LoginResponse weChatAuthorizeLogin(FrontUser user,String ip) {
        LoginResponse loginResponse = new LoginResponse();

        FrontLoginUser frontLoginUser = new FrontLoginUser();

        BeanUtils.copyProperties(user,frontLoginUser);

        LoginUser loginUser = new LoginUser();
        loginUser.setFrontUser(frontLoginUser);
        Date now = new Date();
        // expiration 设置为30天之后
        Date expiration = new Date(now.getTime() + 30L * 24 * 60 * 60 * 1000);
        // 获取token
        Map<String, Object> userToken = tokenService.createUserToken(loginUser);
        loginResponse.setToken(userToken.get("access_token").toString());
        loginResponse.setExpiresTime(expiration.getTime());
        loginResponse.setUserIcon(user.getUserIcon());
        loginResponse.setId(user.getId());
        loginResponse.setUserMobile(user.getUserMobile());
        loginResponse.setUserName(user.getUserName());
        loginResponse.setUserIp(ip);
        loginResponse.setPeriodCycle(user.getPeriodCycle() == null ? 0 : user.getPeriodCycle());
        loginResponse.setPeriodLength(user.getPeriodLength() == null ? 0 : user.getPeriodLength()) ;
        loginResponse.setNearPeriodDate(user.getNearPeriodDate() == null ? null : user.getNearPeriodDate());
        return loginResponse;
    }

}
