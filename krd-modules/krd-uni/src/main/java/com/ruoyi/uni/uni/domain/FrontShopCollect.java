package com.ruoyi.uni.uni.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 收藏对象 front_shop_collect
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@Schema(description = "收藏对象")
@TableName("front_shop_collect")
public class FrontShopCollect implements Serializable
{

    /** 记录ID */
    @Schema(description = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 关联用户ID */
    @Schema(description = "关联用户ID")
    private Long userId;

    /** 关联商品ID */
    @Schema(description = "关联商品ID")
    private Long goodsId;

    /** 关联规格ID */
    @Schema(description = "关联规格ID")
    private Long specId;

    /** 创建时间 */
    private LocalDateTime createTime;
    /** 更新时间 */
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;

}
