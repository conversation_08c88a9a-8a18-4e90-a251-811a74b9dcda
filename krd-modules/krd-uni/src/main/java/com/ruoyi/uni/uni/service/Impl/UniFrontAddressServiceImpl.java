package com.ruoyi.uni.uni.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.uni.uni.domain.Address;
import com.ruoyi.uni.uni.mapper.UniFrontAddressMapper;
import com.ruoyi.uni.uni.service.IUniFrontAddressService;
import com.ruoyi.uni.uni.service.IUniUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName UniFrontAddressServiceImpl
 * @Description 用户地址管理业务实现类
 * <AUTHOR>
 * @Date 2025/5/19 下午3:37
 */
@Slf4j
@Service
public class UniFrontAddressServiceImpl extends ServiceImpl<UniFrontAddressMapper, Address> implements IUniFrontAddressService {

    @Autowired
    private UniFrontAddressMapper uniFrontAddressMapper;

    @Autowired
    private IUniUserService uniUserService;

    @Override
    public List<Address> getList() {
        // 获取用户id
        Long userId = uniUserService.getUserId();

        LambdaQueryWrapper<Address> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Address::getUserId,userId);
        return this.list(queryWrapper);
    }

    @Override
    public Boolean add(Address address) {
        // 获取用户id
        Long userId = uniUserService.getUserId();
        if (address.getIsDefault() == 1){
            updateIsDefault(userId);
        }
        address.setUserId(userId);
        return this.save(address);
    }

    @Override
    public Boolean update(Address address) {
        // 获取用户id
        Long userId = uniUserService.getUserId();
        if (address.getIsDefault() == 1){
            updateIsDefault(userId);
        }
        return this.updateById(address);
    }

    @Override
    public Boolean delete(Long id) {
        return this.removeById(id);
    }

    private void updateIsDefault(Long id){
        // 查询是否存在默认地址
        LambdaQueryWrapper<Address> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Address::getUserId,id).eq(Address::getIsDefault,1);
        Address one = this.getOne(queryWrapper);
        if (one != null){
            one.setIsDefault(0L);
            this.updateById(one);
        }
    }
}
