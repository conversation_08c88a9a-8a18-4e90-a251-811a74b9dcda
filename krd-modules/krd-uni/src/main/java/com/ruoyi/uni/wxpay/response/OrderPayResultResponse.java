package com.ruoyi.uni.wxpay.response;

import com.ruoyi.uni.wxpay.vo.WxPayJsResultVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description:
 */
@Data
public class OrderPayResultResponse implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(name = "支付状态")
    private Boolean status;

    @Schema(name = "微信调起支付参数对象")
    private WxPayJsResultVo jsConfig;

    @Schema(name = "支付类型")
    private String payType;

    @Schema(name = "订单编号")
    private String orderNo;

    @Schema(name = "订单支付宝支付")
    private String alipayRequest;

}
