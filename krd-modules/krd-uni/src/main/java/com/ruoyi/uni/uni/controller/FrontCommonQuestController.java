package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.CblTableDataInfo;
import com.ruoyi.system.api.domain.FrontCommonQuest;
import com.ruoyi.uni.uni.service.IFrontCommonQuestService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 常见问题Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/quest")
@Tag(name = "常见问题Controller", description = "常见问题Controller")
public class FrontCommonQuestController extends BaseController
{
    @Autowired
    private IFrontCommonQuestService frontCommonQuestService;

    /**
     * 查询常见问题列表
     */
    @GetMapping("/list")
    @Tag(name = "常见问题列表", description = "常见问题列表")
    public CblTableDataInfo list(FrontCommonQuest frontCommonQuest)
    {
        startPage();
        List<FrontCommonQuest> list = frontCommonQuestService.selectFrontCommonQuestList(frontCommonQuest);
        return getTableUni(list);
    }
}
