package com.ruoyi.uni.check.domain.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.core.util.Json;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 参数配置
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-05 11:15:16
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName(value = "front_sys_setting",  autoResultMap = true)
public class FrontSysSettingEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Integer id;
	/**
	 * 1-通用配置 2-微信小程序 3-支付接口 4-云仓接口 5-检测机构 6-快递配置 7-客服系统 8-AI配置
	 */
	private Integer type;
	/**
	 * 配置内容json保存
	 */
	@TableField(typeHandler = JacksonTypeHandler.class)
	private JSONObject content;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;

}
