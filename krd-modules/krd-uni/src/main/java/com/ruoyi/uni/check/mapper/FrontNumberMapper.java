package com.ruoyi.uni.check.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.uni.check.domain.dto.SampleStatusDTO;
import com.ruoyi.uni.check.domain.entity.FrontNumberEntity;
import com.ruoyi.uni.check.domain.vo.AddressVO;
import com.ruoyi.uni.check.domain.vo.BindPakeageVO;
import com.ruoyi.uni.check.domain.vo.ReportBaseInfoVO;
import com.ruoyi.uni.check.domain.vo.SampleStatusVO;
import com.ruoyi.uni.check.domain.vo.SendPakeageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 编码管理
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-04 10:40:54
 */
@Mapper
public interface FrontNumberMapper extends BaseMapper<FrontNumberEntity> {

    BindPakeageVO getPakeageByItemCode(@Param("itemCode") String itemCode);

    List<SendPakeageVO> sendList(@Param("userid") Long userid);

    List<AddressVO> getAddressList(@Param("userid") Long userid);

    List<SampleStatusVO> getSampleStatusList(@Param("userid") Long userid,@Param("searchValue") String searchValue);

    SampleStatusDTO getSampleStatusByItemCode(@Param("userid") Long userid,@Param("itemCode") String itemCode);

    ReportBaseInfoVO selectReportBaseInfo(@Param("itemCode") String itemCode);
}
