package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.uni.uni.domain.FrontSign;
import com.ruoyi.uni.uni.domain.vo.FrontSignVo;
import com.ruoyi.uni.uni.service.IFrontSignService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户签到Controller
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@RestController
@RequestMapping("/sign")
@Tag(name = "用户签到", description = "用户签到")
public class FrontSignController extends BaseController
{
    @Autowired
    private IFrontSignService frontSignService;

    /**
     * 查询用户签到列表
     */
    @GetMapping("/getSignInfo")
    public AjaxResult list()
    {
        FrontSignVo  frontSignVo = frontSignService.selectFrontSignList();
        return success(frontSignVo);
    }


    /**
     * 新增用户签到
     */
    @Log(title = "用户签到", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontSign frontSign)
    {
        return success(frontSignService.insertFrontSign(frontSign));
    }
}
