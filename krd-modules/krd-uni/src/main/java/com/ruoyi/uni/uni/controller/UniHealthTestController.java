package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.api.domain.FrontHealthTest;
import com.ruoyi.uni.uni.service.IUniHealthTestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 健康管理测试Controller
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/health")
@Tag(name = "健康管理测试", description = "小程序用户档案问卷相关接口")
public class UniHealthTestController extends BaseController
{
    @Autowired
    private IUniHealthTestService frontHealthTestService;

    /**
     * 获取健康管理测试详细信息
     */
    @Operation(description = "获取档案问卷")
    @GetMapping(value = "/getList")
    public AjaxResult getInfo()
    {
        FrontHealthTest base = frontHealthTestService.selectFrontHealthTestById(0L);
        FrontHealthTest express = frontHealthTestService.selectFrontHealthTestById(1L);
        FrontHealthTest health = frontHealthTestService.selectFrontHealthTestById(2L);
        List<FrontHealthTest> result = Arrays.asList(base, express, health);
        return success(result);
    }

    /**
     * 根据用户id获取专属健康档案
     */
    @Operation(description = "根据用户id获取专属健康档案")
    @GetMapping(value = "/getHealthInfo")
    public AjaxResult getHealthInfo()
    {
        Map<String, String> belongHealthTest = frontHealthTestService.getBelongHealthTest();
        return success(belongHealthTest);
    }


}
