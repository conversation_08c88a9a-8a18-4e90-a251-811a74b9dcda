package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.api.domain.FrontFeedback;
import com.ruoyi.uni.uni.service.IFrontFeedbackService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 意见反馈Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/feedback")
@Tag(name = "意见反馈Controller", description = "意见反馈Controller")
public class FrontFeedbackController extends BaseController
{
    @Autowired
    private IFrontFeedbackService frontFeedbackService;

    /**
     * 新增意见反馈
     */
    @Log(title = "意见反馈", businessType = BusinessType.INSERT)
    @PostMapping
    @Tag(name = "新增意见反馈", description = "新增意见反馈")
    public AjaxResult add(@RequestBody FrontFeedback frontFeedback)
    {
        return toAjax(frontFeedbackService.insertFrontFeedback(frontFeedback));
    }
}
