package com.ruoyi.uni.uni.service;

import com.ruoyi.uni.uni.domain.FrontShopCart;
import com.ruoyi.uni.uni.domain.vo.FrontShopCartVo;

import java.util.List;

/**
 * @ClassName IFrontShopCartService
 * @Description 小程序购物车
 * <AUTHOR>
 * @Date 2025/6/9 上午10:21
 */
public interface IFrontShopCartService {

    /**
     * 查询购物车列表
     * @param
     * @return 购物车对象
     */
    List<FrontShopCartVo.CartList> selectFrontShopCartList();

    /**
     * 加入购物车
     * @param frontShopCart 购物车对象
     * @return true/false
     */
    Boolean add(FrontShopCart frontShopCart);

    /**
     * 修改购物车
     * @param frontShopCart 购物车对象
     * @return true/false
     */
    Boolean update(FrontShopCart frontShopCart);

    /**
     * 删除购物车
     * @param ids 购物车id集合
     * @return true/false
     */
    Boolean delete(Long[] ids);

    /**
     * 批量加入购物车
     * @param vo 购物车对象
     * @return true/false
     */
    Boolean addBatch(FrontShopCartVo.CartListInfo vo);

    /**
     * 用户购物车数量
     * @return 购物车数量
     */
    Long count();

}
