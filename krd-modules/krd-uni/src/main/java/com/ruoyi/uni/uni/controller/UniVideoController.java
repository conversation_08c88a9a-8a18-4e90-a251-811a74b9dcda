package com.ruoyi.uni.uni.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.CblTableDataInfo;
import com.ruoyi.system.api.domain.FrontVideo;
import com.ruoyi.uni.uni.service.IFrontVideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 采集视频Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/video")
@Tag(name = "采集视频管理", description = "采集视频管理")
public class UniVideoController extends BaseController
{
    @Autowired
    private IFrontVideoService frontVideoService;

    /**
     * 查询采集视频列表
     */
    @GetMapping("/list")
    @Operation(description = "查询采集视频列表")
    public CblTableDataInfo list(FrontVideo frontVideo)
    {
        startPage();
        List<FrontVideo> list = frontVideoService.selectFrontVideoList(frontVideo);
        return getTableUni(list);
    }


    /**
     * 获取采集视频详细信息
     */
    @GetMapping(value = "/{id}")
    @Operation(description = "获取采集视频详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) throws JsonProcessingException {
        return success(frontVideoService.selectFrontVideoById(id));
    }

}
