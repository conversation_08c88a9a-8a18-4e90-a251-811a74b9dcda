package com.ruoyi.uni.system.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.uni.system.service.GetConfigService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName GetConfigController
 * @Description 获取系统配置信息
 * <AUTHOR>
 * @Date 2025/6/12 上午11:45
 */
@RestController
@RequestMapping("/getConfig")
@Tag(name = "GetConfigController", description = "获取系统配置信息")
@Slf4j
public class GetConfigController extends BaseController {

    @Autowired
    private GetConfigService getConfigService;

    @GetMapping("/getConfigByKey/{configKey}")
    public AjaxResult getConfigByKey(@PathVariable String configKey) {
        return success("配置",getConfigService.selectConfigByKeyUniObj(configKey).getConfigValue());
    }
}
