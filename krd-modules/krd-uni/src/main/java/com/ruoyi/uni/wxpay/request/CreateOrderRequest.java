package com.ruoyi.uni.wxpay.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name="CreateOrderRequest对象", description="创建订单请求对象")
public class CreateOrderRequest implements Serializable {

    private static final long serialVersionUID = -6133994384185333872L;

    @NotBlank(message = "预下单订单号不能为空")
    private String preOrderNo;

    @Schema(title = "快递类型: 1-快递配送，2-到店自提")
    @NotNull(message = "快递类型不能为空")
    @Range(min = 1, max = 2, message = "未知的快递类型")
    private Integer shippingType;

    @Schema(description="支付金额")
    private BigDecimal payPrice;

    @Schema(title = "收货地址id")
    private Integer addressId;

    @Schema(title = "优惠券编号")
    private Integer couponId;

    @Schema(title = "支付类型:weixin-微信支付，yue-余额支付,alipay-支付宝支付")
    @NotBlank(message = "支付类型不能为空")
    private String payType;

    @Schema(title = "支付渠道:weixinh5-微信H5支付，public-公众号支付，routine-小程序支付，weixinAppIos-微信appios支付，weixinAppAndroid-微信app安卓支付,alipay-支付宝支付，appAliPay-App支付宝支付")
    @NotBlank(message = "支付渠道不能为空")
    private String payChannel;

    @Schema(title = "是否使用积分")
    @NotNull(message = "是否使用积分不能为空")
    private Boolean useIntegral;

    @Schema(title = "订单备注")
    private String mark;

    // 以下为到店自提参数

    @Schema(title = "自提点id")
    private Integer storeId;

    @Schema(title = "真实名称")
    private String realName;

    @Schema(title = "手机号码")
    private String phone;
}
