package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.CblTableDataInfo;
import com.ruoyi.uni.uni.domain.FrontShopCollect;
import com.ruoyi.uni.uni.domain.vo.FrontShopCollectVo;
import com.ruoyi.uni.uni.service.IFrontShopCollectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName FrontShopCollectController
 * @Description 小程序收藏控制类
 * <AUTHOR>
 * @Date 2025/6/9 上午11:28
 */
@Tag(name = "FrontShopCollectController", description ="小程序收藏控制类")
@Slf4j
@RestController
@RequestMapping("/uni/shopCollect")
public class FrontShopCollectController extends BaseController {

    @Autowired
    private IFrontShopCollectService IFrontShopCollectService;

    @Operation(summary = "查询收藏列表")
    @GetMapping("/list")
    public CblTableDataInfo list(FrontShopCollectVo.SearchParam vo){
        startPage();
        return getTableUni(IFrontShopCollectService.selectFrontShopCollectList(vo));
    }

    @Operation(summary = "添加/移除收藏")
    @PostMapping("/addOrDelete")
    public AjaxResult add(@RequestBody FrontShopCollect frontShopCollect){
        return success(IFrontShopCollectService.addOrDelete(frontShopCollect));
    }

    @Operation(summary = "删除收藏")
    @DeleteMapping("/deleteByIds/{ids}")
    public AjaxResult deleteByIds(@PathVariable Long[] ids){
        return IFrontShopCollectService.delete(ids) ? success() : error();
    }

    @Operation(summary = "删除对应收藏")
    @DeleteMapping("/deleteByGoodsId/{goodsId}")
    public AjaxResult deleteByGoodsId(@PathVariable Long goodsId){
        return success(IFrontShopCollectService.deleteByGoodsId(goodsId));
    }
    @Operation(summary = "批量添加收藏")
    @PostMapping("/addBatch")
    public AjaxResult addBatch(@RequestBody FrontShopCollectVo.CartListInfo vo){
        return IFrontShopCollectService.addBatch(vo) ? success() : error();
    }

}
