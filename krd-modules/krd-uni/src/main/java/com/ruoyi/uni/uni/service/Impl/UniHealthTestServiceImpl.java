package com.ruoyi.uni.uni.service.Impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.FrontHealthAnswer;
import com.ruoyi.system.api.domain.FrontHealthTest;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.mapper.FrontHealthAnswerMapper;
import com.ruoyi.system.api.mapper.FrontHealthTestMapper;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.uni.uni.service.IUniHealthTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: suhai
 * @Date: 2025/5/27
 * @Description:
 */
@Service
public class UniHealthTestServiceImpl  implements IUniHealthTestService {

    @Autowired
    private FrontHealthTestMapper frontHealthTestMapper;

    @Autowired
    private FrontHealthAnswerMapper frontHealthAnswerMapper;

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Override
    public FrontHealthTest selectFrontHealthTestById(Long id)
    {
        return frontHealthTestMapper.selectFrontHealthTestById(id);
    }

    @Override
    public Map<String, String> getBelongHealthTest() {
        Map<String, String> info=new HashMap<>();
        Long userId = SecurityUtils.getUserId();
        LambdaQueryWrapper<FrontHealthAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontHealthAnswer::getUid,userId);
        queryWrapper.eq(FrontHealthAnswer::getType,1);
        List<FrontHealthAnswer> frontHealthAnswer = frontHealthAnswerMapper.selectList(queryWrapper);
        if (!frontHealthAnswer.isEmpty()) {
            String content = frontHealthAnswer.get(0).getContent();
            List<String> plans = extractHealthPlans(content);
            String healthPlanStr = plans.isEmpty() ? "暂无健康建议" : String.join("#", plans);
            info.put("healthPlan", healthPlanStr);
            String userTags = getUserTags(content);
            info.put("userTags", userTags);

        }
        //计算月经开始
        FrontUser frontUser = frontUserMapper.selectById(userId);
        if (frontUser != null) {
            // 获取最近一次月经开始时间
            Date nearPeriodDate = frontUser.getNearPeriodDate();
            Long periodCycle = frontUser.getPeriodCycle();
            if (nearPeriodDate != null && periodCycle != null) {
                Date now = new Date();
                // 从最近一次月经开始时间出发，不断加上周期，直到超过当前时间
                Date nextPeriodDate = nearPeriodDate;
                while (nextPeriodDate.before(now)) {
                    nextPeriodDate = DateUtils.addDays(nextPeriodDate, periodCycle.intValue());
                }
                // 计算距离下次月经还有几天
                Long daysUntilNextPeriod = DateUtils.getDaysBetween(now, nextPeriodDate);
                info.put("untilPeriodStart", daysUntilNextPeriod.toString());
                // 预测下次排卵日：月经首日 - 14 天
                Date ovulationDay = DateUtils.addDays(nextPeriodDate, -14);
                info.put("nextOvulationDay", new SimpleDateFormat("yyyy-MM-dd").format(ovulationDay));
            }
            info.put("name", frontUser.getUserName());
            info.put("periodStatus", String.valueOf(frontUser.getIsGoPeriod()));
            Long height = frontUser.getUserHeight();
            Long weight = frontUser.getUserWeight();
            double safeHeightCm = height != null ? height  : 0.0;
            double safeWeightKg = weight != null ? weight  : 0.0;
            Double bmi = (safeHeightCm > 0 && safeWeightKg > 0)
                    ? safeWeightKg / Math.pow(safeHeightCm / 100.0, 2)
                    : null;
            info.put("height", height != null ? height.toString() : null);
            info.put("weight", weight != null ? weight.toString() : null);
            info.put("bmi", bmi != null ? String.format("%.2f", bmi) : null);
            String bmiLabel = "未知";
            String color = "#999999";
            if (bmi != null) {
                if (bmi < 18.5) {
                    bmiLabel = "偏瘦";
                    color = "#00AAFF";
                } else if (bmi < 24.9) {
                    bmiLabel = "标准";
                    color = "#33CB4C";
                } else if (bmi < 30.0) {
                    bmiLabel = "超重";
                    color = "#EA842B";
                } else {
                    bmiLabel = "肥胖";
                    color = "#C35A21";
                }
            }
            info.put("bmi", String.format("%.2f", bmi));
            info.put("bmiLabel", bmiLabel);
            info.put("bmiLabelColor", color);
        }


        return info;
    }
    //获取用户标签
    public String getUserTags(String content) {
        JSONArray jsonArray = JSONArray.parseArray(content);
        for (Object obj : jsonArray) {
            JSONObject item = (JSONObject) obj;
            String title = item.getString("title");
            String maintitle = item.getString("maintitle");
            if ("用户标签".equals(title) && "基础信息".equals(maintitle)) {
                JSONObject selected = item.getJSONObject("selected");
                if (selected != null) {
                    return selected.getString("label"); // 返回 "日常养生"
                }
            }
        }
        return "";
    }
    //获取健康信息档案
    public List<String> extractHealthPlans(String content) {
        List<String> healthPlans = new ArrayList<>();
        JSONArray jsonArray = JSONArray.parseArray(content);
        if (jsonArray == null || jsonArray.isEmpty()) {
            return healthPlans;
        }
        for (Object obj : jsonArray) {
            JSONObject item = (JSONObject) obj;
            String maintitle = item.getString("maintitle");
            if ("健康信息".equals(maintitle)) {
                JSONObject selected = item.getJSONObject("selected");
                if (selected != null) {
                    String healthPlan = selected.getString("healthPlan");
                    if (healthPlan != null && !healthPlan.isEmpty()) {
                        healthPlans.add(healthPlan);
                    }
                }
            }
        }
        return healthPlans;
    }
}
