package com.ruoyi.uni.uni.service.Impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.FrontFeedback;
import com.ruoyi.system.api.mapper.FrontFeedbackMapper;
import com.ruoyi.uni.uni.service.IFrontFeedbackService;
import com.ruoyi.uni.uni.utils.oss.OssUrlCleanerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 意见反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class FrontFeedbackServiceImpl implements IFrontFeedbackService
{
    @Autowired
    private FrontFeedbackMapper frontFeedbackMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;



    /**
     * 新增意见反馈
     *
     * @param frontFeedback 意见反馈
     * @return 结果
     */
    @Override
    public int insertFrontFeedback(FrontFeedback frontFeedback)
    {
        Long userId = SecurityUtils.getUserId();
        frontFeedback.setUserId(userId);
        frontFeedback.setCreateTime(DateUtils.getNowDate());
        frontFeedback.setPic(ossUrlCleanerUtil.cleanUrlsToString(frontFeedback.getPic()));
        return frontFeedbackMapper.insertFrontFeedback(frontFeedback);
    }

}
