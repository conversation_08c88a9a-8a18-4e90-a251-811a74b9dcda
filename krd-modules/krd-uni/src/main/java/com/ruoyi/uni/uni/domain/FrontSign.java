package com.ruoyi.uni.uni.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户签到对象 front_sign
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@Schema(description = "用户签到对象 front_sign")
@TableName("front_sign")
public class FrontSign implements Serializable {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 最近一次签到日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最近一次签到日期", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDateTime checkInDate;

    /** 连续签到天数 */
    @Excel(name = "连续签到天数")
    private Long continuousDays;

    /** 本次签到获得的积分 */
    @Excel(name = "本次签到获得的积分")
    private BigDecimal pointsGained;



}
