package com.ruoyi.uni.uni.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户历史经期记录对象 front_history_period
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Schema(name = "用户地址")
@Data
@TableName("front_history_period")
public class FrontHistoryPeriod implements Serializable {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 周期长度 */
    @Excel(name = "周期长度")
    private Long periodCycle;

    /** 经期长度 */
    @Excel(name = "经期长度")
    private Long periodLength;

    /** 最近一次月经开始日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最近一次月经开始日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nearPeriodDate;

    /** 用户id */
    @Excel(name = "用户id")
    private Long uid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


}
