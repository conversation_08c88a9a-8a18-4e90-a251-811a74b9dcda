<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.uni.check.mapper.FrontTmpPactarResultMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.uni.check.domain.entity.FrontTmpPactarResultEntity" id="frontTmpPactarResultMap">
        <result property="id" column="id"/>
        <result property="pactarId" column="pactar_id"/>
        <result property="resultName" column="result_name"/>
        <result property="resultColor" column="result_color"/>
        <result property="resultRangeStartVal" column="result_range_start_val"/>
        <result property="resultRangeStartSymbol" column="result_range_start_symbol"/>
        <result property="resultRangeEndVal" column="result_range_end_val"/>
        <result property="resultRangeEndSymbol" column="result_range_end_symbol"/>
        <result property="resultInterpret" column="result_interpret"/>
        <result property="healthAdvice" column="health_advice"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>