<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uni.uni.mapper.FrontUserTokenMapper">

    <sql id="selectFrontUserTokenVo">
        select id, open_id, user_id, create_time from front_user_token
    </sql>

    <select id="selectByUserId" resultType="com.ruoyi.uni.uni.domain.user.FrontUserToken">
        <include refid="selectFrontUserTokenVo"/>
        where user_id = #{userId}
    </select>

    <select id="selectOpenIdOne" resultType="com.ruoyi.uni.uni.domain.user.FrontUserToken">
        <include refid="selectFrontUserTokenVo"/>
        where open_id = #{openId}
    </select>

    <insert id="insertFrontUserToken" parameterType="com.ruoyi.uni.uni.domain.user.FrontUserToken" useGeneratedKeys="true" keyProperty="id">
        insert into front_user_token
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="openId != null">open_id,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="userId != null">#{userId},</if>
        <if test="openId != null">#{openId},</if>
        <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

</mapper>