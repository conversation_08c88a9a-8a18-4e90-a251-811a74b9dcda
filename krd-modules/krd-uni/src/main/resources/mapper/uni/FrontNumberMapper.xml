<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.uni.check.mapper.FrontNumberMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.uni.check.domain.entity.FrontNumberEntity" id="frontNumberMap">
        <result property="id" column="id"/>
        <result property="itemCode" column="item_code"/>
        <result property="packageId" column="package_id"/>
        <result property="number" column="number"/>
        <result property="qrcount" column="qrcount"/>
        <result property="barcount" column="barcount"/>
        <result property="orderUserId" column="order_user_id"/>
        <result property="orderFamilyId" column="order_family_id"/>
        <result property="orderTime" column="order_time"/>
        <result property="checkFamilyId" column="check_family_id"/>
        <result property="checkFamilyAge" column="check_family_age"/>
        <result property="checkFamilyWeight" column="check_family_weight"/>
        <result property="checkFamilyHeight" column="check_family_height"/>
        <result property="checkId" column="check_id"/>
        <result property="codeCreateTime" column="code_create_time"/>
        <result property="printTime" column="print_time"/>
        <result property="bindingTime" column="binding_time"/>
        <result property="shippTime" column="shipp_time"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="handleTime" column="handle_time"/>
        <result property="handleRemark" column="handle_remark"/>
        <result property="reportReviewFamilyId" column="report_review_family_id"/>
        <result property="reportReviewTime" column="report_review_time"/>
        <result property="reportReviewNotes" column="report_review_notes"/>
        <result property="reportReReviewFamilyId" column="report_re_review_family_id"/>
        <result property="reportReReviewTime" column="report_re_review_time"/>
        <result property="reportReReviewNotes" column="report_re_review_notes"/>
        <result property="reportStatus" column="report_status"/>
        <result property="numberStatus" column="number_status"/>
        <result property="sampleStatus" column="sample_status"/>
        <result property="processStatus" column="process_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <select id="getPakeageByItemCode" resultType="com.ruoyi.uni.check.domain.vo.BindPakeageVO">
        select
            num.item_code as itemCode,
            IFNULL(good.`name`,pack.pakeage_name) as pakeageName,
            good.subtitle as subtitle,
            good.first_pic as firstPic,
            num.sample_status as sampleStatus
        from front_number num
        left join front_goods good on num.package_id = good.package_id
        LEFT JOIN front_package pack ON num.package_id=pack.id
        where num.item_code = #{itemCode}
    </select>
    <select id="sendList" resultType="com.ruoyi.uni.check.domain.vo.SendPakeageVO">
        select
            num.item_code as itemCode,
            IFNULL(good.`name`,pack.pakeage_name) as pakeageName,
            good.subtitle as subtitle,
            good.first_pic as firstPic,
            fam.name as checkFamilyName
        from front_number num
        left join front_goods good on num.package_id = good.package_id
        LEFT JOIN front_package pack ON num.package_id=pack.id
        left join front_family fam on num.check_family_id = fam.id
        where num.order_user_id = #{userid}
            and num.sample_status = '02'
            and num.process_status = '03'
    </select>
    <select id="getAddressList" resultType="com.ruoyi.uni.check.domain.vo.AddressVO">
        select
            adr.id as id,
            adr.name as name,
            adr.mobile as mobile,
            adr.area as area,
            adr.address as address,
            adr.tag as tag,
            adr.is_default as isDefault
        from front_address adr
        where adr.user_id = #{userid}
    </select>
    <select id="getSampleStatusList" resultType="com.ruoyi.uni.check.domain.vo.SampleStatusVO">
        select
            num.item_code as itemCode,
            IFNULL(good.`name`,pack.pakeage_name) as pakeageName,
            good.subtitle as subtitle,
            good.first_pic as firstPic,
            fam.name as checkFamilyName,
            num.sample_status as sampleStatus,
            num.report_status as reportStatus
        from front_number num
        left join front_goods good on num.package_id = good.package_id
        LEFT JOIN front_package pack ON num.package_id=pack.id
        left join front_family fam on num.check_family_id = fam.id
        where num.order_user_id = #{userid}
        <if test="searchValue != null and searchValue != ''">
            and (
                num.item_code like concat('%', #{searchValue}, '%')
                or good.name like concat('%', #{searchValue}, '%')
                or fam.name like concat('%', #{searchValue}, '%')
            )
        </if>
    </select>
    <select id="getSampleStatusByItemCode" resultType="com.ruoyi.uni.check.domain.dto.SampleStatusDTO">
        select
            num.item_code as itemCode,
            IFNULL(good.`name`,pack.pakeage_name) as pakeageName,
            good.subtitle as subtitle,
            good.first_pic as firstPic,
            fam.name as checkFamilyName,
            num.binding_time as bindingTime,
            num.shipp_time as shippTime,
            num.receive_time as receiveTime,
            num.handle_time as handleTime,
            num.report_re_review_time as reportReReviewTime,
            num.sample_status as sampleStatus,
            num.report_status as reportStatus,
            jd.order_code as orderCode,
            jd.waybill_code as waybillCode,
            jd.pickup_promise_time as pickupPromiseTime
        from front_number num
                 left join front_goods good on num.package_id = good.package_id
                 LEFT JOIN front_package pack ON num.package_id=pack.id
                 left join front_family fam on num.check_family_id = fam.id
        left join front_jd_order jd on num.jd_order_id = jd.id
        where num.order_user_id = #{userid}
        and  num.item_code = #{itemCode}
    </select>
    <select id="selectReportBaseInfo" resultType="com.ruoyi.uni.check.domain.vo.ReportBaseInfoVO">
        select
            num.item_code as itemCode,
            pack.pakeage_name as pakeageName,
            num.report_result_time as reportResultTime,
            family.name as checkFamilyName,
            num.health_advice_total as healthAdviceTotal,
            num.tmp_id as tmpId
        from front_number num
                 left join front_package pack on num.package_id = pack.id
                 left join front_family family on num.check_family_id = family.id
        where num.item_code = #{itemCode}
    </select>

</mapper>