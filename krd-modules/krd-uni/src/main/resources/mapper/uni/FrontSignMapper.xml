<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uni.uni.mapper.FrontSignMapper">

    <resultMap type="FrontSign" id="FrontSignResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="checkInDate"    column="check_in_date"    />
        <result property="continuousDays"    column="continuous_days"    />
        <result property="pointsGained"    column="points_gained"    />
    </resultMap>

    <sql id="selectFrontSignVo">
        select id, user_id, check_in_date, continuous_days, points_gained, check_in_time from front_sign
    </sql>

    <select id="selectFrontSignList" parameterType="FrontSign" resultMap="FrontSignResult">
        <include refid="selectFrontSignVo"/>
        <where>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="checkInDate != null "> and check_in_date = #{checkInDate}</if>
            <if test="continuousDays != null "> and continuous_days = #{continuousDays}</if>
            <if test="pointsGained != null "> and points_gained = #{pointsGained}</if>
        </where>
    </select>

    <select id="selectFrontSignById" parameterType="String" resultMap="FrontSignResult">
        <include refid="selectFrontSignVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontSign" parameterType="FrontSign">
        insert into front_sign
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="checkInDate != null">check_in_date,</if>
            <if test="continuousDays != null">continuous_days,</if>
            <if test="pointsGained != null">points_gained,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="checkInDate != null">#{checkInDate},</if>
            <if test="continuousDays != null">#{continuousDays},</if>
            <if test="pointsGained != null">#{pointsGained},</if>
        </trim>
    </insert>

    <update id="updateFrontSign" parameterType="FrontSign">
        update front_sign
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="checkInDate != null">check_in_date = #{checkInDate},</if>
            <if test="continuousDays != null">continuous_days = #{continuousDays},</if>
            <if test="pointsGained != null">points_gained = #{pointsGained},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontSignById" parameterType="String">
        delete from front_sign where id = #{id}
    </delete>

    <delete id="deleteFrontSignByIds" parameterType="String">
        delete from front_sign where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
