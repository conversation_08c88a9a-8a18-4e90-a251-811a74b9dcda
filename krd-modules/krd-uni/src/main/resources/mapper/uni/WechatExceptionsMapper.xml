<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uni.wechat.mapper.WechatExceptionsMapper">

    <insert id="insertWechatExceptions" parameterType="com.ruoyi.system.api.domain.WechatExceptions">
        INSERT INTO wechat_exceptions
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="errcode != null">errcode,</if>
            <if test="errmsg != null">errmsg,</if>
            <if test="data != null">data,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="errcode != null">#{errcode},</if>
            <if test="errmsg != null">#{errmsg},</if>
            <if test="data != null">#{data},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
</mapper>
