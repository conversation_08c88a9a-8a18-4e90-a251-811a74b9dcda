<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.uni.check.mapper.FrontJdOrderMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.uni.check.domain.entity.FrontJdOrderEntity" id="frontJdOrderMap">
        <result property="id" column="id"/>
        <result property="orderCode" column="order_code"/>
        <result property="waybillCode" column="waybill_code"/>
        <result property="pickupPromiseTime" column="pickup_promise_time"/>
        <result property="deliveryPromiseTime" column="delivery_promise_time"/>
        <result property="senderContact" column="sender_contact" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="receiverContact" column="receiver_contact" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="createOrderResponse" column="create_order_response" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="orderStatus" column="order_status"/>
        <result property="orderStatusDesc" column="order_status_desc"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>