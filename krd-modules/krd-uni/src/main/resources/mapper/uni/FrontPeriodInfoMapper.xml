<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uni.uni.mapper.FrontPeriodInfoMapper">

    <resultMap type="com.ruoyi.uni.uni.domain.FrontPeriodInfo" id="FrontPeriodInfoResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="period"    column="period"    />
        <result property="ovulation"    column="ovulation"    />
        <result property="ovulaDay"    column="ovula_day"    />
        <result property="predPeriod"    column="pred_period"    />
        <result property="periodStart"    column="period_start"    />
        <result property="periodEnd"    column="period_end"    />
        <result property="compareTime"    column="compare_time"    />
        <result property="menstrualPeriodDays"    column="menstrual_period_days"    />
        <result property="cycleDays"    column="cycle_days"    />
        <result property="month"    column="month"    />
    </resultMap>

    <sql id="selectFrontPeriodInfoVo">
        select id, user_id, create_time,month, update_time, create_by, update_by, period, ovulation, ovula_day, pred_period, period_start, period_end, compare_time, menstrual_period_days, cycle_days from front_period_info
    </sql>

    <select id="selectFrontPeriodInfoList" parameterType="com.ruoyi.uni.uni.domain.FrontPeriodInfo" resultMap="FrontPeriodInfoResult">
        <include refid="selectFrontPeriodInfoVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="ovulation != null  and ovulation != ''"> and ovulation = #{ovulation}</if>
            <if test="ovulaDay != null  and ovulaDay != ''"> and ovula_day = #{ovulaDay}</if>
            <if test="predPeriod != null  and predPeriod != ''"> and pred_period = #{predPeriod}</if>
            <if test="periodStart != null "> and period_start = #{periodStart}</if>
            <if test="periodEnd != null "> and period_end = #{periodEnd}</if>
            <if test="compareTime != null "> and compare_time = #{compareTime}</if>
            <if test="menstrualPeriodDays != null "> and menstrual_period_days = #{menstrualPeriodDays}</if>
            <if test="cycleDays != null "> and cycle_days = #{cycleDays}</if>
             <if test="month != null "> and month = #{month}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectFrontPeriodInfoById" parameterType="Long" resultMap="FrontPeriodInfoResult">
        <include refid="selectFrontPeriodInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontPeriodInfo" parameterType="com.ruoyi.uni.uni.domain.FrontPeriodInfo" useGeneratedKeys="true" keyProperty="id">
        insert into front_period_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="period != null">period,</if>
            <if test="ovulation != null">ovulation,</if>
            <if test="ovulaDay != null">ovula_day,</if>
            <if test="predPeriod != null">pred_period,</if>
            <if test="periodStart != null">period_start,</if>
            <if test="periodEnd != null">period_end,</if>
            <if test="compareTime != null">compare_time,</if>
            <if test="menstrualPeriodDays != null">menstrual_period_days,</if>
            <if test="cycleDays != null">cycle_days,</if>
            <if test="month != null">month,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="period != null">#{period},</if>
            <if test="ovulation != null">#{ovulation},</if>
            <if test="ovulaDay != null">#{ovulaDay},</if>
            <if test="predPeriod != null">#{predPeriod},</if>
            <if test="periodStart != null">#{periodStart},</if>
            <if test="periodEnd != null">#{periodEnd},</if>
            <if test="compareTime != null">#{compareTime},</if>
            <if test="menstrualPeriodDays != null">#{menstrualPeriodDays},</if>
            <if test="cycleDays != null">#{cycleDays},</if>
            <if test="month != null">#{month},</if>
        </trim>
    </insert>

    <update id="updateFrontPeriodInfo" parameterType="com.ruoyi.uni.uni.domain.FrontPeriodInfo">
        update front_period_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="period != null">period = #{period},</if>
            <if test="ovulation != null">ovulation = #{ovulation},</if>
            <if test="ovulaDay != null">ovula_day = #{ovulaDay},</if>
            <if test="predPeriod != null">pred_period = #{predPeriod},</if>
            <if test="periodStart != null">period_start = #{periodStart},</if>
            <if test="periodEnd != null">period_end = #{periodEnd},</if>
            <if test="compareTime != null">compare_time = #{compareTime},</if>
            <if test="menstrualPeriodDays != null">menstrual_period_days = #{menstrualPeriodDays},</if>
            <if test="cycleDays != null">cycle_days = #{cycleDays},</if>
            <if test="month != null">month = #{month},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontPeriodInfoById" parameterType="Long">
        delete from front_period_info where id = #{id}
    </delete>

    <delete id="deleteFrontPeriodInfoByIds" parameterType="String">
        delete from front_period_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
