<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uni.uni.mapper.FrontHistoryPeriodMapper">

    <resultMap type="com.ruoyi.uni.uni.domain.FrontHistoryPeriod" id="FrontHistoryPeriodResult">
        <result property="id"    column="id"    />
        <result property="periodCycle"    column="periodCycle"    />
        <result property="periodLength"    column="periodLength"    />
        <result property="nearPeriodDate"    column="nearPeriodDate"    />
        <result property="uid"    column="uid"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectFrontHistoryPeriodVo">
        select id, periodCycle, periodLength, nearPeriodDate, uid, create_time from front_history_period
    </sql>

    <select id="selectFrontHistoryPeriodList" parameterType="com.ruoyi.uni.uni.domain.FrontHistoryPeriod" resultMap="FrontHistoryPeriodResult">
        <include refid="selectFrontHistoryPeriodVo"/>
        <where>
            <if test="periodCycle != null "> and periodCycle = #{periodCycle}</if>
            <if test="periodLength != null "> and periodLength = #{periodLength}</if>
            <if test="nearPeriodDate != null "> and nearPeriodDate = #{nearPeriodDate}</if>
            <if test="uid != null "> and uid = #{uid}</if>
        </where>
    </select>

    <select id="selectFrontHistoryPeriodById" parameterType="Long" resultMap="FrontHistoryPeriodResult">
        <include refid="selectFrontHistoryPeriodVo"/>
        where id = #{id}
    </select>
    <select id="selectFrontHistoryPeriodByMonth" resultType="com.ruoyi.uni.uni.domain.FrontHistoryPeriod">
        SELECT *
        FROM (
        SELECT
        *,
        ROW_NUMBER() OVER (
        PARTITION BY DATE_FORMAT(near_period_date, '%Y-%m')
        ORDER BY create_time DESC
        ) AS rn
        FROM front_history_period
        WHERE uid = #{uid}
        ) AS tmp
        <where>
            <![CDATA[
            near_period_date <= (
                DATE_FORMAT(#{inputMonth}, '%Y-%m-01') + INTERVAL 1 MONTH - INTERVAL 1 DAY
            )
            AND rn = 1
        ]]>
        </where>
        ORDER BY near_period_date DESC
        LIMIT 1;
    </select>
    <select id="selectInitInfo" resultType="com.ruoyi.uni.uni.domain.FrontHistoryPeriod">
        select * from front_history_period where uid = #{uid} order by create_time desc limit 1;
    </select>

    <insert id="insertFrontHistoryPeriod" parameterType="com.ruoyi.uni.uni.domain.FrontHistoryPeriod" useGeneratedKeys="true" keyProperty="id">
        insert into front_history_period
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="periodCycle != null">periodCycle,</if>
            <if test="periodLength != null">periodLength,</if>
            <if test="nearPeriodDate != null">nearPeriodDate,</if>
            <if test="uid != null">uid,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="periodCycle != null">#{periodCycle},</if>
            <if test="periodLength != null">#{periodLength},</if>
            <if test="nearPeriodDate != null">#{nearPeriodDate},</if>
            <if test="uid != null">#{uid},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateFrontHistoryPeriod" parameterType="com.ruoyi.uni.uni.domain.FrontHistoryPeriod">
        update front_history_period
        <trim prefix="SET" suffixOverrides=",">
            <if test="periodCycle != null">periodCycle = #{periodCycle},</if>
            <if test="periodLength != null">periodLength = #{periodLength},</if>
            <if test="nearPeriodDate != null">nearPeriodDate = #{nearPeriodDate},</if>
            <if test="uid != null">uid = #{uid},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontHistoryPeriodById" parameterType="Long">
        delete from front_history_period where id = #{id}
    </delete>

    <delete id="deleteFrontHistoryPeriodByIds" parameterType="String">
        delete from front_history_period where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
