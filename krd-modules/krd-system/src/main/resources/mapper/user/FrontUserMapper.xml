<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.user.mapper.FrontUserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.user.domain.entity.FrontUserEntity" id="frontUserMap">
        <result property="id" column="id"/>
        <result property="userIcon" column="user_icon"/>
        <result property="userName" column="user_name"/>
        <result property="userCity" column="user_city"/>
        <result property="userTag" column="user_tag"/>
        <result property="userMobile" column="user_mobile"/>
        <result property="userBirthday" column="user_birthday"/>
        <result property="userAge" column="user_age"/>
        <result property="userSex" column="user_sex"/>
        <result property="userHeight" column="user_height"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="userIp" column="user_ip"/>
        <result property="isDoQuest" column="is_do_quest"/>
        <result property="userWeight" column="user_weight"/>
        <result property="periodCycle" column="period_cycle"/>
        <result property="periodLength" column="period_length"/>
        <result property="nearPeriodDate" column="near_period_date"/>
        <result property="isAnswerHealth" column="is_answer_health"/>
        <result property="isGoPeriod" column="is_go_period"/>
    </resultMap>
    <select id="getUserList" resultType="com.ruoyi.user.domain.vo.UserListVO">
        SELECT
            ur.id AS uid,
            ur.user_name AS uname,
            GROUP_CONCAT(tag.tag_name) AS utags,
            ur.user_mobile AS umobile,
            ur.create_time AS registerTime,
            ur.`status` AS ustatus,
            IF(ur.`status`=0,'正常',IF(ur.`status`=1,'冻结','其他')) AS ustatusDesc
        FROM front_user ur
                 LEFT JOIN front_user_tag ut ON ut.user_id=ur.id
                 LEFT JOIN front_tag tag ON tag.id=ut.tag_id
        <where>
            <if test="ustatus != null and ustatus != ''">
                ur.`status`#{ustatus}
            </if>
            <if test="uname != null and uname != ''">
                ur.`user_name` like ( CONCAT('%',#{uname},'%') )
            </if>
            <if test="searchValue != null and searchValue != ''">
                AND (ur.user_name LIKE CONCAT('%',#{searchValue},'%')
                    OR ur.id LIKE CONCAT('%',#{searchValue},'%')
                    OR ur.user_mobile LIKE CONCAT('%',#{searchValue},'%')
                    )
            </if>
        </where>
        GROUP BY ur.id
    </select>
    <select id="getBalanceTotal" resultType="com.ruoyi.user.domain.dto.BalanceTotalDTO">
        SELECT user_id AS uid,balance AS balanceTotal
        FROM front_balance_info
        WHERE id IN (
        SELECT MAX(id) id
        FROM
        front_balance_info
        WHERE user_id IN (<foreach collection="uidList" item="uid" separator=",">#{uid}</foreach>)
        GROUP BY user_id )
    </select>
    <select id="getUserBoardTop" resultType="com.ruoyi.bullboard.vo.UserBoardTopVo">
        SELECT
            -- 今日新增用户数
            IFNULL(today.todayNewsUserCount, 0) AS todayNewsUserCount,

            -- 昨日新增用户数
            IFNULL(yesterday.yesterdayNewsUserCount, 0) AS yesterdayNewsUserCount,

            -- 相比昨日趋势百分比
            CASE
                WHEN IFNULL(yesterday.yesterdayNewsUserCount, 0) = 0 THEN
                    CASE WHEN IFNULL(today.todayNewsUserCount, 0) > 0 THEN '+100%' ELSE '0%' END
                ELSE
                    CONCAT(
                        CASE WHEN (IFNULL(today.todayNewsUserCount, 0) - IFNULL(yesterday.yesterdayNewsUserCount, 0)) &gt;= 0 THEN '+' ELSE '' END,
                        ROUND(((IFNULL(today.todayNewsUserCount, 0) - IFNULL(yesterday.yesterdayNewsUserCount, 0)) / IFNULL(yesterday.yesterdayNewsUserCount, 0)) * 100, 2),
                        '%'
                    )
            END AS todayNewsUserCountPercent,

            -- 7日新增用户数
            IFNULL(sevenDays.sevenDaysNewsUserCount, 0) AS sevenDaysNewsUserCount,

            -- 相比7日趋势百分比（与前7日对比）
            CASE
                WHEN IFNULL(prevSevenDays.prevSevenDaysNewsUserCount, 0) = 0 THEN
                    CASE WHEN IFNULL(sevenDays.sevenDaysNewsUserCount, 0) > 0 THEN '+100%' ELSE '0%' END
                ELSE
                    CONCAT(
                        CASE WHEN (IFNULL(sevenDays.sevenDaysNewsUserCount, 0) - IFNULL(prevSevenDays.prevSevenDaysNewsUserCount, 0)) &gt;= 0 THEN '+' ELSE '' END,
                        ROUND(((IFNULL(sevenDays.sevenDaysNewsUserCount, 0) - IFNULL(prevSevenDays.prevSevenDaysNewsUserCount, 0)) / IFNULL(prevSevenDays.prevSevenDaysNewsUserCount, 0)) * 100, 2),
                        '%'
                    )
            END AS sevenDaysNewsUserCountPercent,

            -- 30日新增用户数
            IFNULL(thirtyDays.thirtyDaysNewsUserCount, 0) AS thirtyDaysNewsUserCount,

            -- 相比30日趋势百分比（与前30日对比）
            CASE
                WHEN IFNULL(prevThirtyDays.prevThirtyDaysNewsUserCount, 0) = 0 THEN
                    CASE WHEN IFNULL(thirtyDays.thirtyDaysNewsUserCount, 0) > 0 THEN '+100%' ELSE '0%' END
                ELSE
                    CONCAT(
                        CASE WHEN (IFNULL(thirtyDays.thirtyDaysNewsUserCount, 0) - IFNULL(prevThirtyDays.prevThirtyDaysNewsUserCount, 0)) &gt;= 0 THEN '+' ELSE '' END,
                        ROUND(((IFNULL(thirtyDays.thirtyDaysNewsUserCount, 0) - IFNULL(prevThirtyDays.prevThirtyDaysNewsUserCount, 0)) / IFNULL(prevThirtyDays.prevThirtyDaysNewsUserCount, 0)) * 100, 2),
                        '%'
                    )
            END AS thirtyDaysNewsUserCountPercent,

            -- 一年新增用户数
            IFNULL(oneYear.oneYearNewsUserCount, 0) AS oneYearNewsUserCount,

            -- 相比一年趋势百分比（与前一年对比）
            CASE
                WHEN IFNULL(prevOneYear.prevOneYearNewsUserCount, 0) = 0 THEN
                    CASE WHEN IFNULL(oneYear.oneYearNewsUserCount, 0) > 0 THEN '+100%' ELSE '0%' END
                ELSE
                    CONCAT(
                        CASE WHEN (IFNULL(oneYear.oneYearNewsUserCount, 0) - IFNULL(prevOneYear.prevOneYearNewsUserCount, 0)) &gt;= 0 THEN '+' ELSE '' END,
                        ROUND(((IFNULL(oneYear.oneYearNewsUserCount, 0) - IFNULL(prevOneYear.prevOneYearNewsUserCount, 0)) / IFNULL(prevOneYear.prevOneYearNewsUserCount, 0)) * 100, 2),
                        '%'
                    )
            END AS oneYearNewsUserCountPercent

        FROM
            -- 今日新增用户数
            (SELECT COUNT(*) AS todayNewsUserCount
             FROM front_user
             WHERE DATE(create_time) = CURDATE()) today

        CROSS JOIN
            -- 昨日新增用户数
            (SELECT COUNT(*) AS yesterdayNewsUserCount
             FROM front_user
             WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)) yesterday

        CROSS JOIN
            -- 7日新增用户数
            (SELECT COUNT(*) AS sevenDaysNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 6 DAY)) sevenDays

        CROSS JOIN
            -- 前7日新增用户数（用于计算7日趋势）
            (SELECT COUNT(*) AS prevSevenDaysNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 13 DAY)
               AND DATE(create_time) &lt;= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) prevSevenDays

        CROSS JOIN
            -- 30日新增用户数
            (SELECT COUNT(*) AS thirtyDaysNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 29 DAY)) thirtyDays

        CROSS JOIN
            -- 前30日新增用户数（用于计算30日趋势）
            (SELECT COUNT(*) AS prevThirtyDaysNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 59 DAY)
               AND DATE(create_time) &lt;= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) prevThirtyDays

        CROSS JOIN
            -- 一年新增用户数
            (SELECT COUNT(*) AS oneYearNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 364 DAY)) oneYear

        CROSS JOIN
            -- 前一年新增用户数（用于计算一年趋势）
            (SELECT COUNT(*) AS prevOneYearNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 729 DAY)
               AND DATE(create_time) &lt;= DATE_SUB(CURDATE(), INTERVAL 365 DAY)) prevOneYear
    </select>
    <select id="getUserDataLine" resultType="com.ruoyi.bullboard.vo.UserDataLineVo">
        WITH RECURSIVE date_range AS (
            -- 生成指定时间段内的所有日期
            SELECT DATE(#{startDate}) AS date
            UNION ALL
            SELECT DATE_ADD(date, INTERVAL 1 DAY)
            FROM date_range
            WHERE date &lt; DATE(#{endDate})
        ),
        daily_new_users AS (
            -- 每日新增用户数
            SELECT
                DATE(create_time) AS date,
                COUNT(*) AS todayNewUserCount
            FROM front_user
            WHERE DATE(create_time) &gt;= DATE(#{startDate})
              AND DATE(create_time) &lt;= DATE(#{endDate})
            GROUP BY DATE(create_time)
        ),
        daily_active_users AS (
            -- 每日活跃用户数（基于签到记录）
            SELECT
                DATE(check_in_date) AS date,
                COUNT(DISTINCT user_id) AS todayActiveUserCount
            FROM front_sign
            WHERE DATE(check_in_date) &gt;= DATE(#{startDate})
              AND DATE(check_in_date) &lt;= DATE(#{endDate})
            GROUP BY DATE(check_in_date)
        ),
        cumulative_users AS (
            -- 累计用户数（截止到每一天的总用户数）
            SELECT
                dr.date,
                (SELECT COUNT(*)
                 FROM front_user
                 WHERE DATE(create_time) &lt;= dr.date) AS totalUserCount
            FROM date_range dr
        )
        SELECT
            DATE_FORMAT(dr.date, '%Y-%m-%d') AS date,
            IFNULL(dnu.todayNewUserCount, 0) AS todayNewUserCount,
            IFNULL(dau.todayActiveUserCount, 0) AS todayActiveUserCount,
            cu.totalUserCount
        FROM date_range dr
        LEFT JOIN daily_new_users dnu ON dr.date = dnu.date
        LEFT JOIN daily_active_users dau ON dr.date = dau.date
        LEFT JOIN cumulative_users cu ON dr.date = cu.date
        ORDER BY dr.date
    </select>
    <select id="getUserAccount" resultType="com.ruoyi.bullboard.vo.UserAccountVo">
        <choose>
            <!-- type = 0: 新增用户占比（今日新增） -->
            <when test="type == 0">
                SELECT
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (male_count * 100.0 / total_count)
                        END, 2
                    ) AS maleRatio,
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (female_count * 100.0 / total_count)
                        END, 2
                    ) AS femaleRatio
                FROM (
                    SELECT
                        COUNT(*) AS total_count,
                        SUM(CASE WHEN user_sex = 1 THEN 1 ELSE 0 END) AS male_count,
                        SUM(CASE WHEN user_sex = 0 THEN 1 ELSE 0 END) AS female_count
                    FROM front_user
                    WHERE DATE(create_time) = CURDATE()
                ) AS user_stats
            </when>

            <!-- type = 1: 活跃用户占比（基于签到记录） -->
            <when test="type == 1">
                SELECT
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (male_count * 100.0 / total_count)
                        END, 2
                    ) AS maleRatio,
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (female_count * 100.0 / total_count)
                        END, 2
                    ) AS femaleRatio
                FROM (
                    SELECT
                        COUNT(DISTINCT u.id) AS total_count,
                        SUM(CASE WHEN u.user_sex = 1 THEN 1 ELSE 0 END) AS male_count,
                        SUM(CASE WHEN u.user_sex = 0 THEN 1 ELSE 0 END) AS female_count
                    FROM front_user u
                    INNER JOIN front_sign s ON u.id = s.user_id
                    WHERE DATE(s.check_in_date) = CURDATE()
                ) AS active_user_stats
            </when>

            <!-- type = 2: 累计用户占比 -->
            <otherwise>
                SELECT
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (male_count * 100.0 / total_count)
                        END, 2
                    ) AS maleRatio,
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (female_count * 100.0 / total_count)
                        END, 2
                    ) AS femaleRatio
                FROM (
                    SELECT
                        COUNT(*) AS total_count,
                        SUM(CASE WHEN user_sex = 1 THEN 1 ELSE 0 END) AS male_count,
                        SUM(CASE WHEN user_sex = 0 THEN 1 ELSE 0 END) AS female_count
                    FROM front_user
                ) AS total_user_stats
            </otherwise>
        </choose>
    </select>

    <select id="getUserAddNewInfo" resultType="com.ruoyi.bullboard.vo.UserAddNewInfo">
        SELECT
            -- 指定时间段内新增用户数
            IFNULL(newUsers.newUser, 0) AS newUser,

            -- 去年同期新增用户数
            IFNULL(lastYearUsers.lastYearUser, 0) AS lastYearUser,

            -- 新增用户同比增长率
            CASE
                WHEN IFNULL(lastYearUsers.lastYearUser, 0) = 0 THEN
                    CASE WHEN IFNULL(newUsers.newUser, 0) &gt; 0 THEN 100.0 ELSE 0.0 END
                ELSE
                    ROUND(((IFNULL(newUsers.newUser, 0) - IFNULL(lastYearUsers.lastYearUser, 0)) / IFNULL(lastYearUsers.lastYearUser, 0)) * 100, 2)
            END AS yearOnYearGrowth,

            -- 指定时间段内活跃用户数（基于签到记录）
            IFNULL(activeUsers.activeUser, 0) AS activeUser,

            -- 去年同期活跃用户数
            IFNULL(lastYearActiveUsers.lastYearActiveUser, 0) AS lastYearActiveUser,

            -- 活跃用户同比增长率
            CASE
                WHEN IFNULL(lastYearActiveUsers.lastYearActiveUser, 0) = 0 THEN
                    CASE WHEN IFNULL(activeUsers.activeUser, 0) &gt; 0 THEN 100.0 ELSE 0.0 END
                ELSE
                    ROUND(((IFNULL(activeUsers.activeUser, 0) - IFNULL(lastYearActiveUsers.lastYearActiveUser, 0)) / IFNULL(lastYearActiveUsers.lastYearActiveUser, 0)) * 100, 2)
            END AS activeGrowth,

            -- 截止到结束日期的累计用户数
            IFNULL(totalUsers.totalUser, 0) AS totalUser,

            -- 去年同期累计用户数
            IFNULL(lastYearTotalUsers.lastYearTotalUser, 0) AS lastYearTotalUser,

            -- 累计用户同比增长率
            CASE
                WHEN IFNULL(lastYearTotalUsers.lastYearTotalUser, 0) = 0 THEN
                    CASE WHEN IFNULL(totalUsers.totalUser, 0) &gt; 0 THEN 100.0 ELSE 0.0 END
                ELSE
                    ROUND(((IFNULL(totalUsers.totalUser, 0) - IFNULL(lastYearTotalUsers.lastYearTotalUser, 0)) / IFNULL(lastYearTotalUsers.lastYearTotalUser, 0)) * 100, 2)
            END AS totalGrowth

        FROM
            -- 指定时间段内新增用户数
            (SELECT COUNT(*) AS newUser
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE(#{startDate})
               AND DATE(create_time) &lt;= DATE(#{endDate})) newUsers

        CROSS JOIN
            -- 去年同期新增用户数
            (SELECT COUNT(*) AS lastYearUser
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(DATE(#{startDate}), INTERVAL 1 YEAR)
               AND DATE(create_time) &lt;= DATE_SUB(DATE(#{endDate}), INTERVAL 1 YEAR)) lastYearUsers

        CROSS JOIN
            -- 指定时间段内活跃用户数（基于签到记录）
            (SELECT COUNT(DISTINCT user_id) AS activeUser
             FROM front_sign
             WHERE DATE(check_in_date) &gt;= DATE(#{startDate})
               AND DATE(check_in_date) &lt;= DATE(#{endDate})) activeUsers

        CROSS JOIN
            -- 去年同期活跃用户数
            (SELECT COUNT(DISTINCT user_id) AS lastYearActiveUser
             FROM front_sign
             WHERE DATE(check_in_date) &gt;= DATE_SUB(DATE(#{startDate}), INTERVAL 1 YEAR)
               AND DATE(check_in_date) &lt;= DATE_SUB(DATE(#{endDate}), INTERVAL 1 YEAR)) lastYearActiveUsers

        CROSS JOIN
            -- 截止到结束日期的累计用户数
            (SELECT COUNT(*) AS totalUser
             FROM front_user
             WHERE DATE(create_time) &lt;= DATE(#{endDate})) totalUsers

        CROSS JOIN
            -- 去年同期累计用户数
            (SELECT COUNT(*) AS lastYearTotalUser
             FROM front_user
             WHERE DATE(create_time) &lt;= DATE_SUB(DATE(#{endDate}), INTERVAL 1 YEAR)) lastYearTotalUsers
    </select>
    <select id="getUserDataInfo" resultType="com.ruoyi.bullboard.vo.UserDataInfoVo">


    </select>

</mapper>
