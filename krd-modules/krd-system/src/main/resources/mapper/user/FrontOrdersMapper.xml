<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.user.mapper.FrontOrdersMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.user.domain.entity.FrontOrdersEntity" id="frontOrdersMap">
        <result property="id" column="id"/>
        <result property="orderNumber" column="order_number"/>
        <result property="userId" column="user_id"/>
        <result property="totalPrice" column="total_price"/>
        <result property="deductionType" column="deduction_type"/>
        <result property="deductionPrice" column="deduction_price"/>
        <result property="payPrice" column="pay_price"/>
        <result property="pushGoodsNo" column="push_goods_no"/>
        <result property="createTime" column="create_time"/>
        <result property="finishTime" column="finish_time"/>
        <result property="isAfter" column="is_after"/>
        <result property="status" column="status"/>
        <result property="userRemark" column="user_remark"/>
        <result property="platRemark" column="plat_remark"/>
        <result property="closeOrder" column="close_order"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
        <result property="paid" column="paid"/>
        <result property="payType" column="pay_type"/>
        <result property="outTradeNo" column="out_trade_no"/>
        <result property="addressId" column="address_id"/>
        <result property="receiveName" column="receive_name"/>
        <result property="receivePhone" column="receive_phone"/>
        <result property="receiveAddress" column="receive_address"/>
        <result property="receiveAddressDetail" column="receive_address_detail"/>
        <result property="pointsDeduction" column="points_deduction"/>
        <result property="giftDeduction" column="gift_deduction"/>
        <result property="couponDeduction" column="coupon_deduction"/>
    </resultMap>
    <select id="getOrderList" resultType="com.ruoyi.user.domain.vo.UserOrderInfoVO">
        SELECT
            oder.id AS orderId,
            oder.user_id AS userId,
            GROUP_CONCAT(god.`name`) AS goodNames,
            SUM(IFNULL(odergo.price,0)) AS goodTotalPrice,
            SUM(IFNULL(odergo.pay_price,0)) AS payTotalPrice,
            oder.deduction_type AS deductionType,
            oder.deduction_price AS deductionPrice,
            oder.finish_time AS payTime
        FROM front_orders oder
                 LEFT JOIN front_orders_goods odergo ON oder.id=odergo.order_id
                 LEFT JOIN front_goods god on god.id=odergo.goods_id
        WHERE oder.user_id=#{uid}
                AND odergo.type=#{goodsType}
                <if test="startTime !=null and startTime != '' and endTime != null and endTime != ''">
                    AND oder.finish_time BETWEEN #{startTime} AND #{endTime}
                </if>
        GROUP BY oder.id
    </select>
    <select id="getOrderStatistics" resultType="com.ruoyi.user.domain.vo.UserStatisticsVO">
        SELECT *,0 AS friendCount
        FROM (
                 SELECT
                     IFNULL(SUM(goo.pay_price),0) AS xfBalance,
                     COUNT(DISTINCT goo.order_id) AS orderCount,
                     IFNULL(SUM(NOT ISNULL(goo.ack_after_price)),0) AS ackAfterCount
                 FROM front_orders orde
                          LEFT JOIN front_orders_goods goo ON goo.order_id=orde.id
                 WHERE orde.user_id=#{uid}
             ) orders,(
                 SELECT
                     COUNT(1) AS giftCount,
                     IFNULL(SUM(balance),0) AS giftBalance
                 FROM front_gift_info
                 WHERE user_id=#{uid}
             ) gift,(
                 SELECT
                     COUNT(1) AS couponCount,
                     IFNULL(SUM(balance),0) AS couponBalance
                 FROM front_coupon_info
                 WHERE user_id=#{uid} AND `status`=0
             ) coupon,(
                 SELECT IFNULL(SUM(balance),0) AS sourceBalance
                 FROM front_source
                 WHERE user_id=#{uid}
             ) source,(
                 SELECT COUNT(1) AS spcoCount
                 FROM front_shop_collect
                 WHERE user_id=#{uid}
             ) spco,(
                 SELECT COUNT(1) AS evaCount
                 FROM front_evaluate
                 WHERE user_id=#{uid}
             ) eva,(
                 SELECT COUNT(1) AS chckCount
                 FROM front_number
                 WHERE order_user_id=#{uid}
             ) chck
    </select>


</mapper>