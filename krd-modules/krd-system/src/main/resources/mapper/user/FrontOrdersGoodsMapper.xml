<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.user.mapper.FrontOrdersGoodsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.user.domain.entity.FrontOrdersGoodsEntity" id="frontOrdersGoodsMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="orderId" column="order_id"/>
        <result property="goodsId" column="goods_id"/>
        <result property="goodsSpecId" column="goods_spec_id"/>
        <result property="count" column="count"/>
        <result property="price" column="price"/>
        <result property="discount" column="discount"/>
        <result property="meetPrice" column="meet_price"/>
        <result property="payPrice" column="pay_price"/>
        <result property="handleRemark" column="handle_remark"/>
        <result property="afterReason" column="after_reason"/>
        <result property="afterStatus" column="after_status"/>
        <result property="afterDesc" column="after_desc"/>
        <result property="afterPrice" column="after_price"/>
        <result property="applyTime" column="apply_time"/>
        <result property="handleTime" column="handle_time"/>
        <result property="ackAfterPrice" column="ack_after_price"/>
        <result property="afterAddress" column="after_address"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>