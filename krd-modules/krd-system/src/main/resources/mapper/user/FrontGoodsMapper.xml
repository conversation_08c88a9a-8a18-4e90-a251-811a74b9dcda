<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.user.mapper.FrontGoodsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.user.domain.entity.FrontGoodsEntity" id="frontGoodsMap">
        <result property="id" column="id"/>
        <result property="goodsType" column="goods_type"/>
        <result property="packageId" column="package_id"/>
        <result property="gategoryId" column="gategory_id"/>
        <result property="name" column="name"/>
        <result property="subtitle" column="subtitle"/>
        <result property="price" column="price"/>
        <result property="vipPrice" column="vip_price"/>
        <result property="amount" column="amount"/>
        <result property="goodsNo" column="goods_no"/>
        <result property="isStatus" column="is_status"/>
        <result property="featuredFirst" column="featured_first"/>
        <result property="indexImage" column="index_image"/>
        <result property="firstPic" column="first_pic"/>
        <result property="banner" column="banner"/>
        <result property="unit" column="unit"/>
        <result property="keywords" column="keywords"/>
        <result property="details" column="details"/>
        <result property="specId" column="spec_id"/>
        <result property="reportInfo" column="report_info"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
        <result property="sort" column="sort"/>
    </resultMap>
    <select id="getUserSpcoList" resultType="com.ruoyi.user.domain.entity.FrontGoodsEntity">
        SELECT god.*
        FROM front_goods god
                 LEFT JOIN front_shop_collect spco ON god.id=spco.goods_id
        WHERE spco.user_id=#{uid}
    </select>


</mapper>