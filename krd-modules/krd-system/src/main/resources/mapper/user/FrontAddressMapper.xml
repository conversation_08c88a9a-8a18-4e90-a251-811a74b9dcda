<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.user.mapper.FrontAddressMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.user.domain.entity.FrontAddressEntity" id="frontAddressMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="mobile" column="mobile"/>
        <result property="area" column="area"/>
        <result property="address" column="address"/>
        <result property="tag" column="tag"/>
        <result property="isDefault" column="is_default"/>
        <result property="isDel" column="is_del"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>