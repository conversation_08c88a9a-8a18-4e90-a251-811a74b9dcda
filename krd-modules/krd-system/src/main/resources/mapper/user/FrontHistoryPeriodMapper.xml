<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.user.mapper.FrontHistoryPeriodMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.user.domain.entity.FrontHistoryPeriodEntity" id="frontHistoryPeriodMap">
        <result property="id" column="id"/>
        <result property="periodCycle" column="period_cycle"/>
        <result property="periodLength" column="period_length"/>
        <result property="nearPeriodDate" column="near_period_date"/>
        <result property="uid" column="uid"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="selectFrontHistoryPeriodByMonth" resultType="com.ruoyi.user.domain.entity.FrontHistoryPeriodEntity">
        SELECT *
        FROM (
        SELECT
        *,
        ROW_NUMBER() OVER (
        PARTITION BY DATE_FORMAT(near_period_date, '%Y-%m')
        ORDER BY create_time DESC
        ) AS rn
        FROM front_history_period
        WHERE uid = #{uid}
        ) AS tmp
        <where>
            <![CDATA[
            near_period_date <= (
                DATE_FORMAT(#{inputMonth}, '%Y-%m-01') + INTERVAL 1 MONTH - INTERVAL 1 DAY
            )
            AND rn = 1
        ]]>
        </where>
        ORDER BY near_period_date DESC
        LIMIT 1;
    </select>
</mapper>