<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.user.mapper.FrontPeriodInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.user.domain.entity.FrontPeriodInfoEntity" id="frontPeriodInfoMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="period" column="period"/>
        <result property="ovulation" column="ovulation"/>
        <result property="ovulaDay" column="ovula_day"/>
        <result property="predPeriod" column="pred_period"/>
        <result property="periodStart" column="period_start"/>
        <result property="periodEnd" column="period_end"/>
        <result property="compareTime" column="compare_time"/>
        <result property="menstrualPeriodDays" column="menstrual_period_days"/>
        <result property="cycleDays" column="cycle_days"/>
        <result property="month" column="month"/>
        <result property="remark" column="remark"/>
    </resultMap>


</mapper>