<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.check.mapper.FrontPackageMapper">
    
    <resultMap type="com.ruoyi.check.domain.vo.PackageListVO" id="FrontPackageResult">
        <result property="id"    column="id"    />
        <result property="pakeageName"    column="pakeage_name"    />
        <result property="packageTarget"    column="package_target"    />
        <result property="number"    column="number"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="userId"    column="user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectFrontPackageVo">
        select id, pakeage_name, package_target, number, create_time, update_time, is_del, user_id, create_by, update_by from front_package
    </sql>

    <select id="selectFrontPackageList" parameterType="com.ruoyi.check.domain.entity.FrontPackage" resultMap="FrontPackageResult">
        <include refid="selectFrontPackageVo"/>
        <where>
            and is_del = 0
            <if test="pakeageName != null  and pakeageName != ''"> and pakeage_name like concat('%', #{pakeageName}, '%')</if>
        </where>
    </select>
    
    <select id="selectFrontPackageById" resultMap="FrontPackageResult">
        <include refid="selectFrontPackageVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontPackage" parameterType="com.ruoyi.check.domain.entity.FrontPackage" useGeneratedKeys="true" keyProperty="id">
        insert into front_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pakeageName != null">pakeage_name,</if>
            <if test="packageTarget != null">package_target,</if>
            <if test="number != null">number,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="userId != null">user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pakeageName != null">#{pakeageName},</if>
            <if test="packageTarget != null">#{packageTarget},</if>
            <if test="number != null">#{number},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateFrontPackage" parameterType="com.ruoyi.check.domain.entity.FrontPackage">
        update front_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="pakeageName != null">pakeage_name = #{pakeageName},</if>
            <if test="packageTarget != null">package_target = #{packageTarget},</if>
            <if test="number != null">number = #{number},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontPackageById" parameterType="Long">
        delete from front_package where id = #{id}
    </delete>

    <delete id="deleteFrontPackageByIds" parameterType="String">
        update front_package
        set is_del = 1
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>