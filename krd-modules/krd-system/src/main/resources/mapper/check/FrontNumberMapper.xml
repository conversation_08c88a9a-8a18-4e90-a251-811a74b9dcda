<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.check.mapper.FrontNumberMapper">
    
    <resultMap type="com.ruoyi.check.domain.entity.FrontNumber" id="FrontNumberResult">
        <result property="id"    column="id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="packageId"    column="package_id"    />
        <result property="number"    column="number"    />
        <result property="qrcount"    column="qrcount"    />
        <result property="barcount"    column="barcount"    />
        <result property="orderUserId"    column="order_user_id"    />
        <result property="orderFamilyId"    column="order_family_id"    />
        <result property="orderTime"    column="order_time"    />
        <result property="checkFamilyId"    column="check_family_id"    />
        <result property="checkFamilyAge"    column="check_family_age"    />
        <result property="checkFamilyWeight"    column="check_family_weight"    />
        <result property="checkFamilyHeight"    column="check_family_height"    />
        <result property="checkId"    column="check_id"    />
        <result property="codeCreateTime"    column="code_create_time"    />
        <result property="printTime"    column="print_time"    />
        <result property="bindingTime"    column="binding_time"    />
        <result property="shippTime"    column="shipp_time"    />
        <result property="receiveTime"    column="receive_time"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleRemark"    column="handle_remark"    />
        <result property="reportResultTime"    column="report_result_time"    />
        <result property="reportReviewFamilyId"    column="report_review_family_id"    />
        <result property="reportReviewTime"    column="report_review_time"    />
        <result property="reportReviewNotes"    column="report_review_notes"    />
        <result property="reportReReviewFamilyId"    column="report_re_review_family_id"    />
        <result property="reportReReviewTime"    column="report_re_review_time"    />
        <result property="reportReReviewNotes"    column="report_re_review_notes"    />
        <result property="reportStatus"    column="report_status"    />
        <result property="numberStatus"    column="number_status"    />
        <result property="sampleStatus"    column="sample_status"    />
        <result property="processStatus"    column="process_status"    />
        <result property="healthAdviceTotal"    column="health_advice_total"    />
        <result property="tmpId"    column="tmp_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectFrontNumberVo">
        select id, item_code, package_id, number, qrcount, barcount, order_user_id, order_family_id, order_time, check_family_id, check_family_age, check_family_weight, check_family_height, check_id, code_create_time, print_time, binding_time, shipp_time, receive_time, handle_time, handle_remark, report_result_time, report_review_family_id, report_review_time, report_review_notes, report_re_review_family_id, report_re_review_time, report_re_review_notes, report_status, number_status, sample_status, process_status, create_time, update_time, create_by, update_by from front_number
    </sql>

    <select id="selectFrontNumberList" parameterType="com.ruoyi.check.domain.entity.FrontNumber" resultMap="FrontNumberResult">
        <include refid="selectFrontNumberVo"/>
        <where>  
            <if test="packageId != null "> and package_id = #{packageId}</if>
            <if test="numberStatus != null  and numberStatus != ''"> and number_status = #{numberStatus}</if>
        </where>
    </select>
    
    <select id="selectFrontNumberById" parameterType="Long" resultMap="FrontNumberResult">
        <include refid="selectFrontNumberVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontNumber" parameterType="com.ruoyi.check.domain.entity.FrontNumber" useGeneratedKeys="true" keyProperty="id">
        insert into front_number
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="packageId != null">package_id,</if>
            <if test="number != null">number,</if>
            <if test="qrcount != null">qrcount,</if>
            <if test="barcount != null">barcount,</if>
            <if test="orderUserId != null">order_user_id,</if>
            <if test="orderFamilyId != null">order_family_id,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="checkFamilyId != null">check_family_id,</if>
            <if test="checkFamilyAge != null">check_family_age,</if>
            <if test="checkFamilyWeight != null">check_family_weight,</if>
            <if test="checkFamilyHeight != null">check_family_height,</if>
            <if test="checkId != null">check_id,</if>
            <if test="codeCreateTime != null">code_create_time,</if>
            <if test="printTime != null">print_time,</if>
            <if test="bindingTime != null">binding_time,</if>
            <if test="shippTime != null">shipp_time,</if>
            <if test="receiveTime != null">receive_time,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="handleRemark != null">handle_remark,</if>
            <if test="reportResultTime != null">report_result_time,</if>
            <if test="reportReviewFamilyId != null">report_review_family_id,</if>
            <if test="reportReviewTime != null">report_review_time,</if>
            <if test="reportReviewNotes != null">report_review_notes,</if>
            <if test="reportReReviewFamilyId != null">report_re_review_family_id,</if>
            <if test="reportReReviewTime != null">report_re_review_time,</if>
            <if test="reportReReviewNotes != null">report_re_review_notes,</if>
            <if test="reportStatus != null">report_status,</if>
            <if test="numberStatus != null">number_status,</if>
            <if test="sampleStatus != null">sample_status,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="healthAdviceTotal != null">health_advice_total,</if>
            <if test="tmpId != null">tmp_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="number != null">#{number},</if>
            <if test="qrcount != null">#{qrcount},</if>
            <if test="barcount != null">#{barcount},</if>
            <if test="orderUserId != null">#{orderUserId},</if>
            <if test="orderFamilyId != null">#{orderFamilyId},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="checkFamilyId != null">#{checkFamilyId},</if>
            <if test="checkFamilyAge != null">#{checkFamilyAge},</if>
            <if test="checkFamilyWeight != null">#{checkFamilyWeight},</if>
            <if test="checkFamilyHeight != null">#{checkFamilyHeight},</if>
            <if test="checkId != null">#{checkId},</if>
            <if test="codeCreateTime != null">#{codeCreateTime},</if>
            <if test="printTime != null">#{printTime},</if>
            <if test="bindingTime != null">#{bindingTime},</if>
            <if test="shippTime != null">#{shippTime},</if>
            <if test="receiveTime != null">#{receiveTime},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="handleRemark != null">#{handleRemark},</if>
            <if test="reportReviewFamilyId != null">#{reportReviewFamilyId},</if>
            <if test="reportReviewTime != null">#{reportReviewTime},</if>
            <if test="reportReviewNotes != null">#{reportReviewNotes},</if>
            <if test="reportReReviewFamilyId != null">#{reportReReviewFamilyId},</if>
            <if test="reportReReviewTime != null">#{reportReReviewTime},</if>
            <if test="reportReReviewNotes != null">#{reportReReviewNotes},</if>
            <if test="reportStatus != null">#{reportStatus},</if>
            <if test="numberStatus != null">#{numberStatus},</if>
            <if test="sampleStatus != null">#{sampleStatus},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="healthAdviceTotal != null">#{healthAdviceTotal},</if>
            <if test="tmpId != null">#{tmpId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateFrontNumber" parameterType="com.ruoyi.check.domain.entity.FrontNumber">
        update front_number
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="number != null">number = #{number},</if>
            <if test="qrcount != null">qrcount = #{qrcount},</if>
            <if test="barcount != null">barcount = #{barcount},</if>
            <if test="orderUserId != null">order_user_id = #{orderUserId},</if>
            <if test="orderFamilyId != null">order_family_id = #{orderFamilyId},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="checkFamilyId != null">check_family_id = #{checkFamilyId},</if>
            <if test="checkFamilyAge != null">check_family_age = #{checkFamilyAge},</if>
            <if test="checkFamilyWeight != null">check_family_weight = #{checkFamilyWeight},</if>
            <if test="checkFamilyHeight != null">check_family_height = #{checkFamilyHeight},</if>
            <if test="checkId != null">check_id = #{checkId},</if>
            <if test="codeCreateTime != null">code_create_time = #{codeCreateTime},</if>
            <if test="printTime != null">print_time = #{printTime},</if>
            <if test="bindingTime != null">binding_time = #{bindingTime},</if>
            <if test="shippTime != null">shipp_time = #{shippTime},</if>
            <if test="receiveTime != null">receive_time = #{receiveTime},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleRemark != null">handle_remark = #{handleRemark},</if>
            <if test="reportResultTime != null">report_result_time = #{reportResultTime},</if>
            <if test="reportReviewFamilyId != null">report_review_family_id = #{reportReviewFamilyId},</if>
            <if test="reportReviewTime != null">report_review_time = #{reportReviewTime},</if>
            <if test="reportReviewNotes != null">report_review_notes = #{reportReviewNotes},</if>
            <if test="reportReReviewFamilyId != null">report_re_review_family_id = #{reportReReviewFamilyId},</if>
            <if test="reportReReviewTime != null">report_re_review_time = #{reportReReviewTime},</if>
            <if test="reportReReviewNotes != null">report_re_review_notes = #{reportReReviewNotes},</if>
            <if test="reportStatus != null">report_status = #{reportStatus},</if>
            <if test="numberStatus != null">number_status = #{numberStatus},</if>
            <if test="sampleStatus != null">sample_status = #{sampleStatus},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="healthAdviceTotal != null">health_advice_total = #{healthAdviceTotal},</if>
            <if test="tmpId != null">tmp_id = #{tmpId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="printDataUpdate">
        update front_number
        set print_time = NOW(),
        number_status = '02',
        process_status = '02'
        where item_code in
        <foreach collection="printDataList" open="(" separator="," close=")" item="item">
            #{item.itemCode}
        </foreach>
    </update>
    <update id="printDataUpdateRe">
        update front_number
        set print_time = null,
        number_status = '01',
        process_status = '01'
        where item_code in
        <foreach collection="printDataList" open="(" separator="," close=")" item="item">
            #{item.itemCode}
        </foreach>
    </update>
    <delete id="deleteFrontNumberById" parameterType="Long">
        delete from front_number where id = #{id}
    </delete>

    <delete id="deleteFrontNumberByIds" parameterType="String">
        delete from front_number where item_code in
        <foreach item="itemCode" collection="itemCodes" open="(" separator="," close=")">
            #{itemCode}
        </foreach>
        AND number_status = '01'
    </delete>

    <select id="createNumberList" resultType="com.ruoyi.check.domain.vo.CreateNumberVO">
        select
            num.item_code as itemCode,
            num.package_id as packageId,
            num.qrcount,
            num.barcount,
            num.code_create_time as codeCreateTime,
            num.print_time as printTime,
            num.receive_time as receiveTime,
            num.handle_time as handleTime,
            num.number_status as numberStatus,
            num.sample_status as sampleStatus,
            num.handle_remark as handleRemark,
            num.report_result_time as reportResultTime,
            num.report_status as reportStatus,
            num.order_user_id as orderUserId,
            num.order_time as orderTime,
            num.check_id as checkId,
            num.check_family_id as checkFamilyId,
            num.check_family_age as checkFamilyAge,
            num.process_status as processStatus,
            pack.pakeage_name as pakeageName,
            fuser.user_name as orderUserName,
            family.name as checkFamilyName,
            family.sex as checkFamilySex,
            ck.name as checkName
        from front_number num
        left join front_package pack on num.package_id = pack.id
        left join front_user fuser on num.order_user_id = fuser.id
        left join front_family family on num.check_family_id = family.id
        left join front_check ck on num.check_id = ck.id
        <where>
            <if test="timeColumn != null and timeColumn != '' and startTime != null and startTime != '' and endTime != null and endTime != ''">
                and ${timeColumn} between #{startTime} and #{endTime}
            </if>
            <if test="isAllocation != null">
                <choose>
                    <when test="isAllocation">
                        AND NOT ISNULL(num.check_id)
                    </when>
                    <otherwise>
                        AND ISNULL(num.check_id)
                    </otherwise>
                </choose>
            </if>
            <if test="module != null and module != ''">
                <choose>
                    <when test="module == 'sample'">
                        and num.sample_status != '01'
                    </when>
                    <when test="module == 'report'">
                        and num.report_status != '01'
                    </when>
                </choose>
            </if>
            <if test="packageId != null ">
             and num.package_id = #{packageId}
            </if>
            <if test="numberStatus != null  and numberStatus != ''">
             and num.number_status = #{numberStatus}
            </if>
            <if test="prodnumberStatus != null and prodnumberStatus != ''">
                <choose>
                    <when test="prodnumberStatus == '01'">
                        and num.number_status = '01'
                    </when>
                    <otherwise>
                        and num.number_status in ('02','03')
                    </otherwise>
                </choose>
            </if>
            <if test="sampleStatus != null and sampleStatus != ''">
                and num.sample_status = #{sampleStatus}
            </if>
            <if test="reportStatus != null and reportStatus != ''">
                and num.report_status = #{reportStatus}
            </if>
            <if test="processStatus != null and processStatus != ''">
                and num.process_status = #{processStatus}
            </if>
            <if test="searchValue != null and searchValue != ''">
                and (pack.pakeage_name like CONCAT('%',#{searchValue},'%')
                    or num.item_code like CONCAT('%',#{searchValue},'%')
                    )
            </if>
        </where>
        <if test="orderByColumn != null and orderByColumn != ''">
            order by ${orderByColumn} ${orderByType}
        </if>
    </select>
    <select id="selectReport" resultType="com.ruoyi.check.domain.vo.CreateNumberVO">
        <include refid="sql-check-report"/>
        where num.item_code = #{itemCode}
    </select>
    <select id="selectItemCodeList" resultType="java.lang.String">
        select item_code from front_number
        where report_status = '01'
        and process_status = '06'
        and sample_status = '06'
    </select>
    <select id="getCheckList" resultType="com.ruoyi.user.domain.vo.UserReportInfoVO">
        SELECT
            num.item_code AS itemCode,
            fam.is_self AS isSelf,
            fam.`name` AS checkFamilyName,
            num.check_family_age AS checkFamilyAge,
            pack.pakeage_name AS pakeageName,
            sus.user_name AS reReviewName,
            num.report_result_time AS reportResultTime
        FROM front_number num
                 LEFT JOIN front_package pack ON pack.id=num.package_id
                 LEFT JOIN sys_user sus ON sus.user_id=num.report_re_review_family_id
                 LEFT JOIN front_family fam ON fam.id=num.check_family_id
        WHERE num.order_user_id=#{uid}
          AND num.report_status = '06'
    </select>
    <select id="selectReportUserNow" resultType="com.ruoyi.check.domain.vo.CreateNumberVO">
        <include refid="sql-check-report"/>
        WHERE num.order_user_id = #{uid}
        AND num.report_status = '06'
        ORDER BY num.report_re_review_time DESC
        LIMIT 1
    </select>
    <select id="getNumberStart" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(CAST(SUBSTR(item_code,8) AS UNSIGNED)),0) + 1
        FROM front_number num
                 LEFT JOIN front_package pac ON num.package_id=pac.id
        WHERE pac.number=#{itemNumber} AND num.number=#{number}
    </select>
    <select id="selectReportBaseInfo" resultType="com.ruoyi.check.domain.vo.ReportBaseInfoVO">
        select
            num.item_code as itemCode,
            pack.pakeage_name as pakeageName,
            num.report_result_time as reportResultTime,
            family.name as checkFamilyName,
            num.health_advice_total as healthAdviceTotal,
            num.tmp_id as tmpId
        from front_number num
                 left join front_package pack on num.package_id = pack.id
                 left join front_family family on num.check_family_id = family.id
        where num.item_code = #{itemCode}
    </select>
    <!--公共分页和排序-->
    <sql id="pageAndOrderCommon">
        <if test="isPage">
            LIMIT #{pageStart},#{pageSize}
        </if>
        <if test="orderByColumn != null and orderByColumn != ''">
            ORDER BY ${orderByColumn} ${orderByType}
        </if>
    </sql>
    <sql id="sql-check-report">
        select
            num.item_code as itemCode,
            num.package_id as packageId,
            num.qrcount,
            num.barcount,
            num.code_create_time as codeCreateTime,
            num.print_time as printTime,
            num.receive_time as receiveTime,
            num.handle_time as handleTime,
            num.report_result_time as reportResultTime,
            num.number_status as numberStatus,
            num.sample_status as sampleStatus,
            num.handle_remark as handleRemark,
            num.report_status as reportStatus,
            num.order_user_id as orderUserId,
            num.order_time as orderTime,
            num.check_id as checkId,
            num.check_family_id as checkFamilyId,
            num.check_family_age as checkFamilyAge,
            pack.pakeage_name as pakeageName,
            fuser.user_name as orderUserName,
            family.name as checkFamilyName,
            family.sex as checkFamilySex,
            ck.name as checkName,
            num.report_review_notes as reportReviewNotes,
            num.report_re_review_notes as reportReReviewNotes,
            num.process_status as processStatus,
            num.health_advice_total as healthAdviceTotal
        from front_number num
                 left join front_package pack on num.package_id = pack.id
                 left join front_user fuser on num.order_user_id = fuser.id
                 left join front_family family on num.check_family_id = family.id
                 left join front_check ck on num.check_id = ck.id
    </sql>
</mapper>