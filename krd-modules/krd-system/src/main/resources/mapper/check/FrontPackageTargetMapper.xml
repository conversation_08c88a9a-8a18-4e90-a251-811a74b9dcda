<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.check.mapper.FrontPackageTargetMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.check.domain.entity.FrontPackageTargetEntity" id="frontPackageTargetMap">
        <result property="id" column="id"/>
        <result property="targetCode" column="target_code"/>
        <result property="targetName" column="target_name"/>
        <result property="packageId" column="package_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>
    <select id="selectPactarList" resultType="com.ruoyi.check.domain.dto.PactarDTO">
        SELECT
            tg.package_id AS packageId,
            tg.id AS targetId,
            tg.target_code AS targetCode,
            tg.target_name AS targetName,
            pt.target_analysis AS targetAnalysis,
            pt.target_reference_range AS targetReferenceRange,
            pt.id AS pactarId
        FROM
            front_package_target tg
                LEFT JOIN front_tmp_pactar pt ON pt.target_id=tg.id
        WHERE
            tg.is_del = 0
          AND tg.package_id = #{packageId}
    </select>


</mapper>