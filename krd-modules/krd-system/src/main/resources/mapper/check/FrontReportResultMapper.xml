<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.check.mapper.FrontReportResultMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.check.domain.entity.FrontReportResultEntity" id="frontReportResultMap">
        <result property="id" column="id"/>
        <result property="itemCode" column="item_code"/>
        <result property="pactarId" column="pactar_id"/>
        <result property="checkTarget" column="check_target"/>
        <result property="checkTargetAbb" column="check_target_abb"/>
        <result property="referenceRange" column="reference_range"/>
        <result property="checkTargetResult" column="check_target_result"/>
        <result property="unit" column="unit"/>
        <result property="checkTime" column="check_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="ptrId" column="ptr_id"/>
        <result property="testState" column="test_state"/>
        <result property="resultColor" column="result_color"/>
        <result property="resultInterpret" column="result_interpret"/>
        <result property="healthAdvice" column="health_advice"/>
    </resultMap>


</mapper>