<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.check.mapper.FrontTmpMapper">

    <resultMap type="com.ruoyi.check.domain.entity.FrontTmp" id="FrontTmpResult">
        <result property="id"    column="id"    />
        <result property="tmpName"    column="tmp_name"    />
        <result property="packageId"    column="package_id"    />
        <result property="fileAddress"    column="file_address"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>
    <resultMap id="FrontTmpListMap" type="com.ruoyi.check.domain.vo.FrontTmpListVO" extends="FrontTmpResult">
        <result property="pakeageName"    column="pakeage_name" />
    </resultMap>

    <sql id="selectFrontTmpVo">
        select id, tmp_name, package_id, file_address, create_time, update_time, is_del, create_by, update_by from front_tmp
    </sql>

    <select id="selectFrontTmpList" parameterType="com.ruoyi.check.domain.entity.FrontTmp" resultMap="FrontTmpResult">
        <include refid="selectFrontTmpVo"/>
        <where>
            and is_del = 0
            <if test="tmpName != null  and tmpName != ''"> and tmp_name like concat('%', #{tmpName}, '%')</if>
            <if test="packageId != null "> and package_id like concat('%', #{packageId}, '%')</if>
        </where>
    </select>

    <select id="selectFrontTmpById" parameterType="Long" resultMap="FrontTmpResult">
        <include refid="selectFrontTmpVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontTmp" parameterType="com.ruoyi.check.domain.entity.FrontTmp" useGeneratedKeys="true" keyProperty="id">
        insert into front_tmp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tmpName != null">tmp_name,</if>
            <if test="packageId != null">package_id,</if>
            <if test="fileAddress != null">file_address,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tmpName != null">#{tmpName},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="fileAddress != null">#{fileAddress},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateFrontTmp" parameterType="com.ruoyi.check.domain.entity.FrontTmp">
        update front_tmp
        <trim prefix="SET" suffixOverrides=",">
            <if test="tmpName != null">tmp_name = #{tmpName},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="fileAddress != null">file_address = #{fileAddress},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontTmpById" parameterType="Long">
        update front_tmp
        set is_del = 1
        where id = #{id}
    </delete>

    <delete id="deleteFrontTmpByIds" parameterType="String">
        update front_tmp
        set is_del = 1
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTmpList" parameterType="com.ruoyi.check.domain.entity.FrontTmp" resultMap="FrontTmpListMap">
        select
            tmp.id,
            tmp.tmp_name,
            pa.pakeage_name,
            tmp.create_time
        from front_tmp tmp
        left join front_package pa on tmp.package_id=pa.id
        <where>
            and tmp.is_del = 0
            <if test="tmpName != null  and tmpName != ''">
                and tmp.tmp_name like concat('%', #{tmpName}, '%')
            </if>
        </where>
    </select>
</mapper>