<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.check.mapper.FrontTmpPactarMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.check.domain.entity.FrontTmpPactarEntity" id="frontTmpPactarMap">
        <result property="id" column="id"/>
        <result property="tmpId" column="tmp_id"/>
        <result property="packageId" column="package_id"/>
        <result property="targetId" column="target_id"/>
        <result property="targetAnalysis" column="target_analysis"/>
        <result property="targetReferenceRange" column="target_reference_range"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>
    <select id="selectPactarList" resultType="com.ruoyi.check.domain.vo.ReportPactarVO">
        select
            rpsu.check_target_abb as targetCode,
            rpsu.check_target as targetName,
            tp.target_id as targetId,
            tp.target_analysis as targetAnalysis
        from front_report_result rpsu
        inner join front_tmp_pactar tp on rpsu.pactar_id = tp.id
        where rpsu.item_code=#{itemCode} and tp.tmp_id=#{tmpId}
    </select>
    <select id="selectTmpModel" resultType="com.ruoyi.check.domain.dto.PactarResultDTO">
        SELECT
            pt.id AS pactarId,
            pt.package_id AS packageId,
            pt.target_id AS targetId,
            tar.target_name AS targetName,
            pt.target_analysis AS targetAnalysis,
            pt.target_reference_range AS targetReferenceRange,
            pt.is_del AS ptIsDel,

            ptr.id AS prtId,
            ptr.result_name AS resultName,
            ptr.result_color AS resultColor,
            ptr.result_range_start_val AS resultRangeStartVal,
            ptr.result_range_start_symbol AS resultRangeStartSymbol,
            ptr.result_range_end_val AS resultRangeEndVal,
            ptr.result_range_end_symbol AS resultRangeEndSymbol,
            ptr.result_interpret AS resultInterpret,
            ptr.health_advice AS healthAdvice,
            ptr.is_del AS isDel
        FROM front_tmp_pactar pt
                 LEFT JOIN front_tmp_pactar_result ptr ON ptr.pactar_id=pt.id
                 LEFT JOIN front_package_target tar ON pt.target_id=tar.id
        WHERE pt.tmp_id=#{tmpId}
    </select>

</mapper>