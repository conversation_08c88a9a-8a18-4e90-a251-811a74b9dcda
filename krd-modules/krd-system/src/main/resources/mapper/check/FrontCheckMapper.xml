<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.check.mapper.FrontCheckMapper">

    <resultMap type="com.ruoyi.check.domain.entity.FrontCheck" id="FrontCheckResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectFrontCheckVo">
        select id, name, create_time, update_time, is_del, create_by, update_by from front_check
    </sql>

    <select id="selectFrontCheckList" parameterType="com.ruoyi.check.domain.entity.FrontCheck" resultMap="FrontCheckResult">
        <include refid="selectFrontCheckVo"/>
        <where>
            and is_del = 0
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
    </select>

    <select id="selectFrontCheckById" parameterType="Long" resultMap="FrontCheckResult">
        <include refid="selectFrontCheckVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontCheck" parameterType="com.ruoyi.check.domain.entity.FrontCheck">
        insert into front_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateFrontCheck" parameterType="com.ruoyi.check.domain.entity.FrontCheck">
        update front_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontCheckById" parameterType="Long">
        delete from front_check where id = #{id}
    </delete>

    <delete id="deleteFrontCheckByIds" parameterType="String">
        update front_check
        set is_del = 1
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>