<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.check.mapper.HisReportMapper">

    <!--检测患者历史报告表-->
    <resultMap id="patientsMap" type="com.ruoyi.check.domain.entity.TTestPatientsHisReportEntity">
        <result property="ptid" column="PTID"/>
        <result property="ptInstId" column="PT_INST_ID"/>
        <result property="id" column="ID"/>
        <result property="applyDate" column="APPLY_DATE"/>
        <result property="sampleDate" column="SAMPLE_DATE"/>
        <result property="receivedDate" column="RECEIVED_DATE"/>
        <result property="resultDate" column="RESULT_DATE"/>
        <result property="executeDate" column="EXECUTE_DATE"/>
        <result property="bedNo" column="BED_NO"/>
        <result property="printIndicator" column="PRINT_INDICATOR"/>
        <result property="emergency" column="EMERGENCY"/>
        <result property="printAllow" column="PRINT_ALLOW"/>
        <result property="ifprint" column="IFPRINT"/>
        <result property="ifcharge" column="IFCHARGE"/>
        <result property="charges" column="CHARGES"/>
        <result property="costs" column="COSTS"/>
        <result property="sex" column="SEX"/>
        <result property="age" column="AGE"/>
        <result property="specimanState" column="SPECIMAN_STATE"/>
        <result property="patType" column="PAT_TYPE"/>
        <result property="costType" column="COST_TYPE"/>
        <result property="visitId" column="VISIT_ID"/>
        <result property="region" column="REGION"/>
        <result property="memos" column="MEMOS"/>
        <result property="name" column="NAME"/>
        <result property="old" column="OLD"/>
        <result property="diagnose" column="DIAGNOSE"/>
        <result property="performedDept" column="PERFORMED_DEPT"/>
        <result property="deptName" column="DEPT_NAME"/>
        <result property="doctorName" column="DOCTOR_NAME"/>
        <result property="reportDoctor" column="REPORT_DOCTOR"/>
        <result property="verifiedDoctor" column="VERIFIED_DOCTOR"/>
        <result property="speciman" column="SPECIMAN"/>
        <result property="testAid" column="TEST_AID"/>
        <result property="period" column="PERIOD"/>
        <result property="barCode" column="BAR_CODE"/>
        <result property="hospName" column="HOSP_NAME"/>
        <result property="execMan" column="EXEC_MAN"/>
        <result property="sendMan" column="SEND_MAN"/>
        <result property="receMan" column="RECE_MAN"/>
        <result property="reexamMan" column="REEXAM_MAN"/>
        <result property="printMan" column="PRINT_MAN"/>
        <result property="printTime" column="PRINT_TIME"/>
        <result property="resultSummary" column="RESULT_SUMMARY"/>
        <result property="remotingBarCode" column="REMOTING_BAR_CODE"/>
        <result property="diag" column="DIAG"/>
        <result property="testMethod" column="TEST_METHOD"/>
        <result property="cellMemoes" column="CELL_MEMOES"/>
        <result property="imageCount" column="IMAGE_COUNT"/>
        <result property="cellCode" column="CELL_CODE"/>
        <result property="ceSendTissue" column="CE_SEND_TISSUE"/>
        <result property="memo" column="MEMO"/>
        <result property="icHospName" column="IC_HOSP_NAME"/>
        <result property="sampleId" column="SAMPLE_ID"/>
        <result property="pathologicalId" column="PATHOLOGICAL_ID"/>
        <result property="pathologicalDiag" column="PATHOLOGICAL_DIAG"/>
        <result property="resultType" column="RESULT_TYPE"/>
        <result property="testCode" column="TEST_CODE"/>
        <result property="viewType" column="VIEW_TYPE"/>
        <result property="viewLow" column="VIEW_LOW"/>
        <result property="viewHigh" column="VIEW_HIGH"/>
        <result property="filePathAddress" column="FILE_PATH_ADDRESS"/>
        <result property="costTest" column="COST_TEST"/>
        <result property="sendState" column="SEND_STATE"/>
        <result property="costTestInstr" column="COST_TEST_INSTR"/>
    </resultMap>

    <!-- 检测结果历史报告表 -->
    <resultMap id="resultsMap" type="com.ruoyi.check.domain.entity.TTestResultsHisReportEntity">
        <result property="ptid" column="PTID"/>
        <result property="testId" column="TEST_ID"/>
        <result property="testCname" column="TEST_CNAME"/>
        <result property="testResult" column="TEST_RESULT"/>
        <result property="testMemo" column="TEST_MEMO"/>
        <result property="instId" column="INST_ID"/>
        <result property="testDate" column="TEST_DATE"/>
        <result property="printNumber" column="PRINT_NUMBER"/>
        <result property="reportResult" column="REPORT_RESULT"/>
        <result property="reference" column="REFERENCE"/>
        <result property="testState" column="TEST_STATE"/>
        <result property="unit" column="UNIT"/>
        <result property="id" column="ID"/>
        <result property="testEname" column="TEST_ENAME"/>
        <result property="hospTestId" column="HOSP_TEST_ID"/>
        <result property="hospBarCode" column="HOSP_BAR_CODE"/>
        <result property="reportDoctor" column="REPORT_DOCTOR"/>
        <result property="verifiedDoctor" column="VERIFIED_DOCTOR"/>
        <result property="visitId" column="VISIT_ID"/>
        <result property="patName" column="PAT_NAME"/>
        <result property="resultImage" column="RESULT_IMAGE"/>
        <result property="sendState" column="SEND_STATE"/>
        <result property="testMethodMemo" column="TEST_METHOD_MEMO"/>
    </resultMap>
    <select id="getPatientsListByItemCode" resultMap="patientsMap">
        SELECT *
        FROM T_TEST_PATIENTS_HIS_REPORT
        WHERE REMOTING_BAR_CODE in
              <foreach collection="itemCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
    </select>
    <select id="getResultsListByItemCode" resultMap="resultsMap">
        SELECT *
        FROM T_TEST_RESULTS_HIS_REPORT
        WHERE HOSP_BAR_CODE in
        <foreach collection="itemCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>