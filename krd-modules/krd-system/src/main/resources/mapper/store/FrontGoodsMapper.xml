<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.store.mapper.FrontGoodsMapper">
    
    <resultMap type="com.ruoyi.system.api.domain.FrontGoods" id="FrontGoodsResult">
        <result property="id"    column="id"    />
        <result property="goodsType"    column="goods_type"    />
        <result property="packageId"    column="package_id"    />
        <result property="gategoryId"    column="gategory_id"    />
        <result property="name"    column="name"    />
        <result property="subtitle"    column="subtitle"    />
        <result property="price"    column="price"    />
        <result property="vipPrice"    column="vip_price"    />
        <result property="amount"    column="amount"    />
        <result property="goodsNo"    column="goods_no"    />
        <result property="isStatus"    column="is_status"    />
        <result property="featuredFirst"    column="featured_first"    />
        <result property="indexImage"    column="index_image"    />
        <result property="firstPic"    column="first_pic"    />
        <result property="banner"    column="banner"    />
        <result property="unit"    column="unit"    />
        <result property="keywords"    column="keywords"    />
        <result property="details"    column="details"    />
        <result property="specId"    column="spec_id"    />
        <result property="reportInfo"    column="report_info"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectFrontGoodsVo">
        select id, goods_type, package_id, gategory_id, name, subtitle, price, vip_price, amount, goods_no, is_status, featured_first, index_image, first_pic, banner, unit, keywords, details, spec_id, report_info, create_time, update_time, create_by, update_by, is_del from front_goods
    </sql>

    <select id="selectFrontGoodsList" resultType="com.ruoyi.system.api.domain.FrontGoods">
        select * from front_goods
        <where>
            <if test="query.keyword != null ">
                and (id = #{query.keyword} or name like concat('%',#{query.keyword},'%'))
            </if>
            <if test="query.goodsType != null "> and goods_type = #{query.goodsType}</if>
            <if test="query.categoryId != null"> and (gategory_id = #{query.categoryId} or package_id = #{query.categoryId})</if>
        </where>
    </select>
    
    <select id="selectFrontGoodsById" parameterType="Long" resultMap="FrontGoodsResult">
        <include refid="selectFrontGoodsVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontGoods" parameterType="com.ruoyi.system.api.domain.FrontGoods" useGeneratedKeys="true" keyProperty="id">
        insert into front_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="goodsType != null">goods_type,</if>
            <if test="packageId != null">package_id,</if>
            <if test="gategoryId != null">gategory_id,</if>
            <if test="name != null">name,</if>
            <if test="subtitle != null">subtitle,</if>
            <if test="price != null">price,</if>
            <if test="amount != null">amount,</if>
            <if test="goodsNo != null">goods_no,</if>
            <if test="isStatus != null">is_status,</if>
            <if test="featuredFirst != null">featured_first,</if>
            <if test="indexImage != null">index_image,</if>
            <if test="firstPic != null">first_pic,</if>
            <if test="banner != null">banner,</if>
            <if test="unit != null">unit,</if>
            <if test="keywords != null">keywords,</if>
            <if test="details != null">details,</if>
            <if test="specId != null">spec_id,</if>
            <if test="reportInfo != null">report_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="goodsType != null">#{goodsType},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="gategoryId != null">#{gategoryId},</if>
            <if test="name != null">#{name},</if>
            <if test="subtitle != null">#{subtitle},</if>
            <if test="price != null">#{price},</if>
            <if test="vipPrice != null">#{vipPrice},</if>
            <if test="amount != null">#{amount},</if>
            <if test="goodsNo != null">#{goodsNo},</if>
            <if test="isStatus != null">#{isStatus},</if>
            <if test="featuredFirst != null">#{featuredFirst},</if>
            <if test="firstPic != null">#{firstPic},</if>
            <if test="banner != null">#{banner},</if>
            <if test="unit != null">#{unit},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="details != null">#{details},</if>
            <if test="specId != null">#{specId},</if>
            <if test="reportInfo != null">#{reportInfo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateFrontGoods" parameterType="com.ruoyi.system.api.domain.FrontGoods">
        update front_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodsType != null">goods_type = #{goodsType},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="gategoryId != null">gategory_id = #{gategoryId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="subtitle != null">subtitle = #{subtitle},</if>
            <if test="price != null">price = #{price},</if>
            <if test="vipPrice != null">vip_price = #{vipPrice},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="goodsNo != null">goods_no = #{goodsNo},</if>
            <if test="isStatus != null">is_status = #{isStatus},</if>
            <if test="featuredFirst != null">featured_first = #{featuredFirst},</if>
            <if test="firstPic != null">first_pic = #{firstPic},</if>
            <if test="banner != null">banner = #{banner},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="keywords != null">keywords = #{keywords},</if>
            <if test="details != null">details = #{details},</if>
            <if test="specId != null">spec_id = #{specId},</if>
            <if test="reportInfo != null">report_info = #{reportInfo},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontGoodsById" parameterType="Long">
        delete from front_goods where id = #{id}
    </delete>

    <delete id="deleteFrontGoodsByIds" parameterType="String">
        delete from front_goods where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>