<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.operate.mapper.FrontTodoMapper">

    <resultMap type="FrontTodo" id="FrontTodoResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="title"    column="title"    />
        <result property="createTime"    column="create_time"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectFrontTodoVo">
        select id, type, title, create_time, status from front_todo
    </sql>

    <select id="selectFrontTodoList" parameterType="FrontTodo" resultMap="FrontTodoResult">
        <include refid="selectFrontTodoVo"/>
        <where>
            <if test="type != null "> and type = #{type}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectFrontTodoById" parameterType="Long" resultMap="FrontTodoResult">
        <include refid="selectFrontTodoVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontTodo" parameterType="FrontTodo" useGeneratedKeys="true" keyProperty="id">
        insert into front_todo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="title != null">title,</if>
            <if test="createTime != null">create_time,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="title != null">#{title},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateFrontTodo" parameterType="FrontTodo">
        update front_todo
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="title != null">title = #{title},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontTodoById" parameterType="Long">
        delete from front_todo where id = #{id}
    </delete>

    <delete id="deleteFrontTodoByIds" parameterType="String">
        delete from front_todo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
