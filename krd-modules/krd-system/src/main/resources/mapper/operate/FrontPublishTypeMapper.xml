<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.operate.mapper.FrontPublishTypeMapper">

    <resultMap type="com.ruoyi.operate.domain.FrontPublishType" id="FrontPublishTypeResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectFrontPublishTypeVo">
        select id, name, is_del from front_publish_type
    </sql>

    <select id="selectFrontPublishTypeList" parameterType="com.ruoyi.operate.domain.FrontPublishType" resultMap="FrontPublishTypeResult">
        <include refid="selectFrontPublishTypeVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>

    <select id="selectFrontPublishTypeById" parameterType="Long" resultMap="FrontPublishTypeResult">
        <include refid="selectFrontPublishTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontPublishType" parameterType="com.ruoyi.operate.domain.FrontPublishType" useGeneratedKeys="true" keyProperty="id">
        insert into front_publish_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateFrontPublishType" parameterType="com.ruoyi.operate.domain.FrontPublishType">
        update front_publish_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontPublishTypeById" parameterType="Long">
        delete from front_publish_type where id = #{id}
    </delete>

    <delete id="deleteFrontPublishTypeByIds" parameterType="String">
        delete from front_publish_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
