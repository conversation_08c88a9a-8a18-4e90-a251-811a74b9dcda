<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.operate.mapper.FrontPublishMapper">

    <resultMap type="com.ruoyi.operate.domain.FrontPublish" id="FrontPublishResult">
        <result property="id"    column="id"    />
        <result property="messageId"    column="message_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="picUrl"    column="pic_url"    />
        <result property="linkUrl"    column="link_url"    />
        <result property="tagId"    column="tag_id"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="publishType"    column="publish_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
        <result property="publishCount"    column="publish_count"    />
        <result property="status"    column="status"    />
        <result property="publishNum"    column="publish_num"    />
        <result property="receiveNum"    column="receive_num"    />
    </resultMap>

    <sql id="selectFrontPublishVo">
        select id, message_id, title,publish_num ,receive_num,content, pic_url, link_url, tag_id, publish_date, publish_type, create_time, update_time, create_by, update_by, is_del, publish_count, status from front_publish
    </sql>

    <select id="selectFrontPublishList" parameterType="com.ruoyi.operate.domain.FrontPublish" resultMap="FrontPublishResult">
        select fp.*,fpt.name as messageName from front_publish fp
        inner join front_publish_type fpt on fp.message_id = fpt.id
        <where>
            <if test="messageId != null "> and message_id = #{messageId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="picUrl != null  and picUrl != ''"> and pic_url = #{picUrl}</if>
            <if test="linkUrl != null  and linkUrl != ''"> and link_url = #{linkUrl}</if>
            <if test="tagId != null "> and tag_id = #{tagId}</if>
            <if test="publishDate != null "> and publish_date = #{publishDate}</if>
            <if test="publishType != null "> and publish_type = #{publishType}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="publishCount != null "> and publish_count = #{publishCount}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectFrontPublishById" parameterType="Long" resultMap="FrontPublishResult">
        <include refid="selectFrontPublishVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontPublish" parameterType="com.ruoyi.operate.domain.FrontPublish" useGeneratedKeys="true" keyProperty="id">
        insert into front_publish
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="messageId != null">message_id,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="picUrl != null">pic_url,</if>
            <if test="linkUrl != null">link_url,</if>
            <if test="tagId != null">tag_id,</if>
            <if test="publishDate != null">publish_date,</if>
            <if test="publishType != null">publish_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="publishNum != null">publish_num,</if>
            <if test="receiveNum != null">receive_num,</if>
            <if test="publishCount != null">publish_count,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="messageId != null">#{messageId},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="picUrl != null">#{picUrl},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="tagId != null">#{tagId},</if>
            <if test="publishDate != null">#{publishDate},</if>
            <if test="publishType != null">#{publishType},</if>
            <if test="publishNum != null">#{publishNum},</if>
            <if test="receiveNum != null">#{receiveNum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="publishCount != null">#{publishCount},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateFrontPublish" parameterType="com.ruoyi.operate.domain.FrontPublish">
        update front_publish
        <trim prefix="SET" suffixOverrides=",">
            <if test="messageId != null">message_id = #{messageId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="picUrl != null">pic_url = #{picUrl},</if>
            <if test="linkUrl != null">link_url = #{linkUrl},</if>
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="publishDate != null">publish_date = #{publishDate},</if>
            <if test="publishType != null">publish_type = #{publishType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="publishCount != null">publish_count = #{publishCount},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontPublishById" parameterType="Long">
        delete from front_publish where id = #{id}
    </delete>

    <delete id="deleteFrontPublishByIds" parameterType="String">
        delete from front_publish where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
