package com.ruoyi.check.controller;

import com.ruoyi.check.domain.dto.NumberQueryDTO;
import com.ruoyi.check.domain.dto.ReportExamineBatchDTO;
import com.ruoyi.check.domain.dto.ReportExamineDTO;
import com.ruoyi.check.domain.vo.CreateNumberVO;
import com.ruoyi.check.service.IFrontNumberService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 报告管理
 * <AUTHOR>
 * @date 2025/5/29 16:30
 */
@RestController
@RequestMapping("check/reportmanager")
public class ReportManagerController extends BaseController {

    @Autowired
    private IFrontNumberService frontNumberService;

    @GetMapping("/list")
    public TableDataInfo queryNumberList(NumberQueryDTO queryDTO)
    {
        startPage();
        queryDTO.setModule("report");
        List<CreateNumberVO> list = frontNumberService.createNumberList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 查询审核报告详情
     * @param itemCode
     * @return
     */
    @GetMapping("/report/details/examine/{itemCode}")
    public AjaxResult queryReportDetailsExamine(@PathVariable("itemCode") String itemCode)
    {
        return success(frontNumberService.queryReportDetailsExamine(itemCode));
    }

    /**
     * 审核报告
     * @param model
     * @return
     */
    @PutMapping("report/examine")
    public R reportExamine(@RequestBody ReportExamineDTO model)
    {
        frontNumberService.reportExamine(model);
        return R.ok();
    }

    /**
     * 复核报告
     * @param model
     * @return
     */
    @PutMapping("report/examineRe")
    public R reportExamineRe(@RequestBody ReportExamineDTO model)
    {
        frontNumberService.reportExamineRe(model);
        return R.ok();
    }

    /**
     * 批量审核报告
     * @param model
     * @return
     */
    @PutMapping("report/examineBatch")
    public R reportExamineBatch(@RequestBody ReportExamineBatchDTO model)
    {
        frontNumberService.reportExamineBatch(model);
        return R.ok();
    }

    /**
     * 批量复核报告
     * @param model
     * @return
     */
    @PutMapping("report/examineReBatch")
    public R reportExamineReBatch(@RequestBody ReportExamineBatchDTO model)
    {
        frontNumberService.reportExamineReBatch(model);
        return R.ok();
    }

    /**
     * 查询用户最新的报告详情
     * @param uid 用户id
     * @return
     */
    @GetMapping("/report/details/usernow/{uid}")
    public AjaxResult queryReportDetailsUsernow(@PathVariable("uid") String uid)
    {
        return success(frontNumberService.queryReportDetailsUsernow(uid));
    }

    /**
     * 报告预览
     * @param itemCode
     * @return
     */
    @GetMapping("/report/preview/{itemCode}")
    public AjaxResult reportPreview(@PathVariable("itemCode") String itemCode)
    {
        return success(frontNumberService.reportPreview(itemCode));
    }
}
