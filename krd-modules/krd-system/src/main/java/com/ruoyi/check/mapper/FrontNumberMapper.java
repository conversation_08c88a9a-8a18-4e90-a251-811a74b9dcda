package com.ruoyi.check.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.check.domain.dto.NumberQueryDTO;
import com.ruoyi.check.domain.dto.PrintReqParam;
import com.ruoyi.check.domain.entity.FrontNumber;
import com.ruoyi.check.domain.vo.CreateNumberVO;
import com.ruoyi.check.domain.vo.ReportBaseInfoVO;
import com.ruoyi.user.domain.dto.UserReportQueryDTO;
import com.ruoyi.user.domain.vo.UserReportInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 编码管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface FrontNumberMapper extends BaseMapper<FrontNumber>
{
    /**
     * 查询编码管理
     * 
     * @param id 编码管理主键
     * @return 编码管理
     */
    public FrontNumber selectFrontNumberById(Long id);

    /**
     * 查询编码管理列表
     * 
     * @param frontNumber 编码管理
     * @return 编码管理集合
     */
    public List<FrontNumber> selectFrontNumberList(FrontNumber frontNumber);

    /**
     * 新增编码管理
     * 
     * @param frontNumber 编码管理
     * @return 结果
     */
    public int insertFrontNumber(FrontNumber frontNumber);

    /**
     * 修改编码管理
     * 
     * @param frontNumber 编码管理
     * @return 结果
     */
    public int updateFrontNumber(FrontNumber frontNumber);

    /**
     * 删除编码管理
     * 
     * @param id 编码管理主键
     * @return 结果
     */
    public int deleteFrontNumberById(Long id);

    /**
     * 批量删除编码管理
     * 只能删除状态为已生成的，已打印编码的不可删除
     * @param itemCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontNumberByIds(@Param("itemCodes") String[] itemCodes);

    List<CreateNumberVO> createNumberList(NumberQueryDTO queryDTO);

    int printDataUpdate(PrintReqParam printReqParam);

    int printDataUpdateRe(PrintReqParam printReqParam);

    CreateNumberVO selectReport(@Param("itemCode") String itemCode);

    CreateNumberVO selectReportUserNow(@Param("uid") String uid);

    /**
     * 查询待同步检测数据的项目编码
     * process_status：06-已处理（不合格，合格）
     * sample_status：06-检测中（合格）
     * report_status：01-未出报告
     * @return
     */
    List<String> selectItemCodeList();

    List<UserReportInfoVO> getCheckList(UserReportQueryDTO queryDTO);

    /**
     * 查询项目编码的起始编号
     * @param itemNumber
     * @param number
     * @return
     */
    int getNumberStart(@Param("itemNumber") String itemNumber,@Param("number") String number);

    ReportBaseInfoVO selectReportBaseInfo(@Param("itemCode") String itemCode);
}
