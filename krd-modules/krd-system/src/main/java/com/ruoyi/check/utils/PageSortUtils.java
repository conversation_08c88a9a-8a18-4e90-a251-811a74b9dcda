package com.ruoyi.check.utils;

import com.ruoyi.check.domain.dto.QueryBaseEntity;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30 10:55
 */
public class PageSortUtils {
    /**
     * 对列表进行分页
     * @param list         原始数据列表
     * @param queryBaseEntity 查询参数
     * @param <T>          数据类型
     * @return 分页后的结果
     */
    public static <T> List<T> page(List<T> list, QueryBaseEntity queryBaseEntity) {
        return paginateAndSort(list, queryBaseEntity.getPageNum(), queryBaseEntity.getPageSize(), null, null);
    }
    /**
     * 对列表进行排序并分页
     * @param list         原始数据列表
     * @param queryBaseEntity 查询参数
     * @param <T>          数据类型
     * @return 分页+排序后的结果
     */
    public static <T> List<T> pageAndSort(List<T> list, QueryBaseEntity queryBaseEntity) {
        return paginateAndSort(list, queryBaseEntity.getPageNum(), queryBaseEntity.getPageSize(), queryBaseEntity.getOrderByColumn(), queryBaseEntity.getOrderByType());
    }

    /**
     * 对列表进行排序并分页
     *
     * @param list         原始数据列表
     * @param pageNum      当前页码（从1开始）
     * @param pageSize     每页数量
     * @param sortField    排序字段名（可为 null 表示不排序）
     * @param sortOrder    排序方向 "asc" 或 "desc"
     * @param <T>          数据类型
     * @return 分页+排序后的结果
     */
    public static <T> List<T> paginateAndSort(List<T> list, int pageNum, int pageSize,
                                               String sortField, String sortOrder) {
        if (list == null || list.isEmpty()) return Collections.emptyList();

        // 排序逻辑
        List<T> sortedList = new ArrayList<>(list);
        if (sortField != null && !sortField.isEmpty()) {
            sortedList.sort((o1, o2) -> compareObjects(o1, o2, sortField, sortOrder));
        }

        // 分页逻辑
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, sortedList.size());

        if (start >= sortedList.size()) return Collections.emptyList();
        return sortedList.subList(start, end);
    }

    private static <T> int compareObjects(T o1, T o2, String field, String order) {
        try {
            Field f = o1.getClass().getDeclaredField(field);
            f.setAccessible(true);
            Comparable val1 = (Comparable) f.get(o1);
            Comparable val2 = (Comparable) f.get(o2);

            int result = val1.compareTo(val2);
            return "desc".equalsIgnoreCase(order) ? -result : result;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("无法访问字段: " + field, e);
        }
    }

}
