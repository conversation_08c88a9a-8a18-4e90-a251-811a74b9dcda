package com.ruoyi.check.domain.dto;

import com.ruoyi.check.domain.vo.CreateNumberVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26 11:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportExamineBatchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<CreateNumberVO> numberList;

    private CreateNumberVO numberDetails;

}
