package com.ruoyi.check.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.check.mapper.FrontCheckMapper;
import com.ruoyi.check.domain.entity.FrontCheck;
import com.ruoyi.check.service.IFrontCheckService;

/**
 * 检测机构Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
public class FrontCheckServiceImpl implements IFrontCheckService
{
    @Autowired
    private FrontCheckMapper frontCheckMapper;
    @Autowired
    private TokenService tokenService;

    /**
     * 查询检测机构
     *
     * @param id 检测机构主键
     * @return 检测机构
     */
    @Override
    public FrontCheck selectFrontCheckById(Long id)
    {
        return frontCheckMapper.selectFrontCheckById(id);
    }

    /**
     * 查询检测机构列表
     *
     * @param frontCheck 检测机构
     * @return 检测机构
     */
    @Override
    public List<FrontCheck> selectFrontCheckList(FrontCheck frontCheck)
    {
        return frontCheckMapper.selectFrontCheckList(frontCheck);
    }

    /**
     * 新增检测机构
     *
     * @param frontCheck 检测机构
     * @return 结果
     */
    @Override
    public int insertFrontCheck(FrontCheck frontCheck)
    {
        LoginUser user = tokenService.getLoginUser();
        frontCheck.setCreateBy(user.getUsername());
        frontCheck.setCreateTime(DateUtils.getNowDate());
        frontCheck.setUpdateBy(user.getUsername());
        frontCheck.setUpdateTime(DateUtils.getNowDate());
        return frontCheckMapper.insertFrontCheck(frontCheck);
    }

    /**
     * 修改检测机构
     *
     * @param frontCheck 检测机构
     * @return 结果
     */
    @Override
    public int updateFrontCheck(FrontCheck frontCheck)
    {
        LoginUser user = tokenService.getLoginUser();
        frontCheck.setUpdateBy(user.getUsername());
        frontCheck.setUpdateTime(DateUtils.getNowDate());
        return frontCheckMapper.updateFrontCheck(frontCheck);
    }

    /**
     * 批量删除检测机构
     *
     * @param ids 需要删除的检测机构主键
     * @return 结果
     */
    @Override
    public int deleteFrontCheckByIds(Long[] ids)
    {
        return frontCheckMapper.deleteFrontCheckByIds(ids);
    }

    /**
     * 删除检测机构信息
     *
     * @param id 检测机构主键
     * @return 结果
     */
    @Override
    public int deleteFrontCheckById(Long id)
    {
        return frontCheckMapper.deleteFrontCheckById(id);
    }
}