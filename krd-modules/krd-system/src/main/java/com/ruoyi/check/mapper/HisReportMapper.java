package com.ruoyi.check.mapper;

import com.ruoyi.check.domain.entity.TTestPatientsHisReportEntity;
import com.ruoyi.check.domain.entity.TTestResultsHisReportEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检测结果历史报告表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-16 14:46:03
 */
@Mapper
public interface HisReportMapper {

    List<TTestPatientsHisReportEntity> getPatientsListByItemCode(@Param("itemCodes") List<String> itemCodes);

    List<TTestResultsHisReportEntity> getResultsListByItemCode(@Param("itemCodes") List<String> itemCodes);
}
