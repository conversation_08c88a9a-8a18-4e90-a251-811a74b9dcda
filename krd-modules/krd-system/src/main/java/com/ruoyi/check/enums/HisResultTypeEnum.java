package com.ruoyi.check.enums;

import lombok.Getter;

/**
 * HIS返回结果类型枚举
 * <AUTHOR>
 * @date 2025/5/21 18:45
 */
public enum HisResultTypeEnum {

    LIS_CHANG_GUI("LIS_CHANG_GUI","常规结果",""),
    LIS_TONG_YONG("LIS_TONG_YONG","通用模块结果",""),
    PAT_CELL("PAT_CELL","其他公司细胞病理",""),
    PAT_ORG("PAT_ORG","其他公司组织病理",""),
    ;
    @Getter
    private String code;
    private String name;
    private String desc;

    HisResultTypeEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }
}
