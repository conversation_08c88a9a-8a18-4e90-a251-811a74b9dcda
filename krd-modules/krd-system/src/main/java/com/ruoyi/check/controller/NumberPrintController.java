package com.ruoyi.check.controller;

import com.ruoyi.check.aop.NumberInfoLog;
import com.ruoyi.check.domain.dto.CreateNumberDTO;
import com.ruoyi.check.domain.dto.PrintReqParam;
import com.ruoyi.check.domain.vo.CreateNumberVO;
import com.ruoyi.check.enums.HandleTypeEnum;
import com.ruoyi.check.service.IFrontNumberService;
import com.ruoyi.check.utils.NumberCreateUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 编码管理Controller
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/number/print")
public class NumberPrintController extends BaseController {

    @Autowired
    private IFrontNumberService frontNumberService;

    /**
     * 前置校验
     * @param createNumberDTO
     * @return
     */
    @PostMapping(path = "/createNumberCheck")
    public R<Integer> createNumberCheck(@RequestBody CreateNumberDTO createNumberDTO){
         return frontNumberService.createNumberCheck(createNumberDTO);
    }
    /**
     * 生成编码
     */
    @PostMapping(path = "/createNumberSave")
    public R<List<CreateNumberVO>> createNumberSave(@RequestBody CreateNumberDTO createNumberDTO) {
        //保存生成的数据
        return R.ok(frontNumberService.saveNumber(createNumberDTO));
    }

    /**
     * 关闭编码生成
     * @return
     */
    @GetMapping(path = "/createNumber/close/{clientId}")
    public R createNumberClose(@PathVariable("clientId") String clientId) {
        NumberCreateUtil.close(clientId);
        return R.ok();
    }

    /**
     * 打印数据修改
     * @param printReqParam
     * @return
     */
    @NumberInfoLog(handleType = HandleTypeEnum.NUMBER, handleInfo = "打印条码")
    @PostMapping("printDataUpdate")
    public AjaxResult printDataUpdate(@RequestBody PrintReqParam printReqParam) throws InterruptedException {
        return toAjax(frontNumberService.printDataUpdate(printReqParam));
    }

    /**
     * 打印数据失败的更新回去
     * @param printReqParam
     * @return
     */
    @NumberInfoLog(handleType = HandleTypeEnum.NUMBER, handleInfo = "打印条码失败回滚")
    @PostMapping("printDataUpdateRe")
    public AjaxResult printDataUpdateRe(@RequestBody PrintReqParam printReqParam)
    {
        return toAjax(frontNumberService.printDataUpdateRe(printReqParam));
    }

}
