package com.ruoyi.check.aop;

/**
 * <AUTHOR>
 * @date 2025/6/3 22:26
 */

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.check.domain.dto.DistributeAgencyDTO;
import com.ruoyi.check.domain.dto.PrintData;
import com.ruoyi.check.domain.dto.PrintReqParam;
import com.ruoyi.check.domain.entity.FrontNumber;
import com.ruoyi.check.enums.HandleTypeEnum;
import com.ruoyi.check.service.FrontNumberInfoService;
import com.ruoyi.common.security.service.TokenService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Aspect
@Component
public class OperationLogAspect {

    @Resource
    private FrontNumberInfoService frontNumberInfoService;
    @Resource
    private TokenService tokenService;

    // 切点：所有被 @NumberInfoLog 注解修饰的方法
    @Pointcut("@annotation(com.ruoyi.check.aop.NumberInfoLog)")
    public void logOperationPointCut() {}

    // 在方法执行后获取返回值并记录日志
    @AfterReturning(pointcut = "logOperationPointCut()", returning = "result")
    public void recordOperationLog(JoinPoint joinPoint, Object result) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取注解信息
        NumberInfoLog annotation = method.getAnnotation(NumberInfoLog.class);
        HandleTypeEnum handleType = annotation.handleType();
        String handleInfo = annotation.handleInfo();

        // 获取方法参数中的 itemCode
        Object[] args = joinPoint.getArgs();

        List<String> itemCodeList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("handleInfo", handleInfo);

        extractItemCode(itemCodeList,args,map);

        if (!itemCodeList.isEmpty()) {
            // 保存操作记录到数据库或日志文件中
            frontNumberInfoService.saveOperationLog(itemCodeList, handleType, map,tokenService.getLoginUser());
        }
    }

    // 提取 itemCode 方法
    private void extractItemCode(List<String> itemCodeList, Object[] args,Map<String, String> map) {
        String handleInfo = map.get("handleInfo");
        for (Object arg : args) {
            if (arg instanceof String) {
                itemCodeList.add((String) arg);// 处理 @PathVariable 参数
            }else if (arg instanceof FrontNumber) {// 处理 @RequestBody 参数
                FrontNumber dto = (FrontNumber) arg;
                itemCodeList.add(dto.getItemCode());
                if ("修改样本状态".equals(handleInfo)){
                    if("05".equals(dto.getSampleStatus())){
                        //不合格
                        map.put("handleInfo", "修改样本状态为不合格");
                        map.put("handleNotes", dto.getHandleRemark());
                    }
                    if("06".equals(dto.getSampleStatus())){
                        //合格
                        map.put("handleInfo", "修改样本状态为合格");
                    }
                }
            } else if (arg instanceof DistributeAgencyDTO) {// 处理 @RequestBody 参数
                DistributeAgencyDTO batchDto = (DistributeAgencyDTO) arg;
                itemCodeList.addAll(batchDto.getItemCodes());
            } else if (arg instanceof PrintReqParam) {
                PrintReqParam param = (PrintReqParam) arg;
                List<String> items = param.getPrintDataList().stream().map(PrintData::getItemCode).collect(Collectors.toList());
                itemCodeList.addAll(items);
            }
        }
    }
}

