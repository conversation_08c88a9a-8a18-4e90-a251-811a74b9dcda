package com.ruoyi.check.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.check.domain.entity.FrontReportResultEntity;
import com.ruoyi.check.domain.entity.FrontTmp;
import com.ruoyi.check.domain.vo.FrontTmpListVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 报告模版Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Mapper
public interface FrontTmpMapper extends BaseMapper<FrontTmp>
{
    /**
     * 查询报告模版
     *
     * @param id 报告模版主键
     * @return 报告模版
     */
    public FrontTmp selectFrontTmpById(String id);

    /**
     * 查询报告模版列表
     *
     * @param frontTmp 报告模版
     * @return 报告模版集合
     */
    public List<FrontTmp> selectFrontTmpList(FrontTmp frontTmp);

    /**
     * 新增报告模版
     *
     * @param frontTmp 报告模版
     * @return 结果
     */
    public int insertFrontTmp(FrontTmp frontTmp);

    /**
     * 修改报告模版
     *
     * @param frontTmp 报告模版
     * @return 结果
     */
    public int updateFrontTmp(FrontTmp frontTmp);

    /**
     * 删除报告模版
     *
     * @param id 报告模版主键
     * @return 结果
     */
    public int deleteFrontTmpById(String id);

    /**
     * 批量删除报告模版
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontTmpByIds(String[] ids);

    List<FrontTmpListVO> selectTmpList(FrontTmp frontTmp);
}