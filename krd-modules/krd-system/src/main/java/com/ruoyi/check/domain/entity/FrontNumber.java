package com.ruoyi.check.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 编码管理对象 front_number
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
public class FrontNumber extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private String id;

    /** 项目编码 */
    @Excel(name = "项目编码")
    private String itemCode;

    /** 套餐ID */
    @Excel(name = "套餐ID")
    private String packageId;

    /** 自定义编码 */
    private String number;

    /** 二维码数量 */
    @Excel(name = "二维码数量")
    private Integer qrcount;

    /** 条形码数量 */
    @Excel(name = "条形码数量")
    private Integer barcount;

    /** 下单用户id */
    private Long orderUserId;

    /** 下单家庭成员id */
    private Long orderFamilyId;

    /** 下单时间 */
    private Date orderTime;

    /** 检测人id（see：front_family） */
    private Long checkFamilyId;

    /** 检测人年龄(单位：岁) */
    private Integer checkFamilyAge;

    /** 检测人体重(单位：kg) */
    private Double checkFamilyWeight;

    /** 检测人身高(单位：cm) */
    private Double checkFamilyHeight;

    /** 检测机构id（see：front_check） */
    private Long checkId;

    /** 编码生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "编码生成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date codeCreateTime;

    /** 套码打印时间 */
    private Date printTime;

    /** 绑定时间 */
    private Date bindingTime;

    /** 回寄时间 */
    private Date shippTime;

    /** 接收时间 */
    private Date receiveTime;

    /** 样本处理时间 */
    private Date handleTime;

    /** 样本处理备注 */
    private String handleRemark;

    /** 报告出结果时间（检测机构出结果的时间） */
    private Date reportResultTime;

    /** 报告审核人id */
    private Long reportReviewFamilyId;

    /** 报告审核时间 */
    private Date reportReviewTime;

    /** 报告审核备注 */
    private String reportReviewNotes;

    /** 报告复核人id */
    private Long reportReReviewFamilyId;

    /** 报告复核时间 */
    private Date reportReReviewTime;

    /** 报告复核备注 */
    private String reportReReviewNotes;

    /** 报告状态
01-未出报告
02-未审核（已出报告）
03-已审核-不通过
04-已审核-通过
05-已复核-不通过
06-已复核-已发布 */
    private String reportStatus;

    /** 编码状态 */
    @Excel(name = "编码状态")
    private String numberStatus;

    /** 样本状态
01-未绑定
02-已绑定
03-已回寄（物流状态）
04-已接收（转管码已打印）
05-不合格
06-检测中（合格）
 */
    private String sampleStatus;

    /** 流程总状态
01-已生成
02-套码已打印
03-已绑定
04-已回寄（物流状态）
05-已接收（转管码已打印）
06-已处理（不合格，合格）
07-未审核（已出报告）
08-已审核（不通过，通过）
09-已复核（不通过，已发布）
 */
    private String processStatus;
    /**
     * 检测结果总健康建议
     */
    private String healthAdviceTotal;
    /**
     * 模版id（使用的具体模板）
     */
    private String tmpId;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("itemCode", getItemCode())
            .append("packageId", getPackageId())
            .append("number", getNumber())
            .append("qrcount", getQrcount())
            .append("barcount", getBarcount())
            .append("orderUserId", getOrderUserId())
            .append("orderFamilyId", getOrderFamilyId())
            .append("orderTime", getOrderTime())
            .append("checkFamilyId", getCheckFamilyId())
            .append("checkFamilyAge", getCheckFamilyAge())
            .append("checkFamilyWeight", getCheckFamilyWeight())
            .append("checkFamilyHeight", getCheckFamilyHeight())
            .append("checkId", getCheckId())
            .append("codeCreateTime", getCodeCreateTime())
            .append("printTime", getPrintTime())
            .append("bindingTime", getBindingTime())
            .append("shippTime", getShippTime())
            .append("receiveTime", getReceiveTime())
            .append("handleTime", getHandleTime())
            .append("handleRemark", getHandleRemark())
            .append("reportReviewFamilyId", getReportReviewFamilyId())
            .append("reportReviewTime", getReportReviewTime())
            .append("reportReviewNotes", getReportReviewNotes())
            .append("reportReReviewFamilyId", getReportReReviewFamilyId())
            .append("reportReReviewTime", getReportReReviewTime())
            .append("reportReReviewNotes", getReportReReviewNotes())
            .append("reportStatus", getReportStatus())
            .append("numberStatus", getNumberStatus())
            .append("sampleStatus", getSampleStatus())
            .append("processStatus", getProcessStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
