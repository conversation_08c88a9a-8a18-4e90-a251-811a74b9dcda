package com.ruoyi.check.domain.dto;

import com.ruoyi.check.domain.entity.FrontTmpPactarResultEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 套餐检测指标dto
 * <AUTHOR>
 * @date 2025/6/21 18:05
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PactarDTO {

    /**
     * 套餐id
     */
    private String packageId;
    /**
     * 指标id
     */
    private String targetId;
    /**
     * 指标名称
     */
    private String targetName;
    /**
     * 指标名称解释
     */
    private String targetAnalysis;
    /**
     * 指标参考范围
     */
    private String targetReferenceRange;
    /**
     * 模版指标id
     */
    private String pactarId;

    private Integer ptIsDel;

    private List<FrontTmpPactarResultEntity> pactarResultList = new ArrayList<>();
}
