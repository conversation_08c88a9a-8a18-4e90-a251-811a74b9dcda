package com.ruoyi.check.utils;

import com.ruoyi.check.domain.entity.FrontNumber;
import com.ruoyi.check.enums.ProcessStatusEnum;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.util.Assert;

/**
 * 检测流程预检测
 * <AUTHOR>
 * @date 2025/6/14 11:28
 */
public class CheckProcessPrecheckUtils {

    /**
     * 样品接收前置校验
     * @param model
     */
    public static void sampleReceivePrecheck(FrontNumber model,String itemCode){
        Assert.notNull(model,"【" + itemCode + "】编码不存在");
        if(ProcessStatusEnum.STATUS_01.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_02.getCode().equals(model.getProcessStatus())){
            throw new ServiceException("【" + itemCode + "】未绑定样品，接收失败");
        }
        //必须为已绑定或者已回寄状态才能处理
        /*if(ProcessStatusEnum.STATUS_03.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_04.getCode().equals(model.getProcessStatus())){
            log.info("【" + itemCode + "】接收成功");
        }*/
        if(ProcessStatusEnum.STATUS_05.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_06.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_07.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_08.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_09.getCode().equals(model.getProcessStatus())){
            throw new ServiceException("【" + itemCode + "】已接收，请勿重复操作");
        }
    }

    /**
     * 样品处理前置校验
     * @param model
     */
    public static void sampleHandlePrecheck(FrontNumber model,String itemCode) {
        Assert.notNull(model,"【" + itemCode + "】编码不存在");
        if(ProcessStatusEnum.STATUS_01.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_02.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_03.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_04.getCode().equals(model.getProcessStatus())
        ){
            throw new ServiceException("【" + itemCode + "】未接收样品，请先接收后再处理");
        }
        //必须为已接收状态才能处理
        /*if(ProcessStatusEnum.STATUS_05.getCode().equals(model.getProcessStatus())){
            log.info("【" + itemCode + "】处理成功");
        }*/
        if(ProcessStatusEnum.STATUS_06.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_07.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_08.getCode().equals(model.getProcessStatus())
                ||  ProcessStatusEnum.STATUS_09.getCode().equals(model.getProcessStatus())){
            throw new ServiceException("【" + itemCode + "】已处理，请勿重复操作");
        }
    }
}
