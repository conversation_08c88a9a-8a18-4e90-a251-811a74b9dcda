package com.ruoyi.check.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.ruoyi.check.domain.dto.CreateNumberDTO;
import com.ruoyi.check.domain.dto.DistributeAgencyDTO;
import com.ruoyi.check.domain.dto.NumberQueryDTO;
import com.ruoyi.check.domain.dto.PrintReqParam;
import com.ruoyi.check.domain.dto.ReportExamineBatchDTO;
import com.ruoyi.check.domain.dto.ReportExamineDTO;
import com.ruoyi.check.domain.entity.FrontNumber;
import com.ruoyi.check.domain.entity.FrontPackage;
import com.ruoyi.check.domain.entity.FrontReportResultEntity;
import com.ruoyi.check.domain.entity.FrontTmpPactarResultEntity;
import com.ruoyi.check.domain.entity.TTestPatientsHisReportEntity;
import com.ruoyi.check.domain.entity.TTestResultsHisReportEntity;
import com.ruoyi.check.domain.vo.CreateNumberVO;
import com.ruoyi.check.domain.vo.ReportBaseInfoVO;
import com.ruoyi.check.domain.vo.ReportPactarVO;
import com.ruoyi.check.domain.vo.ReportPreviewVO;
import com.ruoyi.check.domain.vo.ReportResultVO;
import com.ruoyi.check.enums.HandleTypeEnum;
import com.ruoyi.check.enums.NumberStatusEnum;
import com.ruoyi.check.enums.ProcessStatusEnum;
import com.ruoyi.check.enums.ReportStatusEnum;
import com.ruoyi.check.enums.SampleStatusEnum;
import com.ruoyi.check.mapper.FrontNumberMapper;
import com.ruoyi.check.mapper.FrontPackageMapper;
import com.ruoyi.check.mapper.FrontReportResultMapper;
import com.ruoyi.check.mapper.FrontTmpPactarMapper;
import com.ruoyi.check.mapper.FrontTmpPactarResultMapper;
import com.ruoyi.check.service.FrontNumberInfoService;
import com.ruoyi.check.service.FrontReportResultService;
import com.ruoyi.check.service.IFrontNumberService;
import com.ruoyi.check.utils.CheckProcessPrecheckUtils;
import com.ruoyi.check.utils.NumberCreateUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 编码管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@Slf4j
@Service
public class FrontNumberServiceImpl implements IFrontNumberService 
{
    @Autowired
    private FrontNumberMapper frontNumberMapper;
    @Autowired
    private FrontPackageMapper  frontPackageMapper;
    @Autowired
    private FrontReportResultMapper frontReportResultMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private FrontNumberInfoService frontNumberInfoService;
    @Resource
    private FrontReportResultService frontReportResultService;
    @Resource
    private FrontTmpPactarMapper frontTmpPactarMapper;
    @Resource
    private FrontTmpPactarResultMapper frontTmpPactarResultMapper;

    /**
     * 查询编码管理
     * 
     * @param id 编码管理主键
     * @return 编码管理
     */
    @Override
    public FrontNumber selectFrontNumberById(Long id)
    {
        return frontNumberMapper.selectFrontNumberById(id);
    }

    /**
     * 查询编码管理列表
     * 
     * @param frontNumber 编码管理
     * @return 编码管理
     */
    @Override
    public List<FrontNumber> selectFrontNumberList(FrontNumber frontNumber)
    {
        return frontNumberMapper.selectFrontNumberList(frontNumber);
    }

    /**
     * 新增编码管理
     * 
     * @param frontNumber 编码管理
     * @return 结果
     */
    @Override
    public int insertFrontNumber(FrontNumber frontNumber)
    {
        frontNumber.setId(IdWorker.getIdStr());
        frontNumber.setCreateTime(DateUtils.getNowDate());
        return frontNumberMapper.insertFrontNumber(frontNumber);
    }

    /**
     * 修改编码管理
     * 
     * @param frontNumber 编码管理
     * @return 结果
     */
    @Override
    public int updateFrontNumber(FrontNumber frontNumber)
    {
        frontNumber.setUpdateTime(DateUtils.getNowDate());
        return frontNumberMapper.updateFrontNumber(frontNumber);
    }

    /**
     * 批量删除编码管理
     * 
     * @param itemCodes 需要删除的编码管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontNumberByIds(String[] itemCodes)
    {
        return frontNumberMapper.deleteFrontNumberByIds(itemCodes);
    }

    /**
     * 删除编码管理信息
     * 
     * @param id 编码管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontNumberById(Long id)
    {
        return frontNumberMapper.deleteFrontNumberById(id);
    }

    @Override
    public List<CreateNumberVO> createNumberList(NumberQueryDTO queryDTO) {
        List<CreateNumberVO> list = frontNumberMapper.createNumberList(queryDTO);
        return list;
    }

    @Transactional
    @Override
    public List<CreateNumberVO> saveNumber(CreateNumberDTO createNumberDTO) {
        List<String> itemCodeAll = createNumberDTO.getItemCodes();
        if(CollectionUtils.isEmpty(itemCodeAll)){
            return new ArrayList<>(0);
        }
        List<String> itemCodes = new ArrayList<>(itemCodeAll.size());//统计生成成功的编码
        List<CreateNumberVO> voList = new ArrayList<>(itemCodeAll.size());
        LoginUser loginUser = tokenService.getLoginUser();

        itemCodeAll.forEach(itemCode -> {
            FrontNumber model = new FrontNumber();
            model.setId(IdWorker.getIdStr());
            model.setItemCode(itemCode);
            model.setPackageId(createNumberDTO.getPackageId());
            model.setNumber(createNumberDTO.getNumber());
            model.setQrcount(createNumberDTO.getQrcount());
            model.setBarcount(createNumberDTO.getBarcount());
            model.setCodeCreateTime(DateUtils.getNowDate());

            model.setReportStatus(ReportStatusEnum.STATUS_01.getCode());
            model.setNumberStatus(NumberStatusEnum.STATUS_01.getCode());
            model.setSampleStatus(SampleStatusEnum.STATUS_01.getCode());
            model.setProcessStatus(ProcessStatusEnum.STATUS_01.getCode());
            model.setCreateTime(DateUtils.getNowDate());
            model.setCreateBy(loginUser.getUsername());

            frontNumberMapper.insertFrontNumber(model);

            CreateNumberVO vo = new CreateNumberVO();
            vo.setPakeageName(createNumberDTO.getPakeageName());
            BeanUtils.copyProperties(model, vo);
            voList.add(vo);
            itemCodes.add(itemCode);
            log.info("保存成功编码：" + model.getItemCode());
        });

        Map<String, String> map = new HashMap<>();
        map.put("handleInfo", "生成编码");
        frontNumberInfoService.saveOperationLog(itemCodes, HandleTypeEnum.NUMBER, map, loginUser);

        return voList;
    }

    @Override
    public int printDataUpdate(PrintReqParam printReqParam) {
        return frontNumberMapper.printDataUpdate(printReqParam);
    }

    @Override
    public int printDataUpdateRe(PrintReqParam printReqParam) {
        return frontNumberMapper.printDataUpdateRe(printReqParam);
    }

    @Override
    public CreateNumberVO samplereceive(String itemCode) {
        LoginUser user = tokenService.getLoginUser();

        FrontNumber model = frontNumberMapper.selectOne(new LambdaQueryWrapper<FrontNumber>()
                .eq(FrontNumber::getItemCode, itemCode));
        CheckProcessPrecheckUtils.sampleReceivePrecheck(model,itemCode);

        model.setProcessStatus(ProcessStatusEnum.STATUS_05.getCode());
        model.setNumberStatus(NumberStatusEnum.STATUS_03.getCode());
        model.setSampleStatus(SampleStatusEnum.STATUS_04.getCode());
        model.setReceiveTime(new Date());
        model.setUpdateBy(user.getUsername());
        model.setUpdateTime(new Date());

        frontNumberMapper.update(model,new LambdaQueryWrapper<FrontNumber>()
                .eq(FrontNumber::getItemCode,itemCode));

        CreateNumberVO vo = new CreateNumberVO();
        BeanUtils.copyProperties(model,vo);

        FrontPackage pack = frontPackageMapper.selectFrontPackageById(model.getPackageId());
        if(Objects.nonNull(pack)){
            vo.setPakeageName(pack.getPakeageName());
        }
        return vo;
    }

    @Override
    public FrontNumber queryModelByItemCode(String itemCode) {
        return frontNumberMapper.selectOne(new LambdaQueryWrapper<FrontNumber>()
                .eq(FrontNumber::getItemCode,itemCode));
    }

    @Override
    public void samplereceiveRe(String itemCode) {

        FrontNumber model = frontNumberMapper.selectOne(new LambdaQueryWrapper<FrontNumber>()
                .eq(FrontNumber::getItemCode, itemCode));
        Assert.notNull(model,"编码不存在");

        model.setProcessStatus(ProcessStatusEnum.STATUS_04.getCode());
        model.setNumberStatus(NumberStatusEnum.STATUS_02.getCode());
        model.setSampleStatus(SampleStatusEnum.STATUS_03.getCode());
        model.setReceiveTime(null);
        model.setUpdateTime(new Date());

        frontNumberMapper.update(model,new LambdaQueryWrapper<FrontNumber>()
                .eq(FrontNumber::getItemCode,itemCode)
                .eq(FrontNumber::getProcessStatus,ProcessStatusEnum.STATUS_05.getCode())
                .eq(FrontNumber::getNumberStatus,NumberStatusEnum.STATUS_03.getCode())
                .eq(FrontNumber::getSampleStatus,SampleStatusEnum.STATUS_04.getCode())
        );
    }

    @Override
    public CreateNumberVO samplehandle(String itemCode) {
        LoginUser user = tokenService.getLoginUser();

        FrontNumber model = frontNumberMapper.selectOne(new LambdaQueryWrapper<FrontNumber>()
                .eq(FrontNumber::getItemCode, itemCode));

        CheckProcessPrecheckUtils.sampleHandlePrecheck(model,itemCode);

        model.setProcessStatus(ProcessStatusEnum.STATUS_06.getCode());
        model.setSampleStatus(SampleStatusEnum.STATUS_06.getCode());
        model.setHandleTime(new Date());
        model.setUpdateBy(user.getUsername());
        model.setUpdateTime(new Date());

        frontNumberMapper.update(model,new LambdaQueryWrapper<FrontNumber>()
                .eq(FrontNumber::getItemCode,itemCode)
                .eq(FrontNumber::getProcessStatus,ProcessStatusEnum.STATUS_05.getCode())
        );

        CreateNumberVO vo = new CreateNumberVO();
        BeanUtils.copyProperties(model,vo);

        FrontPackage pack = frontPackageMapper.selectFrontPackageById(model.getPackageId());
        if(Objects.nonNull(pack)){
            vo.setPakeageName(pack.getPakeageName());
        }
        log.info("【" + itemCode + "】处理成功");
        return vo;
    }

    @Override
    public void sampleUpdate(FrontNumber frontNumber) {
        LoginUser user = tokenService.getLoginUser();

        FrontNumber model = frontNumberMapper.selectOne(new LambdaQueryWrapper<FrontNumber>()
                .eq(FrontNumber::getItemCode, frontNumber.getItemCode()));
        Assert.notNull(model,"编码不存在");

        model.setSampleStatus(frontNumber.getSampleStatus());
        model.setHandleRemark(frontNumber.getHandleRemark());
        model.setHandleTime(new Date());

        model.setUpdateBy(user.getUsername());
        model.setUpdateTime(new Date());

        frontNumberMapper.update(model,new LambdaQueryWrapper<FrontNumber>()
                .eq(FrontNumber::getItemCode,frontNumber.getItemCode()));
    }

    @Override
    public void distributeAgency(DistributeAgencyDTO distributeAgency) {
        if(CollectionUtils.isEmpty(distributeAgency.getItemCodes())){
            return;
        }
        if(distributeAgency.getCheckId()==null){
            return;
        }
        frontNumberMapper.update(null,new LambdaUpdateWrapper<FrontNumber>()
                .set(FrontNumber::getCheckId,distributeAgency.getCheckId())
                .in(FrontNumber::getItemCode,distributeAgency.getItemCodes()));
    }

    @Override
    public ReportExamineDTO queryReportDetailsExamine(String itemCode) {

        CreateNumberVO model = frontNumberMapper.selectReport(itemCode);
        Assert.notNull(model,"编码不存在");

        List<FrontReportResultEntity> reportList = frontReportResultMapper.selectList(new LambdaQueryWrapper<FrontReportResultEntity>()
                .eq(FrontReportResultEntity::getItemCode, itemCode));
        return ReportExamineDTO.builder()
                .itemCode(itemCode)
                .numberDetails(model)
                .reportList(reportList)
                .build();
    }

    @Transactional
    @Override
    public void reportExamine(ReportExamineDTO model) {
        LoginUser user = tokenService.getLoginUser();
        List<FrontReportResultEntity> list = model.getReportList();
        if(!CollectionUtils.isEmpty(list)){
             list.forEach(item->{
                 frontReportResultMapper.updateById(item);
            });
        }
        CreateNumberVO number = model.getNumberDetails();
        frontNumberMapper.update(null,new LambdaUpdateWrapper<FrontNumber>()
                .set(FrontNumber::getProcessStatus,ProcessStatusEnum.STATUS_08.getCode())
                .set(FrontNumber::getReportStatus,number.getReportStatus())
                .set(FrontNumber::getReportReviewFamilyId,user.getUserid())
                .set(FrontNumber::getReportReviewTime,new Date())
                .set(FrontNumber::getReportReviewNotes,number.getReportReviewNotes())
                .set(FrontNumber::getHealthAdviceTotal,number.getHealthAdviceTotal())
                .eq(FrontNumber::getItemCode,model.getItemCode()));
    }

    @Override
    public void reportExamineRe(ReportExamineDTO model) {
        LoginUser user = tokenService.getLoginUser();
        CreateNumberVO number = model.getNumberDetails();
        frontNumberMapper.update(null,new LambdaUpdateWrapper<FrontNumber>()
                .set(FrontNumber::getProcessStatus,ProcessStatusEnum.STATUS_09.getCode())
                .set(FrontNumber::getReportStatus,number.getReportStatus())
                .set(FrontNumber::getReportReReviewFamilyId,user.getUserid())
                .set(FrontNumber::getReportReReviewTime,new Date())
                .set(FrontNumber::getReportReReviewNotes,number.getReportReReviewNotes())
                .set(FrontNumber::getHealthAdviceTotal,number.getHealthAdviceTotal())
                .eq(FrontNumber::getItemCode,model.getItemCode()));
    }

    @Override
    public void reportExamineBatch(ReportExamineBatchDTO model) {
        LoginUser user = tokenService.getLoginUser();
        CreateNumberVO number = model.getNumberDetails();
        List<String> itemList = model.getNumberList().stream()
                .map(CreateNumberVO::getItemCode).collect(Collectors.toList());

        frontNumberMapper.update(null,new LambdaUpdateWrapper<FrontNumber>()
                .set(FrontNumber::getProcessStatus,ProcessStatusEnum.STATUS_08.getCode())
                .set(FrontNumber::getReportStatus,number.getReportStatus())
                .set(FrontNumber::getReportReviewFamilyId,user.getUserid())
                .set(FrontNumber::getReportReviewTime,new Date())
                .set(FrontNumber::getReportReviewNotes,number.getReportReviewNotes())
                .in(FrontNumber::getItemCode,itemList));
    }

    @Override
    public void reportExamineReBatch(ReportExamineBatchDTO model) {
        LoginUser user = tokenService.getLoginUser();
        CreateNumberVO number = model.getNumberDetails();
        List<String> itemList = model.getNumberList().stream()
                .map(CreateNumberVO::getItemCode).collect(Collectors.toList());

        frontNumberMapper.update(null,new LambdaUpdateWrapper<FrontNumber>()
                .set(FrontNumber::getProcessStatus,ProcessStatusEnum.STATUS_09.getCode())
                .set(FrontNumber::getReportStatus,number.getReportStatus())
                .set(FrontNumber::getReportReReviewFamilyId,user.getUserid())
                .set(FrontNumber::getReportReReviewTime,new Date())
                .set(FrontNumber::getReportReReviewNotes,number.getReportReReviewNotes())
                .in(FrontNumber::getItemCode,itemList));
    }

    @Override
    public ReportExamineDTO queryReportDetailsUsernow(String uid) {

        CreateNumberVO model = frontNumberMapper.selectReportUserNow(uid);
        Assert.notNull(model,"该用户下暂时没有报告");

        List<FrontReportResultEntity> reportList = frontReportResultMapper.selectList(new LambdaQueryWrapper<FrontReportResultEntity>()
                .eq(FrontReportResultEntity::getItemCode, model.getItemCode()));
        return ReportExamineDTO.builder()
                .itemCode(model.getItemCode())
                .numberDetails(model)
                .reportList(reportList)
                .build();
    }

    //编码生成前置校验
    @Override
    public R<Integer> createNumberCheck(CreateNumberDTO createNumberDTO) {
        int start = frontNumberMapper.getNumberStart(createNumberDTO.getItemNumber(),createNumberDTO.getNumber());
        if(start + createNumberDTO.getCreateCount() > NumberCreateUtil.MAX_CREATE_COUNT + 1){
            String msg = "该编码已超过最大生成数量【" + NumberCreateUtil.MAX_CREATE_COUNT + "】";
            return R.fail(msg);
        }else {
            return R.ok(start);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updataCheckReport(TTestPatientsHisReportEntity patient, List<TTestResultsHisReportEntity> results) {
        List<FrontReportResultEntity> reportResultList =  results.stream().map(resultItem ->
                FrontReportResultEntity.builder()
                        .itemCode(resultItem.getHospBarCode())
                        .checkTarget(resultItem.getTestCname())
                        .checkTargetAbb(resultItem.getTestId())
                        .checkTargetResult(resultItem.getReportResult())
                        .referenceRange(resultItem.getReference())
                        .unit(resultItem.getUnit())
                        .testState(resultItem.getTestState())
                        .checkTime(resultItem.getTestDate())
                        .createTime(new Date())
                        .updateTime(new Date())
                        .createBy("scheduled")
                        .updateBy("scheduled")
                        .build()).collect(Collectors.toList());

        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(reportResultList)){
            //更新报告状态
            frontNumberMapper.update(null,new LambdaUpdateWrapper<FrontNumber>()
                    .set(FrontNumber::getProcessStatus, ProcessStatusEnum.STATUS_07.getCode())
                    .set(FrontNumber::getReportStatus, ReportStatusEnum.STATUS_02.getCode())
                    .set(FrontNumber::getReportResultTime,patient.getResultDate())
                    .eq(FrontNumber::getItemCode,patient.getRemotingBarCode()));
            //保存结果
            frontReportResultService.saveBatch(reportResultList);
        }
    }

    @Override
    public ReportPreviewVO reportPreview(String itemCode) {
        ReportBaseInfoVO baseInfo = frontNumberMapper.selectReportBaseInfo(itemCode);
        Assert.notNull(baseInfo,"报告不存在");

        List<ReportPactarVO> pactarList = frontTmpPactarMapper.selectPactarList(itemCode,baseInfo.getTmpId());

        List<FrontReportResultEntity> reportList = frontReportResultMapper.selectList(new LambdaQueryWrapper<FrontReportResultEntity>()
                .eq(FrontReportResultEntity::getItemCode, itemCode));

        List<ReportResultVO> reportResultList = new ArrayList<>(reportList.size());
        if(!CollectionUtils.isEmpty(reportList)){
            reportList.forEach(reportItem -> {
                ReportResultVO redto = new ReportResultVO();
                BeanUtils.copyProperties(reportItem,redto);

                List<FrontTmpPactarResultEntity> tmpPactarResultList = frontTmpPactarResultMapper.selectList(new LambdaQueryWrapper<FrontTmpPactarResultEntity>()
                        .eq(FrontTmpPactarResultEntity::getPactarId, reportItem.getPactarId()));
                //按照起始值升序排序
                tmpPactarResultList.sort(Comparator.comparing(FrontTmpPactarResultEntity::getResultRangeStartVal));
                redto.setTmpPactarResultList(tmpPactarResultList);

                reportResultList.add(redto);
            });
        }
        return ReportPreviewVO.builder()
                .itemCode(itemCode)
                .baseInfo(baseInfo)
                .pactarList(pactarList)
                .reportList(reportResultList)
                .build();
    }
}
