package com.ruoyi.check.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 检测患者历史报告表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-16 14:46:03
 */
@Data
public class TTestPatientsHisReportEntity implements Serializable {

	private static final long serialVersionUID = 1934502716490833922L;

	/**
	 * 患者ID
	 */
	private String ptid;
	/**
	 * 机构ID
	 */
	private String ptInstId;
	/**
	 * 主键ID
	 */
	private String id;
	/**
	 * 申请日期
	 */
	private Date applyDate;
	/**
	 * 采样日期
	 */
	private Date sampleDate;
	/**
	 * 接收日期
	 */
	private Date receivedDate;
	/**
	 * 结果日期
	 */
	private Date resultDate;
	/**
	 * 执行日期
	 */
	private String executeDate;
	/**
	 * 床位号
	 */
	private String bedNo;
	/**
	 * 打印指示器
	 */
	private Integer printIndicator;
	/**
	 * 是否急诊
	 */
	private Integer emergency;
	/**
	 * 是否允许打印
	 */
	private Integer printAllow;
	/**
	 * 是否已打印
	 */
	private Integer ifprint;
	/**
	 * 是否收费
	 */
	private Integer ifcharge;
	/**
	 * 费用
	 */
	private BigDecimal charges;
	/**
	 * 成本
	 */
	private BigDecimal costs;
	/**
	 * 性别
	 */
	private String sex;
	/**
	 * 年龄
	 */
	private String age;
	/**
	 * 标本状态
	 */
	private String specimanState;
	/**
	 * 病人类型
	 */
	private String patType;
	/**
	 * 费用类型
	 */
	private String costType;
	/**
	 * 就诊ID
	 */
	private String visitId;
	/**
	 * 区域
	 */
	private String region;
	/**
	 * 备注信息
	 */
	private String memos;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 旧值
	 */
	private String old;
	/**
	 * 诊断信息
	 */
	private String diagnose;
	/**
	 * 执行科室
	 */
	private String performedDept;
	/**
	 * 科室名称
	 */
	private String deptName;
	/**
	 * 医生姓名
	 */
	private String doctorName;
	/**
	 * 报告医生
	 */
	private String reportDoctor;
	/**
	 * 审核医生
	 */
	private String verifiedDoctor;
	/**
	 * 标本类型
	 */
	private String speciman;
	/**
	 * 检测辅助信息
	 */
	private String testAid;
	/**
	 * 周期
	 */
	private String period;
	/**
	 * 条形码
	 */
	private String barCode;
	/**
	 * 医院名称
	 */
	private String hospName;
	/**
	 * 执行人
	 */
	private String execMan;
	/**
	 * 发送人
	 */
	private String sendMan;
	/**
	 * 接收人
	 */
	private String receMan;
	/**
	 * 复检人
	 */
	private String reexamMan;
	/**
	 * 打印人
	 */
	private String printMan;
	/**
	 * 打印时间
	 */
	private Date printTime;
	/**
	 * 结果摘要
	 */
	private String resultSummary;
	/**
	 * 远程条形码
	 */
	private String remotingBarCode;
	/**
	 * 诊断详情
	 */
	private String diag;
	/**
	 * 检测方法
	 */
	private String testMethod;
	/**
	 * 细胞备注
	 */
	private String cellMemoes;
	/**
	 * 图片数量
	 */
	private Integer imageCount;
	/**
	 * 细胞编码
	 */
	private String cellCode;
	/**
	 * 发送组织信息
	 */
	private String ceSendTissue;
	/**
	 * 通用备注
	 */
	private String memo;
	/**
	 * 医保医院名称
	 */
	private String icHospName;
	/**
	 * 样本ID
	 */
	private String sampleId;
	/**
	 * 病理ID
	 */
	private String pathologicalId;
	/**
	 * 病理诊断
	 */
	private String pathologicalDiag;
	/**
	 * 常规结果=LIS_CHANG_GUI,通用模块结果=LIS_TONG_YONG,其他公司细胞病理=PAT_CELL,其他公司组织病理=PAT_ORG
	 */
	private String resultType;
	/**
	 * 检测代码
	 */
	private String testCode;
	/**
	 * 型别判读（结果都为阴性 就是显示 “所检结果都为阴性”，哪个型别有阳性就显示 “XXX型别阳性”）
	 */
	private String viewType;
	/**
	 * 低危
	 */
	private String viewLow;
	/**
	 * 高危
	 */
	private String viewHigh;
	/**
	 * 写入图片的默认地址
	 */
	private String filePathAddress;
	/**
	 * 费用检测
	 */
	private String costTest;
	/**
	 * 发送状态
	 */
	private Integer sendState;
	/**
	 * 费用检测说明
	 */
	private String costTestInstr;

}
