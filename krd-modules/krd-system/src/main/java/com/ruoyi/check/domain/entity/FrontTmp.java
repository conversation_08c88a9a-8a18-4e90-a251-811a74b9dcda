package com.ruoyi.check.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 报告模版对象 front_tmp
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@TableName("front_tmp")
public class FrontTmp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 模版名称 */
    @Excel(name = "模版名称")
    private String tmpName;

    /** 对于套餐 */
    @Excel(name = "对于套餐")
    private String packageId;

    /** 上传文档地址 */
    private String fileAddress;

    /** 0-false ,1-true */
    private Long isDel;
}