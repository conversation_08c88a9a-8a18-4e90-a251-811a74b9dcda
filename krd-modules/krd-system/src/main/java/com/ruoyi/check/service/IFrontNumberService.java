package com.ruoyi.check.service;

import com.ruoyi.check.domain.dto.CreateNumberDTO;
import com.ruoyi.check.domain.dto.DistributeAgencyDTO;
import com.ruoyi.check.domain.dto.NumberQueryDTO;
import com.ruoyi.check.domain.dto.PrintReqParam;
import com.ruoyi.check.domain.dto.ReportExamineBatchDTO;
import com.ruoyi.check.domain.dto.ReportExamineDTO;
import com.ruoyi.check.domain.entity.FrontNumber;
import com.ruoyi.check.domain.entity.TTestPatientsHisReportEntity;
import com.ruoyi.check.domain.entity.TTestResultsHisReportEntity;
import com.ruoyi.check.domain.vo.CreateNumberVO;
import com.ruoyi.check.domain.vo.ReportPreviewVO;
import com.ruoyi.common.core.domain.R;

import java.util.List;

/**
 * 编码管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IFrontNumberService
{
    /**
     * 查询编码管理
     * 
     * @param id 编码管理主键
     * @return 编码管理
     */
    public FrontNumber selectFrontNumberById(Long id);

    /**
     * 查询编码管理列表
     * 
     * @param frontNumber 编码管理
     * @return 编码管理集合
     */
    public List<FrontNumber> selectFrontNumberList(FrontNumber frontNumber);

    /**
     * 新增编码管理
     * 
     * @param frontNumber 编码管理
     * @return 结果
     */
    public int insertFrontNumber(FrontNumber frontNumber);

    /**
     * 修改编码管理
     * 
     * @param frontNumber 编码管理
     * @return 结果
     */
    public int updateFrontNumber(FrontNumber frontNumber);

    /**
     * 批量删除编码管理
     * 
     * @param itemCodes 需要删除的编码管理主键集合
     * @return 结果
     */
    public int deleteFrontNumberByIds(String[] itemCodes);

    /**
     * 删除编码管理信息
     * 
     * @param id 编码管理主键
     * @return 结果
     */
    public int deleteFrontNumberById(Long id);

    List<CreateNumberVO> saveNumber(CreateNumberDTO createNumberDTO);

    List<CreateNumberVO> createNumberList(NumberQueryDTO queryDTO);

    int printDataUpdate(PrintReqParam printReqParam);

    int printDataUpdateRe(PrintReqParam printReqParam);

    FrontNumber queryModelByItemCode(String itemCode);

    /**
     * 样本接收
     * @param itemCode
     * @return 返回接收的样本对象
     */
    CreateNumberVO samplereceive(String itemCode);

    void samplereceiveRe(String itemCode);

    CreateNumberVO samplehandle(String itemCode);

    void sampleUpdate(FrontNumber frontNumber);

    void distributeAgency(DistributeAgencyDTO distributeAgency);

    ReportExamineDTO queryReportDetailsExamine(String itemCode);

    void reportExamine(ReportExamineDTO model);

    void reportExamineRe(ReportExamineDTO model);

    void reportExamineBatch(ReportExamineBatchDTO model);

    void reportExamineReBatch(ReportExamineBatchDTO model);

    ReportExamineDTO queryReportDetailsUsernow(String uid);

    R<Integer> createNumberCheck(CreateNumberDTO createNumberDTO);

    void updataCheckReport(TTestPatientsHisReportEntity patient, List<TTestResultsHisReportEntity> results);

    ReportPreviewVO reportPreview(String itemCode);
}
