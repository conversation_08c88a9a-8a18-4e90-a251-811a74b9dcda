package com.ruoyi.check.scheduled;

import com.ruoyi.check.service.CheckDataUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 检测结果数据更新定时
 * <AUTHOR>
 * @date 2025/6/3 15:04
 */
@EnableScheduling
@Component
@Slf4j
public class CheckDataUpdateScheduled {

    @Autowired
    private CheckDataUpdateService checkDataUpdateService;

    @Scheduled(cron = "0 0 * * * ?")
    public void checkDataUpdate(){
        checkDataUpdateService.checkDataUpdate();
        log.info("检测结果数据更新定时");
    }
}
