package com.ruoyi.check.service;

import java.util.List;
import com.ruoyi.check.domain.entity.FrontCheck;

/**
 * 检测机构Service接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IFrontCheckService
{
    /**
     * 查询检测机构
     *
     * @param id 检测机构主键
     * @return 检测机构
     */
    public FrontCheck selectFrontCheckById(Long id);

    /**
     * 查询检测机构列表
     *
     * @param frontCheck 检测机构
     * @return 检测机构集合
     */
    public List<FrontCheck> selectFrontCheckList(FrontCheck frontCheck);

    /**
     * 新增检测机构
     *
     * @param frontCheck 检测机构
     * @return 结果
     */
    public int insertFrontCheck(FrontCheck frontCheck);

    /**
     * 修改检测机构
     *
     * @param frontCheck 检测机构
     * @return 结果
     */
    public int updateFrontCheck(FrontCheck frontCheck);

    /**
     * 批量删除检测机构
     *
     * @param ids 需要删除的检测机构主键集合
     * @return 结果
     */
    public int deleteFrontCheckByIds(Long[] ids);

    /**
     * 删除检测机构信息
     *
     * @param id 检测机构主键
     * @return 结果
     */
    public int deleteFrontCheckById(Long id);
}