package com.ruoyi.check.controller;

import com.ruoyi.check.domain.dto.PactarDTO;
import com.ruoyi.check.domain.dto.TmpPactarDTO;
import com.ruoyi.check.domain.entity.FrontTmp;
import com.ruoyi.check.domain.vo.FrontTmpListVO;
import com.ruoyi.check.service.IFrontTmpService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 报告模版Controller
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/tmp")
public class FrontTmpController extends BaseController
{
    @Autowired
    private IFrontTmpService frontTmpService;

    /**
     * 查询报告模版列表
     */
    @RequiresPermissions("check:tmp:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontTmp frontTmp)
    {
        startPage();
        List<FrontTmpListVO> list = frontTmpService.selectFrontTmpList(frontTmp);
        return getDataTable(list);
    }

    /**
     * 导出报告模版列表
     */
    @RequiresPermissions("check:tmp:export")
    @Log(title = "报告模版", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontTmp frontTmp)
    {
        List<FrontTmpListVO> list = frontTmpService.selectFrontTmpList(frontTmp);
        ExcelUtil<FrontTmpListVO> util = new ExcelUtil<>(FrontTmpListVO.class);
        util.exportExcel(response, list, "报告模版数据");
    }

    /**
     * 获取报告模版详细信息
     */
    @RequiresPermissions("check:tmp:query")
    @GetMapping(value = "getTmpInfo/{tmpId}")
    public AjaxResult getTmpInfo(@PathVariable("tmpId") String tmpId)
    {
        return success(frontTmpService.getTmpInfo(tmpId));
    }

    /**
     * 新增报告模版
     */
    @RequiresPermissions("check:tmp:add")
    @Log(title = "报告模版", businessType = BusinessType.INSERT)
    @PostMapping("addTmp")
    public AjaxResult addTmp(@RequestBody TmpPactarDTO dto)
    {
        return toAjax(frontTmpService.addTmp(dto));
    }

    /**
     * 修改报告模版
     */
    @RequiresPermissions("check:tmp:edit")
    @Log(title = "报告模版", businessType = BusinessType.UPDATE)
    @PutMapping("updateTmp")
    public AjaxResult updateTmp(@RequestBody TmpPactarDTO dto)
    {
        return toAjax(frontTmpService.updateTmp(dto));
    }

    /**
     * 删除报告模版
     */
    @RequiresPermissions("check:tmp:remove")
    @Log(title = "报告模版", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(frontTmpService.deleteFrontTmpByIds(ids));
    }
}