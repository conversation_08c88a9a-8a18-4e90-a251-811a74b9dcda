package com.ruoyi.check.domain.vo;

import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/26 11:35
 */
@Data
public class CreateNumberVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *套餐-项目编号
     */
    @Excel(name = "项目编码")
    private String itemCode;

    /**
     * 套餐id
     */
    private String packageId;
    /**
     * 套餐名称
     */
    @Excel(name = "套餐名称")
    private String pakeageName;
    /**
     * 条形码数量
     */
    @Excel(name = "条形码数量")
    private Integer barcount;
    /**
     * 二维码数量
     */
    @Excel(name = "二维码数量")
    private Integer qrcount;
    /** 编码生成时间 */
    @Excel(name = "编码生成时间")
    private Date codeCreateTime;

    /** 套码打印时间 */
    @Excel(name = "套码打印时间")
    private Date printTime;

    /** 接收时间 */
    @Excel(name = "接收时间")
    private Date receiveTime;

    /** 处理时间 */
    @Excel(name = "处理时间")
    private Date handleTime;

    /**报告出结果时间（检测机构出结果的时间）*/
    private Date reportResultTime;

    /** 编码状态 */
    private String numberStatus;

    /** 样本状态*/
    private String sampleStatus;

    /** 报告状态 */
    private String reportStatus;

    /** 处理备注 */
    private String handleRemark;

    /** 下单用户id */
    private String orderUserId;
    /** 下单用户名称 */
    private String orderUserName;
    /** 下单时间 */
    private String orderTime;

    /** 检测机构id */
    private Long checkId;

    /** 检测人姓名 */
    @Excel(name = "检测人姓名")
    private String checkFamilyName;

    /** 检测人id */
    private Long checkFamilyId;

    /** 检测人年龄 */
    @Excel(name = "检测人年龄")
    private String checkFamilyAge;

    /** 检测人性别 */
    @Excel(name = "检测人性别")
    private String checkFamilySex;

    /** 检测机构 */
    @Excel(name = "检测机构")
    private String checkName;

    /** 报告审核备注 */
    private String reportReviewNotes;

    /** 报告复核备注 */
    private String reportReReviewNotes;

    /** 处理流程状态 */
    private String processStatus;
    /** 健康建议汇总 */
    private String healthAdviceTotal;

    public String getCheckFamilySex() {
        if(Objects.nonNull(checkFamilySex)){
            if(Objects.equals("0",checkFamilySex)){
                return "女";
            }
            if(Objects.equals("1",checkFamilySex)){
                return  "男";
            }
        }
        return checkFamilySex;
    }
}
