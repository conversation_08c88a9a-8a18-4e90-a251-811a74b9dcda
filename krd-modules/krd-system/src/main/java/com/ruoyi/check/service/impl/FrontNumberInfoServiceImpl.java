package com.ruoyi.check.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.check.domain.entity.FrontNumberInfoEntity;
import com.ruoyi.check.enums.HandleTypeEnum;
import com.ruoyi.check.mapper.FrontNumberInfoMapper;
import com.ruoyi.check.service.FrontNumberInfoService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Service("frontNumberInfoService")
public class FrontNumberInfoServiceImpl extends ServiceImpl<FrontNumberInfoMapper, FrontNumberInfoEntity> implements FrontNumberInfoService {




    @Override
    public void saveOperationLog(List<String> itemCodeList, HandleTypeEnum handleType, Map<String, String> map, LoginUser loginUser) {
        List<FrontNumberInfoEntity> saveList = new ArrayList<>(itemCodeList.size());
        String handleInfo = map.get("handleInfo");
        String handleNotes = map.get("handleNotes");
        if(!CollectionUtils.isEmpty(itemCodeList)){
            itemCodeList.forEach(itemCode -> {
                saveList.add(FrontNumberInfoEntity.builder()
                        .handleType(handleType.getCode())
                        .itemCode(itemCode)
                        .handleTime(new Date())
                        .handleUserId(loginUser.getUserid())
                        .handleUserName(loginUser.getUsername())
                        .handleInfo(handleInfo)
                        .handleNotes(handleNotes)
                        .build());
            });
            this.saveBatch(saveList,1000);
        }
    }
}