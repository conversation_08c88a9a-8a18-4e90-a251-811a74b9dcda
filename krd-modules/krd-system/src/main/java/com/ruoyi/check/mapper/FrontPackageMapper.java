package com.ruoyi.check.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.check.domain.entity.FrontPackage;
import com.ruoyi.check.domain.vo.PackageListVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 套餐管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Mapper
public interface FrontPackageMapper extends BaseMapper<FrontPackage> {
    /**
     * 查询套餐管理
     *
     * @param id 套餐管理主键
     * @return 套餐管理
     */
    public FrontPackage selectFrontPackageById(String id);

    /**
     * 查询套餐管理列表
     *
     * @param frontPackage 套餐管理
     * @return 套餐管理集合
     */
    public List<PackageListVO> selectFrontPackageList(FrontPackage frontPackage);

    /**
     * 新增套餐管理
     *
     * @param frontPackage 套餐管理
     * @return 结果
     */
    public int insertFrontPackage(FrontPackage frontPackage);

    /**
     * 修改套餐管理
     *
     * @param frontPackage 套餐管理
     * @return 结果
     */
    public int updateFrontPackage(FrontPackage frontPackage);

    /**
     * 删除套餐管理
     *
     * @param id 套餐管理主键
     * @return 结果
     */
    public int deleteFrontPackageById(Long id);

    /**
     * 批量删除套餐管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontPackageByIds(Long[] ids);
}
