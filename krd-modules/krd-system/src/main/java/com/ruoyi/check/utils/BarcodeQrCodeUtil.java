package com.ruoyi.check.utils;

import com.google.zxing.*;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.OutputStream;
import java.util.Hashtable;


/**
 * 二维码+条形码工具类
 */
public class BarcodeQrCodeUtil {

    /**
     * 生成带两行文字的二维码，并写入文件
     */
    public static void generateQRCode(String content, int width, int height,
                                      String text1, String text2, String outputPath) throws Exception {
        BufferedImage image = generateQRCodeWithText(content, width, height, text1, text2);
        ImageIO.write(image, "PNG", new File(outputPath));
    }

    /**
     * 生成带两行文字的二维码，并写入 OutputStream
     */
    public static void generateQRCode(String content, int width, int height,
                                      String text1, String text2, OutputStream outputStream) throws Exception {
        BufferedImage image = generateQRCodeWithText(content, width, height, text1, text2);
        ImageIO.write(image, "PNG", outputStream);
    }

    /**
     * 生成带两行文字的条形码，并写入文件
     */
    public static void generateBarCode(String content, int width, int height,
                                       String text1, String text2, String outputPath) throws Exception {
        BufferedImage image = generateBarCodeWithText(content, width, height, text1, text2);
        ImageIO.write(image, "PNG", new File(outputPath));
    }

    /**
     * 生成带两行文字的条形码，并写入 OutputStream
     */
    public static void generateBarCode(String content, int width, int height,
                                       String text1, String text2, OutputStream outputStream) throws Exception {
        BufferedImage image = generateBarCodeWithText(content, width, height, text1, text2);
        ImageIO.write(image, "PNG", outputStream);
    }

    /**
     * 生成无文字、无额外边框的二维码（纯图像），输出到文件
     */
    public static void generateSimpleQRCode(String content, int width, int height, String outputPath)
            throws Exception {
        BitMatrix matrix = createBitMatrix(content, BarcodeFormat.QR_CODE, width, height);
        BufferedImage image = MatrixToImageWriter.toBufferedImage(matrix);
        ImageIO.write(image, "PNG", new File(outputPath));
    }

    /**
     * 生成无文字、无额外边框的二维码（纯图像），输出到 OutputStream
     */
    public static void generateSimpleQRCode(String content, int width, int height, OutputStream outputStream)
            throws Exception {
        BitMatrix matrix = createBitMatrix(content, BarcodeFormat.QR_CODE, width, height);
        BufferedImage image = MatrixToImageWriter.toBufferedImage(matrix);
        ImageIO.write(image, "PNG", outputStream);
    }

    /**
     * 生成无文字、无额外边框的条形码（纯图像），输出到文件
     */
    public static void generateSimpleBarCode(String content, int width, int height, String outputPath)
            throws Exception {
        BitMatrix matrix = createBitMatrix(content, BarcodeFormat.CODE_128, width, height);
        BufferedImage image = MatrixToImageWriter.toBufferedImage(matrix);
        ImageIO.write(image, "PNG", new File(outputPath));
    }

    /**
     * 生成无文字、无额外边框的条形码（纯图像），输出到 OutputStream
     */
    public static void generateSimpleBarCode(String content, int width, int height, OutputStream outputStream)
            throws Exception {
        BitMatrix matrix = createBitMatrix(content, BarcodeFormat.CODE_128, width, height);
        BufferedImage image = MatrixToImageWriter.toBufferedImage(matrix);
        ImageIO.write(image, "PNG", outputStream);
    }

    // =================== 私有方法 ===================

    private static BufferedImage generateQRCodeWithText(String content, int width, int height,
                                                        String text1, String text2) throws WriterException {
        BitMatrix matrix = createBitMatrix(content, BarcodeFormat.QR_CODE, width, height);
        BufferedImage image = MatrixToImageWriter.toBufferedImage(matrix);
        return addTextBelow(image, width, height, text1, text2);
    }

    private static BufferedImage generateBarCodeWithText(String content, int width, int height,
                                                         String text1, String text2) throws WriterException {
        BitMatrix matrix = createBitMatrix(content, BarcodeFormat.CODE_128, width, height);
        BufferedImage image = MatrixToImageWriter.toBufferedImage(matrix);
        return addTextBelow(image, width, height, text1, text2);
    }

    private static BitMatrix createBitMatrix(String content, BarcodeFormat format, int width, int height) throws WriterException {
        Hashtable<EncodeHintType, Object> hints = new Hashtable<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 1);
        if (format == BarcodeFormat.QR_CODE) {
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        }
        MultiFormatWriter writer = new MultiFormatWriter();
        return writer.encode(content, format, width, height, hints);
    }

    private static BufferedImage addTextBelow(BufferedImage image, int imgWidth, int imgHeight,
                                              String text1, String text2) {
        int margin = 20;
        int totalWidth = imgWidth + margin * 2;
        int totalHeight = imgHeight + margin * 3 + 30 * 2;

        BufferedImage combined = new BufferedImage(totalWidth, totalHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = combined.createGraphics();

        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, totalWidth, totalHeight);

        g.drawImage(image, margin, margin, null);

        g.setFont(new Font("微软雅黑", Font.PLAIN, 16));
        g.setColor(Color.BLACK);

        int x = totalWidth / 2;
        int y1 = imgHeight + margin * 2 + 16;
        int y2 = y1 + 24;

        drawCenteredString(g, text1, x, y1);
        drawCenteredString(g, text2, x, y2);

        g.dispose();
        return combined;
    }

    private static void drawCenteredString(Graphics2D g, String text, int x, int y) {
        FontMetrics metrics = g.getFontMetrics();
        int width = metrics.stringWidth(text);
        g.drawString(text, x - width / 2, y);
    }
}
