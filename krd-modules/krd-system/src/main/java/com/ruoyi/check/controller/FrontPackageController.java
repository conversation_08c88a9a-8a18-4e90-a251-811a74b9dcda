package com.ruoyi.check.controller;

import com.ruoyi.check.domain.dto.PackageAuDTO;
import com.ruoyi.check.domain.entity.FrontPackage;
import com.ruoyi.check.domain.vo.PackageListVO;
import com.ruoyi.check.service.IFrontPackageService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 套餐管理Controller
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/package")
public class FrontPackageController extends BaseController
{
    @Autowired
    private IFrontPackageService frontPackageService;

    /**
     * 查询套餐管理列表
     */
    @RequiresPermissions("check:package:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontPackage frontPackage)
    {
        startPage();
        List<PackageListVO> list = frontPackageService.selectFrontPackageList(frontPackage);
        return getDataTable(list);
    }

    /**
     * 导出套餐管理列表
     */
    @RequiresPermissions("check:package:export")
    @Log(title = "套餐管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontPackage frontPackage)
    {
        List<PackageListVO> list = frontPackageService.selectFrontPackageList(frontPackage);
        ExcelUtil<PackageListVO> util = new ExcelUtil<>(PackageListVO.class);
        util.exportExcel(response, list, "套餐管理数据");
    }

    /**
     * 获取套餐管理详细信息
     */
    @RequiresPermissions("check:package:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(frontPackageService.selectFrontPackageById(id));
    }

    /**
     * 新增套餐管理
     */
    @RequiresPermissions("check:package:add")
    @Log(title = "套餐管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PackageAuDTO auDTO)
    {
        return toAjax(frontPackageService.insertFrontPackage(auDTO));
    }

    /**
     * 修改套餐管理
     */
    @RequiresPermissions("check:package:edit")
    @Log(title = "套餐管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PackageAuDTO auDTO)
    {
        return toAjax(frontPackageService.updateFrontPackage(auDTO));
    }

    /**
     * 删除套餐管理
     */
    @RequiresPermissions("check:package:remove")
    @Log(title = "套餐管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontPackageService.deleteFrontPackageByIds(ids));
    }

    /**
     * 查询套餐对应的标签列表
     */
    @GetMapping(value = "/getTagListByPackageId/{packageId}")
    public AjaxResult getTagListByPackageId(@PathVariable("packageId") Long packageId)
    {
        return success(frontPackageService.getTagListByPackageId(packageId));
    }
}
