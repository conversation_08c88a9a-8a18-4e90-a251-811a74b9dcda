package com.ruoyi.check.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 预览报告dto
 * <AUTHOR>
 * @date 2025/6/23 15:48
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReportPreviewVO {
    /**
     * 项目编码
     */
    private String itemCode;
    /**
     * 基础信息
     */
    private ReportBaseInfoVO baseInfo;
    /**
     * 套餐-指标解释
     */
    private List<ReportPactarVO> pactarList;
    /**
     * 检测结果
     */
    private List<ReportResultVO> reportList;
}
