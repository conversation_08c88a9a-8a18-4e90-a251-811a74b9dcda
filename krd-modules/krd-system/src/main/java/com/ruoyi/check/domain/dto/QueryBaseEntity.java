package com.ruoyi.check.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/30 10:24
 */
@Data
public class QueryBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否分页
     */
    private Boolean isPage = true;
    /**
     * 搜索值
     */
    private String searchValue;
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    /**
     * 每页显示记录数
     */
    private Integer pageSize = 10;

    /**
     * 总记录数
     */
    private Integer total = 0;

    /**
     * 开始记录索引
     */
    private Integer pageStart = 0;
    /**
     * 排序列
     */
    private String orderByColumn;
    /**
     * 排序的方向desc或者asc
     */
    private String orderByType = "asc";

    public Integer getPageStart() {
        if (pageNum != null && pageSize != null) {
            pageStart = (pageNum - 1) * pageSize;
        }
        return pageStart;
    }

    public String getSearchValue() {
        return searchValue == null ? "" : searchValue.trim();
    }
}
