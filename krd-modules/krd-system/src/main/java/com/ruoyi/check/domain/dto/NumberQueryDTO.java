package com.ruoyi.check.domain.dto;

import lombok.Data;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/30 10:31
 */
@Data
public class NumberQueryDTO extends QueryBaseEntity{

    private Long packageId;
    private String numberStatus;
    private String sampleStatus;
    private String reportStatus;
    private String processStatus;
    private String module;//模块
    private String timeColumn;//时间筛选字段
    private String startTime;
    private String endTime;
    private Boolean isAllocation;//是否分配机构
    private String prodnumberStatus;//生产编码状态：01已生成，02套码已打印（包含专管码已打印状态）

    /**
     * 时间范围处理
     * 前端传timeRange字段时，会自动转换，不需要添加真正的field
     * @param timeRange
     */
    public void setTimeRange(Date[] timeRange) {
        if (timeRange != null && timeRange.length > 1) {
            this.startTime = DateFormatUtils.format(timeRange[0], "yyyy-MM-dd HH:mm:ss");
            this.endTime = DateFormatUtils.format(timeRange[1], "yyyy-MM-dd HH:mm:ss").replace("00:00:00", "23:59:59");
        }
    }
}
