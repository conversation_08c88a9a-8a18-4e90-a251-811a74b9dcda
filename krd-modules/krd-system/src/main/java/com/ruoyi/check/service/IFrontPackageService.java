package com.ruoyi.check.service;

import java.util.List;

import com.ruoyi.check.domain.dto.PackageAuDTO;
import com.ruoyi.check.domain.dto.PactarDTO;
import com.ruoyi.check.domain.entity.FrontPackage;
import com.ruoyi.check.domain.entity.FrontPackageTargetEntity;
import com.ruoyi.check.domain.vo.PackageListVO;

/**
 * 套餐管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IFrontPackageService 
{
    /**
     * 查询套餐管理
     * 
     * @param id 套餐管理主键
     * @return 套餐管理
     */
    public PackageListVO selectFrontPackageById(String id);

    /**
     * 查询套餐管理列表
     * 
     * @param frontPackage 套餐管理
     * @return 套餐管理集合
     */
    public List<PackageListVO> selectFrontPackageList(FrontPackage frontPackage);

    /**
     * 新增套餐管理
     * 
     * @param auDTO 套餐管理
     * @return 结果
     */
    public int insertFrontPackage(PackageAuDTO auDTO);

    /**
     * 修改套餐管理
     * 
     * @param auDTO 套餐管理
     * @return 结果
     */
    public int updateFrontPackage(PackageAuDTO auDTO);

    /**
     * 批量删除套餐管理
     * 
     * @param ids 需要删除的套餐管理主键集合
     * @return 结果
     */
    public int deleteFrontPackageByIds(Long[] ids);

    /**
     * 删除套餐管理信息
     * 
     * @param id 套餐管理主键
     * @return 结果
     */
    public int deleteFrontPackageById(Long id);

    List<PactarDTO> getTagListByPackageId(Long packageId);
}
