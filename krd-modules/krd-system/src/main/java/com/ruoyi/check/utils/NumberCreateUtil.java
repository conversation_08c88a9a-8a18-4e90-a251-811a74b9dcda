package com.ruoyi.check.utils;

import com.ruoyi.check.domain.entity.FrontNumber;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 生成编码工具
 * <AUTHOR>
 * @date 2025/5/23 16:40
 */
@Slf4j
public class NumberCreateUtil {

    // 存储生成的编码，使用ConcurrentHashMap保证线程安全
    private static final Map<String, LinkedBlockingQueue<FrontNumber>> numbers = new ConcurrentHashMap<>();

    private static final Map<String, Boolean> open = new ConcurrentHashMap<>();//控制中断状态

    // 最大生成数量
    public static final int MAX_CREATE_COUNT = 990000;
    // 队列容量
    private static final int QUEUE_CAPACITY = 1000;
    //  队列超时时间(秒)
    private static final int QUEUE_TIMEOUT = 60;

    private NumberCreateUtil() {}

    /**
     * 为指定客户端生成一组编码并放入队列中（线程安全）
     * 如果已存在对应clientId的队列，则不会重复生成
     *
     * @param clientId 客户标识
     * @param item 项目编码（3位）
     * @param number 自定义编码（4）
     * @param createCount 要生成的数量
     */
    public static void createNumber(String clientId, String item, String number, int createCount,int start) {
        open(clientId);
        generateNumbers(clientId, item, number, start, createCount);
    }


    /**
     * 实际生成编码的方法
     * @param item  项目编码
     * @param number    自定义编码
     * @param start 起始索引
     * @param createCount   生成的数量
     */
    private static void generateNumbers(String clientId, String item, String number,
                                       int start, int createCount) {
        log.info("开始生成编码：{}---{}", start,createCount);
        int end = start + Math.min(createCount, MAX_CREATE_COUNT) - 1;
        LinkedBlockingQueue<FrontNumber> queue = getNumberQueue(clientId);
        for (int count = start; count <= end; count++) {
            if(getOpenStatus(clientId)){
                FrontNumber frontNumber = new FrontNumber();
                String formattedCount = String.format("%06d", count); // 格式化为6位数
                frontNumber.setItemCode(item + number + formattedCount);

                try {
                    boolean result = queue.offer(frontNumber, QUEUE_TIMEOUT, TimeUnit.SECONDS);
                    if (result) {
                        System.out.println("生成成功编码：" + frontNumber.getItemCode());
                    } else {
                        System.err.println("队列已满，无法插入编码：" + frontNumber.getItemCode());
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // 恢复中断状态
                    log.error("编码生成过程中发生中断" + e.getMessage());
                }
            }else {
                log.info("已关闭生成编码：{}---{}", start,end);
                return;
            }
        }
    }

    /**
     * 获取指定客户端的编码队列
     */
    public static LinkedBlockingQueue<FrontNumber> getNumberQueue(String clientId) {
        return numbers.get(clientId);
    }

    /**
     * 清理指定客户端的编码队列
     */
    public static void clearNumberQueue(String clientId) {
        LinkedBlockingQueue<FrontNumber> queue = numbers.remove(clientId);
        if (queue != null) {
            queue.clear();
        }
    }

    /**
     * 停止生成编码并且清空队列
     */
    public static void close(String clientId){
        open.put(clientId,false);//关闭生成编码
        clearNumberQueue(clientId);//清空未处理的队列
    }

    /**
     * 开启生成编码并且创建新的队列
     * @param clientId
     */
    public static void open(String clientId){
        open.put(clientId,true);
        numbers.put(clientId, new LinkedBlockingQueue<>(QUEUE_CAPACITY));
    }

    /**
     * 获取指定客户端的生成编码状态
     * @param clientId
     * @return
     */
    public static boolean getOpenStatus(String clientId) {
        return open.getOrDefault(clientId, false);
    }
}
