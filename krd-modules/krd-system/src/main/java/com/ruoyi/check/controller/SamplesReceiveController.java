package com.ruoyi.check.controller;

import com.ruoyi.check.aop.NumberInfoLog;
import com.ruoyi.check.domain.dto.NumberQueryDTO;
import com.ruoyi.check.domain.vo.CreateNumberVO;
import com.ruoyi.check.enums.HandleTypeEnum;
import com.ruoyi.check.enums.NumberStatusEnum;
import com.ruoyi.check.enums.ProcessStatusEnum;
import com.ruoyi.check.enums.SampleStatusEnum;
import com.ruoyi.check.service.IFrontNumberService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 样本接收
 * <AUTHOR>
 * @date 2025/5/29 16:26
 */
@RestController
@RequestMapping("check/samplereceive")
public class SamplesReceiveController  extends BaseController {

    @Autowired
    private IFrontNumberService frontNumberService;

    @PostMapping("/list")
    public TableDataInfo queryNumberList(@RequestBody NumberQueryDTO queryDTO)
    {
        startPage();
        queryDTO.setProcessStatus(ProcessStatusEnum.STATUS_05.getCode());
        queryDTO.setNumberStatus(NumberStatusEnum.STATUS_03.getCode());
        queryDTO.setSampleStatus(SampleStatusEnum.STATUS_04.getCode());
        queryDTO.setTimeColumn("num.receive_time");
        queryDTO.setOrderByColumn("receiveTime");
        queryDTO.setOrderByType("desc");
        List<CreateNumberVO> list = frontNumberService.createNumberList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 接收样本
     * @param itemCode
     * @return
     */
    @NumberInfoLog(handleType = HandleTypeEnum.SAMPLE, handleInfo = "接收样本")
    @GetMapping("/receive/{itemCode}")
    public R receive(@PathVariable("itemCode") String itemCode) {
        return R.ok(frontNumberService.samplereceive(itemCode));
    }

    /**
     * 接收样本回滚
     * @param itemCode
     * @return
     */
    @NumberInfoLog(handleType = HandleTypeEnum.SAMPLE, handleInfo = "接收样本失败回滚")
    @GetMapping("/receiveRe/{itemCode}")
    public R receiveRe(@PathVariable("itemCode") String itemCode) {
        frontNumberService.samplereceiveRe(itemCode);
        return R.ok();
    }
}
