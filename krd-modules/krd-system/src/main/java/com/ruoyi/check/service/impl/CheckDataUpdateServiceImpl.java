package com.ruoyi.check.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.ruoyi.check.domain.entity.TTestPatientsHisReportEntity;
import com.ruoyi.check.domain.entity.TTestResultsHisReportEntity;
import com.ruoyi.check.enums.HisResultTypeEnum;
import com.ruoyi.check.mapper.FrontNumberMapper;
import com.ruoyi.check.service.CheckDataUpdateService;
import com.ruoyi.check.service.HisReportService;
import com.ruoyi.check.service.IFrontNumberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/3 15:06
 */
@Slf4j
@Service
public class CheckDataUpdateServiceImpl implements CheckDataUpdateService {

    @Resource
    private FrontNumberMapper frontNumberMapper;
    @Resource
    private HisReportService hisReportService;
    @Resource
    private IFrontNumberService frontNumberService;

    @Override
    public void checkDataUpdate() {

        List<String> itemCodeList = frontNumberMapper.selectItemCodeList();
        if(CollectionUtils.isNotEmpty(itemCodeList)){
            Lists.partition(itemCodeList, 100).forEach(itemCode->{
                List<TTestPatientsHisReportEntity> patients = hisReportService.getPatientsListByItemCode(itemCode);
                if(CollectionUtils.isNotEmpty(patients)){
                    Map<String, List<TTestPatientsHisReportEntity>> groupMap = patients.stream()
                            .collect(Collectors.groupingBy(entity -> entity.getResultType().toUpperCase(Locale.ROOT)));

                    //处理常规检测
                    List<TTestPatientsHisReportEntity> resultList = groupMap.get(HisResultTypeEnum.LIS_CHANG_GUI.getCode());
                    if(CollectionUtils.isNotEmpty(resultList)){//是常规检测，需要更新结果表

                        List<String> itemCodeUpdateList = resultList.stream().map(TTestPatientsHisReportEntity::getRemotingBarCode).collect(Collectors.toList());
                        List<TTestResultsHisReportEntity> resultItemList = hisReportService.getResultsListByItemCode(itemCodeUpdateList);

                        if (CollectionUtils.isNotEmpty(resultItemList)){
                            //循环处理报告
                            resultList.forEach(patient -> {
                                //获取当前报告的结果
                                List<TTestResultsHisReportEntity> results =  resultItemList.stream()
                                        .filter(item -> item.getHospBarCode().equals(patient.getRemotingBarCode()))
                                        .collect(Collectors.toList());
                                //每个报告都是新的事务，不影响其他报告
                                frontNumberService.updataCheckReport(patient, results);
                            });
                            log.info("【{}】获取检测报告结果成功",itemCodeUpdateList);
                        }
                    }
                    /// //////////////////// 保留逻辑
//                    List<TTestPatientsHisReportEntity> reportList = groupMap.get(HisResultTypeEnum.LIS_TONG_YONG.getCode());
//                    if(CollectionUtils.isNotEmpty(reportList)){//读取诊断列（DIAG）和补充意见列（CELL_MEMOES）
//
//                    }
                }
            });
        }
    }

}
