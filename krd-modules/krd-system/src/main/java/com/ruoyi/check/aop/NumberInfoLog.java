package com.ruoyi.check.aop;

/**
 * 样本接收流程过程：操作日志记录注解
 * <AUTHOR>
 * @date 2025/6/3 22:26
 */
import com.ruoyi.check.enums.HandleTypeEnum;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface NumberInfoLog {
    /**
     * 操作流程类型：number编码，sample样本，report报告
     * @return
     */
    HandleTypeEnum handleType();

    /**
     * 操作详情
     * @return
     */
    String handleInfo();
}

