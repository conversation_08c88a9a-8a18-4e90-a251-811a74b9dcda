package com.ruoyi.check.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.check.domain.dto.PactarDTO;
import com.ruoyi.check.domain.entity.FrontPackageTargetEntity;
import com.ruoyi.common.core.mybatisext.MyBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 套餐指标表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 18:23:56
 */
@Mapper
public interface FrontPackageTargetMapper extends MyBaseMapper<FrontPackageTargetEntity> {

    List<PactarDTO> selectPactarList(@Param("packageId") Long packageId);
}
