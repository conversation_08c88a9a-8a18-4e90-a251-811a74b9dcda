package com.ruoyi.check.controller;

import com.ruoyi.check.domain.entity.FrontCheck;
import com.ruoyi.check.service.IFrontCheckService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 检测机构Controller
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/check")
public class FrontCheckController extends BaseController
{
    @Autowired
    private IFrontCheckService frontCheckService;

    /**
     * 查询检测机构列表
     */
    @RequiresPermissions("check:check:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontCheck frontCheck)
    {
        startPage();
        List<FrontCheck> list = frontCheckService.selectFrontCheckList(frontCheck);
        return getDataTable(list);
    }

    /**
     * 导出检测机构列表
     */
    @RequiresPermissions("check:check:export")
    @Log(title = "检测机构", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontCheck frontCheck)
    {
        List<FrontCheck> list = frontCheckService.selectFrontCheckList(frontCheck);
        ExcelUtil<FrontCheck> util = new ExcelUtil<FrontCheck>(FrontCheck.class);
        util.exportExcel(response, list, "检测机构数据");
    }

    /**
     * 获取检测机构详细信息
     */
    @RequiresPermissions("check:check:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontCheckService.selectFrontCheckById(id));
    }

    /**
     * 新增检测机构
     */
    @RequiresPermissions("check:check:add")
    @Log(title = "检测机构", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontCheck frontCheck)
    {
        return toAjax(frontCheckService.insertFrontCheck(frontCheck));
    }

    /**
     * 修改检测机构
     */
    @RequiresPermissions("check:check:edit")
    @Log(title = "检测机构", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontCheck frontCheck)
    {
        return toAjax(frontCheckService.updateFrontCheck(frontCheck));
    }

    /**
     * 删除检测机构
     */
    @RequiresPermissions("check:check:remove")
    @Log(title = "检测机构", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontCheckService.deleteFrontCheckByIds(ids));
    }
}