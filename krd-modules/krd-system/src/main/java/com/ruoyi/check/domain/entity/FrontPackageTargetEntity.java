package com.ruoyi.check.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 套餐指标表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 18:23:56
 */
@Data
@TableName("front_package_target")
public class FrontPackageTargetEntity implements Serializable {

	private static final long serialVersionUID = 1934919936979136513L;

	/**
	 * 
	 */
	@TableId(type = IdType.ASSIGN_ID)
	private String id;
	/**
	 * 指标代码
	 */
	private String targetCode;
	/**
	 * 指标名称
	 */
	private String targetName;
	/**
	 * 套餐id
	 */
	private String packageId;
	/**
	 * 创建人
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateBy;
	/**
	 * 更新时间
	 */
	private Date updateTime;
	/**
	 * 是否删除0-false,1-true
	 */
	private Integer isDel;

}
