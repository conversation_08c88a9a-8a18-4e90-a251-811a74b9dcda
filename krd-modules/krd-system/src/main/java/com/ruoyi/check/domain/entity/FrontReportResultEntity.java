package com.ruoyi.check.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 检测报告结果表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-23 15:43:05
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("front_report_result")
public class FrontReportResultEntity implements Serializable {

	private static final long serialVersionUID = 1937053786081509378L;

	/**
	 * 
	 */
	@TableId(type = IdType.ASSIGN_ID)
	private String id;
	/**
	 * 项目编码

	 */
	private String itemCode;
	/**
	 * 模版指标id
	 */
	private String pactarId;
	/**
	 * 检测指标
	 */
	private String checkTarget;
	/**
	 * 检测指标简称
	 */
	private String checkTargetAbb;
	/**
	 * 指标参考范围
	 */
	private String referenceRange;
	/**
	 * 结果
	 */
	private String checkTargetResult;
	/**
	 * 单位
	 */
	private String unit;
	/**
	 * 检测日期
	 */
	private Date checkTime;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;
	/**
	 * 最终结果id：front_tmp_pactar_result.id
	 */
	private String ptrId;
	/**
	 * 结果状态：front_tmp_pactar_result.result_name
	 */
	private String testState;
	/**
	 * 结果颜色
	 */
	private String resultColor;
	/**
	 * 结果解读
	 */
	private String resultInterpret;
	/**
	 * 健康建议
	 */
	private String healthAdvice;

}
