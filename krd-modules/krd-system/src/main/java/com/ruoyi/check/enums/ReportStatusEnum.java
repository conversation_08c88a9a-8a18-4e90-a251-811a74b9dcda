package com.ruoyi.check.enums;

import lombok.Getter;

/**
 * 报告状态
 * <AUTHOR>
 * @date 2025/5/21 18:45
 */
public enum ReportStatusEnum {

    STATUS_01("01","未出报告","未出报告"),
    STATUS_02("02","未审核（已出报告）","未审核（已出报告）"),
    STATUS_03("03","已审核-不通过","已审核-不通过"),
    STATUS_04("04","已审核-通过","已审核-通过"),
    STATUS_05("05","已复核-不通过","已复核-不通过"),
    STATUS_06("06","已复核-已发布","已复核-已发布"),
    ;
    @Getter
    private String code;
    private String name;
    private String desc;

    ReportStatusEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

}
