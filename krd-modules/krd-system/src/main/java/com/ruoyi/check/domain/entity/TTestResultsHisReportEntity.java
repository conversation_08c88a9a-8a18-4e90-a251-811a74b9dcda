package com.ruoyi.check.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 检测结果历史报告表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-16 14:46:03
 */
@Data
public class TTestResultsHisReportEntity implements Serializable {

	private static final long serialVersionUID = 1934502715035410433L;

	/**
	 * 患者ID
	 */
	private String ptid;
	/**
	 * 检测ID
	 */
	private String testId;
	/**
	 * 检测中文名称
	 */
	private String testCname;
	/**
	 * 检测结果
	 */
	private String testResult;
	/**
	 * 检测备注
	 */
	private String testMemo;
	/**
	 * 机构ID
	 */
	private String instId;
	/**
	 * 检测日期
	 */
	private Date testDate;
	/**
	 * 打印编号
	 */
	private String printNumber;
	/**
	 * 报告结果
	 */
	private String reportResult;
	/**
	 * 参考值
	 */
	private String reference;
	/**
	 * 检测状态
	 */
	private String testState;
	/**
	 * 单位
	 */
	private String unit;
	/**
	 * 主键ID
	 */
	private String id;
	/**
	 * 检测英文名称
	 */
	private String testEname;
	/**
	 * 医院检测ID
	 */
	private String hospTestId;
	/**
	 * 医院条形码
	 */
	private String hospBarCode;
	/**
	 * 报告医生
	 */
	private String reportDoctor;
	/**
	 * 审核医生
	 */
	private String verifiedDoctor;
	/**
	 * 就诊ID
	 */
	private String visitId;
	/**
	 * 患者姓名
	 */
	private String patName;
	/**
	 * 结果图片
	 */
	private String resultImage;
	/**
	 * 发送状态
	 */
	private Integer sendState;
	/**
	 * 检测方法备注
	 */
	private String testMethodMemo;

}
