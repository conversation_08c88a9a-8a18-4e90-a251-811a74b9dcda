package com.ruoyi.check.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.ruoyi.check.domain.dto.PackageAuDTO;
import com.ruoyi.check.domain.dto.PactarDTO;
import com.ruoyi.check.domain.entity.FrontPackageTargetEntity;
import com.ruoyi.check.domain.entity.FrontTmpPactarResultEntity;
import com.ruoyi.check.domain.vo.PackageListVO;
import com.ruoyi.check.mapper.FrontPackageTargetMapper;
import com.ruoyi.check.mapper.FrontTmpPactarResultMapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.check.mapper.FrontPackageMapper;
import com.ruoyi.check.domain.entity.FrontPackage;
import com.ruoyi.check.service.IFrontPackageService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 套餐管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
public class FrontPackageServiceImpl implements IFrontPackageService 
{
    @Autowired
    private FrontPackageMapper frontPackageMapper;
    @Autowired
    private FrontPackageTargetMapper frontPackageTargetMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private FrontTmpPactarResultMapper frontTmpPactarResultMapper;

    /**
     * 查询套餐管理
     * 
     * @param id 套餐管理主键
     * @return 套餐管理
     */
    @Override
    public PackageListVO selectFrontPackageById(String id)
    {
        PackageListVO vo = new PackageListVO();
        FrontPackage model = frontPackageMapper.selectFrontPackageById(id);
        BeanUtils.copyProperties(model,vo);

        List<FrontPackageTargetEntity> targetList = frontPackageTargetMapper.selectList(new LambdaQueryWrapper<FrontPackageTargetEntity>()
                .eq(FrontPackageTargetEntity::getPackageId, model.getId())
                .eq(FrontPackageTargetEntity::getIsDel, 0));
        vo.setPackageTargetList(targetList);
        return vo;
    }

    /**
     * 查询套餐管理列表
     * 
     * @param frontPackage 套餐管理
     * @return 套餐管理
     */
    @Override
    public List<PackageListVO> selectFrontPackageList(FrontPackage frontPackage)
    {
        List<PackageListVO> list = frontPackageMapper.selectFrontPackageList(frontPackage);
        if(!CollectionUtils.isEmpty(list)){
            list.forEach(item->{
                item.setPackageTargetList(frontPackageTargetMapper.selectList(new LambdaQueryWrapper<FrontPackageTargetEntity>()
                        .eq(FrontPackageTargetEntity::getPackageId, item.getId())
                        .eq(FrontPackageTargetEntity::getIsDel, 0)));
            });
        }
        return list;
    }

    /**
     * 新增套餐管理
     * 
     * @param auDTO 套餐管理
     * @return 结果
     */
    @Transactional
    @Override
    public int insertFrontPackage(PackageAuDTO auDTO) {
        LoginUser user = tokenService.getLoginUser();
        Date nowDate = DateUtils.getNowDate();
        FrontPackage frontPackage = new FrontPackage();

        BeanUtils.copyProperties(auDTO,frontPackage);

        frontPackage.setUserId(user.getUserid());
        frontPackage.setCreateBy(user.getUsername());
        frontPackage.setCreateTime(nowDate);
        frontPackage.setUpdateBy(user.getUsername());
        frontPackage.setUpdateTime(nowDate);
//        frontPackage.setId(IdWorker.getIdStr());
        frontPackageMapper.insert(frontPackage);
//        frontPackageMapper.insertFrontPackage(frontPackage);

        List<FrontPackageTargetEntity> targetList = auDTO.getPackageTargetList();
        if(!CollectionUtils.isEmpty(targetList)){
            targetList.stream()
                    .filter(item->item.getIsDel()==0)
                    .forEach(item->{
                        item.setPackageId(frontPackage.getId());
                        item.setCreateBy(user.getUsername());
                        item.setCreateTime(nowDate);
                        item.setUpdateBy(user.getUsername());
                        item.setUpdateTime(nowDate);

                frontPackageTargetMapper.insert(item);
            });
        }
        return 1;
    }

    /**
     * 修改套餐管理
     * 
     * @param auDTO 套餐管理
     * @return 结果
     */
    @Override
    public int updateFrontPackage(PackageAuDTO auDTO)
    {
        LoginUser user = tokenService.getLoginUser();
        FrontPackage frontPackage = new FrontPackage();

        BeanUtils.copyProperties(auDTO,frontPackage);
        frontPackage.setUpdateBy(user.getUsername());
        frontPackage.setUpdateTime(DateUtils.getNowDate());
        frontPackageMapper.updateById(frontPackage);
//        frontPackageMapper.updateFrontPackage(frontPackage);

        //先删除旧指标
        List<FrontPackageTargetEntity> targetList = auDTO.getPackageTargetList();
        if(!CollectionUtils.isEmpty(targetList)){
            targetList.forEach(item->{
                item.setPackageId(frontPackage.getId());
                item.setCreateBy(user.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                item.setUpdateBy(user.getUsername());
                item.setUpdateTime(DateUtils.getNowDate());

                if(item.getId()==null){//是新增
                    if(item.getIsDel()==0){
                        item.setId(IdWorker.getIdStr());
                        frontPackageTargetMapper.insert(item);
                    }
                }else {
                    frontPackageTargetMapper.updateById(item);
                }
            });
        }
        return 1;
    }

    /**
     * 批量删除套餐管理
     * 
     * @param ids 需要删除的套餐管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontPackageByIds(Long[] ids)
    {
        return frontPackageMapper.deleteFrontPackageByIds(ids);
    }

    /**
     * 删除套餐管理信息
     * 
     * @param id 套餐管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontPackageById(Long id)
    {
        return frontPackageMapper.deleteFrontPackageById(id);
    }

    @Override
    public List<PactarDTO> getTagListByPackageId(Long packageId) {
        List<PactarDTO> targetList = frontPackageTargetMapper.selectPactarList(packageId);
        for (PactarDTO pactarDTO : targetList) {
            if(pactarDTO.getPactarId() != null){
                pactarDTO.setPactarResultList(frontTmpPactarResultMapper.selectList(new LambdaQueryWrapper<FrontTmpPactarResultEntity>()
                        .eq(FrontTmpPactarResultEntity::getPactarId, pactarDTO.getPactarId())));
            }
        }
        return targetList;
    }
}
