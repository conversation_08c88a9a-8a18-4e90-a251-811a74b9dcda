package com.ruoyi.check.service;

import com.ruoyi.check.domain.entity.TTestPatientsHisReportEntity;
import com.ruoyi.check.domain.entity.TTestResultsHisReportEntity;

import java.util.List;

/**
 * 检测结果历史报告表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-16 14:46:03
 */
public interface HisReportService {

    List<TTestPatientsHisReportEntity> getPatientsListByItemCode(List<String> itemCodes);

    List<TTestResultsHisReportEntity> getResultsListByItemCode(List<String> itemCodes);
}

