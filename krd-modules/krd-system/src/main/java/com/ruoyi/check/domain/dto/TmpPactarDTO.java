package com.ruoyi.check.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 模板dto
 * <AUTHOR>
 * @date 2025/6/23 10:15
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TmpPactarDTO {

    /**
     * 模板id
     */
    private String tmpId;
    /**
     * 模板名称
     */
    private String tmpName;
    /**
     * 套餐id
     */
    private String packageId;
    /**
     * 套餐指标列表
     */
    private List<PactarDTO> targetList;
}
