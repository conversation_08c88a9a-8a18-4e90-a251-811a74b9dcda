package com.ruoyi.check.domain.entity;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 检测机构对象 front_check
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public class FrontCheck extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 机构名称 */
    @Excel(name = "机构名称")
    private String name;

    /** 0-false ,1-true */
    private Long isDel;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setIsDel(Long isDel)
    {
        this.isDel = isDel;
    }

    public Long getIsDel()
    {
        return isDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("isDel", getIsDel())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .toString();
    }
}