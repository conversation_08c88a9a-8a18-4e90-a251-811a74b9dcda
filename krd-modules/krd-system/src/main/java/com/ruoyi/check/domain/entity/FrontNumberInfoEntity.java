package com.ruoyi.check.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 样本接收流程过程
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-03 23:10:47
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("front_number_info")
public class FrontNumberInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 操作流程类型：number编码，sample样本，report报告
	 */
	private String handleType;
	/**
	 * 项目编码
	 */
	private String itemCode;
	/**
	 * 操作时间
	 */
	private Date handleTime;
	/**
	 * 操作人id
	 */
	private Long handleUserId;
	/**
	 * 操作人名称
	 */
	private String handleUserName;
	/**
	 * 操作详情
	 */
	private String handleInfo;

	/**
	 * 操作备注
	 */
	private String handleNotes;
}
