package com.ruoyi.check.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.check.domain.entity.FrontNumberInfoEntity;
import com.ruoyi.check.enums.HandleTypeEnum;
import com.ruoyi.system.api.model.LoginUser;

import java.util.List;
import java.util.Map;

/**
 *
 * 样本接收流程过程
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-03 23:10:47
 */
public interface FrontNumberInfoService extends IService<FrontNumberInfoEntity> {

    /**
     * 保存操作日志
     * @param itemCodeList
     * @param handleType
     * @param map
     */
    public void saveOperationLog(List<String> itemCodeList, HandleTypeEnum handleType, Map<String, String> map, LoginUser loginUser);
}

