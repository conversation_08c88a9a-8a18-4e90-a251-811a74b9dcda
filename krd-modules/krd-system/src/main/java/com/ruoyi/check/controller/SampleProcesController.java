package com.ruoyi.check.controller;

import com.github.pagehelper.PageHelper;
import com.ruoyi.check.aop.NumberInfoLog;
import com.ruoyi.check.domain.dto.DistributeAgencyDTO;
import com.ruoyi.check.domain.dto.NumberQueryDTO;
import com.ruoyi.check.domain.entity.FrontNumber;
import com.ruoyi.check.domain.vo.CreateNumberVO;
import com.ruoyi.check.enums.HandleTypeEnum;
import com.ruoyi.check.enums.ProcessStatusEnum;
import com.ruoyi.check.service.IFrontNumberService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 样本处理
 * <AUTHOR>
 * @date 2025/5/29 16:28
 */
@RestController
@RequestMapping("check/sampleproces")
public class SampleProcesController extends BaseController {

    @Autowired
    private IFrontNumberService frontNumberService;

    /**
     * 查询待处理列表
     * @param queryDTO
     * @return
     */
    @PostMapping("/nohandlelist")
    public TableDataInfo nohandleList(@RequestBody NumberQueryDTO queryDTO)
    {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        queryDTO.setProcessStatus(ProcessStatusEnum.STATUS_05.getCode());
        queryDTO.setTimeColumn("num.receive_time");
        List<CreateNumberVO> list = frontNumberService.createNumberList(queryDTO);
        return getDataTable(list);
    }
    /**
     * 查询已经处理列表-未分配机构的列表
     * @param queryDTO
     * @return
     */
    @PostMapping("/okhandlelist")
    public TableDataInfo okhandleList(@RequestBody NumberQueryDTO queryDTO)
    {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        queryDTO.setProcessStatus(ProcessStatusEnum.STATUS_06.getCode());
        queryDTO.setIsAllocation(false);
        queryDTO.setTimeColumn("num.receive_time");
        List<CreateNumberVO> list = frontNumberService.createNumberList(queryDTO);
        //查询未分配机构的数量
        return getDataTable(list);
    }

    /**
     * 处理样本（默认合格）
     * @param itemCode
     * @return
     */
    @NumberInfoLog(handleType = HandleTypeEnum.SAMPLE, handleInfo = "处理样本")
    @GetMapping("/handle/{itemCode}")
    public R handle(@PathVariable("itemCode") String itemCode) {
        return R.ok(frontNumberService.samplehandle(itemCode));
    }



    /**
     * 修改状态是合格还是不合格
     * @param frontNumber
     * @return
     */
    @NumberInfoLog(handleType = HandleTypeEnum.SAMPLE, handleInfo = "修改样本状态")
    @PutMapping("/sampleUpdate")
    public R sampleUpdate(@RequestBody FrontNumber frontNumber) {
        frontNumberService.sampleUpdate(frontNumber);
        return R.ok();
    }

    /**
     * 分配机构
     * @param distributeAgency
     * @return
     */
    @NumberInfoLog(handleType = HandleTypeEnum.SAMPLE, handleInfo = "分配机构")
    @PutMapping("/distributeAgency")
    public R distributeAgency(@RequestBody DistributeAgencyDTO distributeAgency) {
        frontNumberService.distributeAgency(distributeAgency);
        return R.ok();
    }

    /**
     * 查询已经处理-已分配机构的列表
     * @param queryDTO
     * @return
     */
    @PostMapping("/okallocationList")
    public TableDataInfo okallocationList(@RequestBody NumberQueryDTO queryDTO)
    {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        queryDTO.setProcessStatus(ProcessStatusEnum.STATUS_06.getCode());
        queryDTO.setIsAllocation(true);
        queryDTO.setTimeColumn("num.receive_time");
        List<CreateNumberVO> list = frontNumberService.createNumberList(queryDTO);
        //查询未分配机构的数量
        return getDataTable(list);
    }
}
