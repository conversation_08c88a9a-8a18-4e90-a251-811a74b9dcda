package com.ruoyi.check.enums;

import lombok.Getter;

/**
 * 编码状态
 * <AUTHOR>
 * @date 2025/5/21 18:45
 */
public enum NumberStatusEnum {

    STATUS_01("01","已生成",""),
    STATUS_02("02","套码已打印",""),
    STATUS_03("03","转管码已打印",""),
    ;
    @Getter
    private String code;
    private String name;
    private String desc;

    NumberStatusEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }
}
