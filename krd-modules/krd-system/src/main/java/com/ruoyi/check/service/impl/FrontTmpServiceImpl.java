package com.ruoyi.check.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.ruoyi.check.domain.dto.PactarDTO;
import com.ruoyi.check.domain.dto.PactarResultDTO;
import com.ruoyi.check.domain.dto.TmpPactarDTO;
import com.ruoyi.check.domain.entity.FrontTmp;
import com.ruoyi.check.domain.entity.FrontTmpPactarEntity;
import com.ruoyi.check.domain.entity.FrontTmpPactarResultEntity;
import com.ruoyi.check.domain.vo.FrontTmpListVO;
import com.ruoyi.check.mapper.FrontTmpMapper;
import com.ruoyi.check.mapper.FrontTmpPactarMapper;
import com.ruoyi.check.mapper.FrontTmpPactarResultMapper;
import com.ruoyi.check.service.IFrontTmpService;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.model.LoginUser;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报告模版Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
public class FrontTmpServiceImpl implements IFrontTmpService
{
    @Autowired
    private FrontTmpMapper frontTmpMapper;
    @Autowired
    private FrontTmpPactarMapper frontTmpPactarMapper;
    @Autowired
    private FrontTmpPactarResultMapper frontTmpPactarResultMapper;
    @Autowired
    private TokenService tokenService;

    /**
     * 查询报告模版
     *
     * @param id 报告模版主键
     * @return 报告模版
     */
    @Override
    public FrontTmp selectFrontTmpById(String id)
    {
        return frontTmpMapper.selectFrontTmpById(id);
    }

    /**
     * 查询报告模版列表
     *
     * @param frontTmp 报告模版
     * @return 报告模版
     */
    @Override
    public List<FrontTmpListVO> selectFrontTmpList(FrontTmp frontTmp)
    {
        return frontTmpMapper.selectTmpList(frontTmp);
    }

    /**
     * 新增报告模版
     *
     * @param frontTmp 报告模版
     * @return 结果
     */
    @Override
    public int insertFrontTmp(FrontTmp frontTmp)
    {
        LoginUser user = tokenService.getLoginUser();
        frontTmp.setCreateTime(DateUtils.getNowDate());
        frontTmp.setCreateBy(user.getUsername());
        frontTmp.setUpdateBy(user.getUsername());
        frontTmp.setUpdateTime(DateUtils.getNowDate());
        return frontTmpMapper.insertFrontTmp(frontTmp);
    }

    /**
     * 修改报告模版
     *
     * @param frontTmp 报告模版
     * @return 结果
     */
    @Override
    public int updateFrontTmp(FrontTmp frontTmp)
    {
        LoginUser user = tokenService.getLoginUser();
        frontTmp.setUpdateBy(user.getUsername());
        frontTmp.setUpdateTime(DateUtils.getNowDate());
        return frontTmpMapper.updateFrontTmp(frontTmp);
    }

    /**
     * 批量删除报告模版
     *
     * @param ids 需要删除的报告模版主键
     * @return 结果
     */
    @Override
    public int deleteFrontTmpByIds(String[] ids)
    {
        return frontTmpMapper.deleteFrontTmpByIds(ids);
    }

    /**
     * 删除报告模版信息
     *
     * @param id 报告模版主键
     * @return 结果
     */
    @Override
    public int deleteFrontTmpById(String id)
    {
        return frontTmpMapper.deleteFrontTmpById(id);
    }

    @Transactional
    @Override
    public int addTmp(TmpPactarDTO dto) {
        String userName = tokenService.getLoginUser().getUsername();
        Date nowDate = DateUtils.getNowDate();
        String tmpId = IdWorker.getIdStr();//新建模板id

        //暂存保存的模板对象
        FrontTmp tmp = new FrontTmp();
        //暂存保存的模板指标对象
        List<FrontTmpPactarEntity> tmpPactarList = new ArrayList<>();
        //暂存保存的模板指标结果对象
        List<FrontTmpPactarResultEntity> pactarResultList = new ArrayList<>();

        tmp.setId(tmpId);
        tmp.setTmpName(dto.getTmpName());
        tmp.setPackageId(dto.getPackageId());
        tmp.setCreateTime(nowDate);
        tmp.setUpdateTime(nowDate);
        tmp.setCreateBy(userName);
        tmp.setUpdateBy(userName);

        List<PactarDTO> targetList = dto.getTargetList();
        if(CollectionUtils.isEmpty(targetList)){
            throw new RuntimeException("指标不能为空");
        }
        targetList.forEach(item->{
            String tpId = IdWorker.getIdStr();//创建模板指标id
            tmpPactarList.add(FrontTmpPactarEntity.builder()
                    .id(tpId)
                    .tmpId(tmpId)
                    .packageId(dto.getPackageId())
                    .targetId(item.getTargetId())
                    .targetAnalysis(item.getTargetAnalysis())
                    .targetReferenceRange(item.getTargetReferenceRange())
                    .createTime(nowDate)
                    .updateTime(nowDate)
                    .createBy(userName)
                    .updateBy(userName)
                    .isDel(0)
                    .build()
            );

            List<FrontTmpPactarResultEntity> pactarResultDtoList = item.getPactarResultList()
                    .stream()
                    .filter(pr-> pr.getIsDel() == 0)
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(pactarResultDtoList)){
                throw new RuntimeException("每一个指标的检测结果至少存在一项");
            }
            pactarResultDtoList.forEach(pr->{
                pactarResultList.add(FrontTmpPactarResultEntity.builder()
                        .id(IdWorker.getIdStr())
                        .pactarId(tpId)
                        .resultName(pr.getResultName())
                        .resultColor(pr.getResultColor())
                        .resultRangeStartVal(pr.getResultRangeStartVal())
                        .resultRangeStartSymbol(pr.getResultRangeStartSymbol())
                        .resultRangeEndVal(pr.getResultRangeEndVal())
                        .resultRangeEndSymbol(pr.getResultRangeEndSymbol())
                        .resultInterpret(pr.getResultInterpret())
                        .healthAdvice(pr.getHealthAdvice())
                        .createTime(nowDate)
                        .updateTime(nowDate)
                        .createBy(userName)
                        .updateBy(userName)
                        .isDel(0)
                        .build());
            });
        });
        //保存暂存的模板对象
        frontTmpMapper.insert(tmp);
        //保存暂存的模板指标对象
        frontTmpPactarMapper.insertBatch(tmpPactarList);
        //保存暂存的模板指标结果对象
        frontTmpPactarResultMapper.insertBatch(pactarResultList);
        return 1;
    }

    @Override
    public TmpPactarDTO getTmpInfo(String tmpId) {
        FrontTmp tmpModel = frontTmpMapper.selectOne(new LambdaQueryWrapper<FrontTmp>()
                .eq(FrontTmp::getId, tmpId));

        List<PactarDTO> targetList = new ArrayList<>();
        List<PactarResultDTO> dtoList = frontTmpPactarMapper.selectTmpModel(tmpId);
        dtoList.stream().collect(Collectors.groupingBy(PactarResultDTO::getPactarId))
                .forEach((key,value)->{
                    List<FrontTmpPactarResultEntity> pactarResultList = value.stream().map(item -> {
                        FrontTmpPactarResultEntity model = new FrontTmpPactarResultEntity();
                        BeanUtils.copyProperties(item, model);
                        model.setId(item.getPrtId());
                        return model;
                    }).collect(Collectors.toList());
                    //按照起始值升序排序
                    pactarResultList.sort(Comparator.comparing(FrontTmpPactarResultEntity::getResultRangeStartVal));
                    targetList.add(PactarDTO.builder()
                            .packageId(value.get(0).getPackageId())
                            .targetId(value.get(0).getTargetId())
                            .targetName(value.get(0).getTargetName())
                            .targetAnalysis(value.get(0).getTargetAnalysis())
                            .targetReferenceRange(value.get(0).getTargetReferenceRange())
                            .ptIsDel(value.get(0).getPtIsDel())
                            .pactarId(key)
                            .pactarResultList(pactarResultList)
                            .build());
                });
        return TmpPactarDTO.builder()
                .tmpId(tmpId)
                .tmpName(tmpModel.getTmpName())
                .packageId(tmpModel.getPackageId())
                .targetList(targetList)
                .build();
    }

    @Transactional
    @Override
    public int updateTmp(TmpPactarDTO dto) {
        String userName = tokenService.getLoginUser().getUsername();
        Date nowDate = DateUtils.getNowDate();
        String tmpId = dto.getTmpId();//获取模板id

        //暂存保存的模板对象
        FrontTmp tmp = new FrontTmp();
        //暂存新增的模板指标对象
        List<FrontTmpPactarEntity> addTmpPactarList = new ArrayList<>();
        //暂存新增的模板指标结果对象
        List<FrontTmpPactarResultEntity> addPactarResultList = new ArrayList<>();

        tmp.setId(tmpId);
        tmp.setTmpName(dto.getTmpName());
        tmp.setPackageId(dto.getPackageId());
        tmp.setUpdateTime(nowDate);
        tmp.setUpdateBy(userName);

        List<PactarDTO> targetList = dto.getTargetList();
        if(CollectionUtils.isEmpty(targetList)){
            throw new RuntimeException("指标不能为空");
        }
        targetList.forEach(item->{
            String pactarId = null;
            if(StringUtils.isBlank(item.getPactarId())){
                pactarId = IdWorker.getIdStr();//创建模板指标id
            }else {
                pactarId = item.getPactarId();
            }

            if(StringUtils.isBlank(item.getPactarId())){//新增
                addTmpPactarList.add(FrontTmpPactarEntity.builder()
                        .id(pactarId)
                        .tmpId(tmpId)
                        .packageId(dto.getPackageId())
                        .targetId(item.getTargetId())
                        .targetAnalysis(item.getTargetAnalysis())
                        .targetReferenceRange(item.getTargetReferenceRange())
                        .createTime(nowDate)
                        .updateTime(nowDate)
                        .createBy(userName)
                        .updateBy(userName)
                        .isDel(0)
                        .build());
            }else {//更新
                frontTmpPactarMapper.updateById(FrontTmpPactarEntity.builder()
                        .id(pactarId)
                        .tmpId(tmpId)
                        .packageId(dto.getPackageId())
                        .targetId(item.getTargetId())
                        .targetAnalysis(item.getTargetAnalysis())
                        .targetReferenceRange(item.getTargetReferenceRange())
                        .updateTime(nowDate)
                        .updateBy(userName)
                        .isDel(item.getPtIsDel())
                        .build()
                );
            }


            List<FrontTmpPactarResultEntity> pactarResultDtoList = item.getPactarResultList();
            long count = pactarResultDtoList.stream().filter(pr -> pr.getIsDel() == 0).count();
            if(count < 1){
                throw new RuntimeException("每一个指标的检测结果至少存在一项");
            }
            String finalPactarId = pactarId;
            pactarResultDtoList.forEach(pr->{
                if(StringUtils.isBlank(pr.getId())){//新增
                    if(pr.getIsDel() == 0){//只添加为0的
                        addPactarResultList.add(FrontTmpPactarResultEntity.builder()
                                .id(IdWorker.getIdStr())
                                .pactarId(finalPactarId)
                                .resultName(pr.getResultName())
                                .resultColor(pr.getResultColor())
                                .resultRangeStartVal(pr.getResultRangeStartVal())
                                .resultRangeStartSymbol(pr.getResultRangeStartSymbol())
                                .resultRangeEndVal(pr.getResultRangeEndVal())
                                .resultRangeEndSymbol(pr.getResultRangeEndSymbol())
                                .resultInterpret(pr.getResultInterpret())
                                .healthAdvice(pr.getHealthAdvice())
                                .createTime(nowDate)
                                .updateTime(nowDate)
                                .createBy(userName)
                                .updateBy(userName)
                                .isDel(0)
                                .build());
                    }
                }else {
                    frontTmpPactarResultMapper.updateById(FrontTmpPactarResultEntity.builder()
                            .id(pr.getId())
                            .pactarId(finalPactarId)
                            .resultName(pr.getResultName())
                            .resultColor(pr.getResultColor())
                            .resultRangeStartVal(pr.getResultRangeStartVal())
                            .resultRangeStartSymbol(pr.getResultRangeStartSymbol())
                            .resultRangeEndVal(pr.getResultRangeEndVal())
                            .resultRangeEndSymbol(pr.getResultRangeEndSymbol())
                            .resultInterpret(pr.getResultInterpret())
                            .healthAdvice(pr.getHealthAdvice())
                            .updateTime(nowDate)
                            .updateBy(userName)
                            .isDel(pr.getIsDel())
                            .build());
                }
            });
        });
        //保存暂存的模板对象
        frontTmpMapper.updateById(tmp);
        if(CollectionUtils.isNotEmpty(addTmpPactarList)){
            //保存新增的模板指标对象
            frontTmpPactarMapper.insertBatch(addTmpPactarList);
        }

        if(CollectionUtils.isNotEmpty(addPactarResultList)){
            //保存新增的模板指标结果对象
            frontTmpPactarResultMapper.insertBatch(addPactarResultList);
        }
        return 1;
    }
}