package com.ruoyi.check.domain.dto;

import com.ruoyi.check.domain.entity.FrontReportResultEntity;
import com.ruoyi.check.domain.vo.CreateNumberVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26 11:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportExamineDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String itemCode;

    private CreateNumberVO numberDetails;

    private List<FrontReportResultEntity> reportList;
}
