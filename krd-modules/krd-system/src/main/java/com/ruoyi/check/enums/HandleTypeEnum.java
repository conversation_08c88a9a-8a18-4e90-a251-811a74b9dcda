package com.ruoyi.check.enums;

import lombok.Getter;

/**
 * 操作流程类型
 * <AUTHOR>
 * @date 2025/5/21 18:45
 */
public enum HandleTypeEnum {

    NUMBER("number","编码",""),
    SAMPLE("sample","样本",""),
    REPORT("report","报告",""),
    ;
    @Getter
    private String code;
    private String name;
    private String desc;

    HandleTypeEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }
}
