package com.ruoyi.check.service;

import java.util.List;

import com.ruoyi.check.domain.dto.PactarDTO;
import com.ruoyi.check.domain.dto.TmpPactarDTO;
import com.ruoyi.check.domain.entity.FrontTmp;
import com.ruoyi.check.domain.vo.FrontTmpListVO;

/**
 * 报告模版Service接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IFrontTmpService
{
    /**
     * 查询报告模版
     *
     * @param id 报告模版主键
     * @return 报告模版
     */
    public FrontTmp selectFrontTmpById(String id);

    /**
     * 查询报告模版列表
     *
     * @param frontTmp 报告模版
     * @return 报告模版集合
     */
    public List<FrontTmpListVO> selectFrontTmpList(FrontTmp frontTmp);

    /**
     * 新增报告模版
     *
     * @param frontTmp 报告模版
     * @return 结果
     */
    public int insertFrontTmp(FrontTmp frontTmp);

    /**
     * 修改报告模版
     *
     * @param frontTmp 报告模版
     * @return 结果
     */
    public int updateFrontTmp(FrontTmp frontTmp);

    /**
     * 批量删除报告模版
     *
     * @param ids 需要删除的报告模版主键集合
     * @return 结果
     */
    public int deleteFrontTmpByIds(String[] ids);

    /**
     * 删除报告模版信息
     *
     * @param id 报告模版主键
     * @return 结果
     */
    public int deleteFrontTmpById(String id);

    int addTmp(TmpPactarDTO dto);

    TmpPactarDTO getTmpInfo(String tmpId);

    int updateTmp(TmpPactarDTO dto);
}