package com.ruoyi.check.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 套餐管理对象 front_package
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@TableName("front_package")
public class FrontPackage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 检测套餐名称 */
    @Excel(name = "检测套餐名称")
    private String pakeageName;

    /** 套餐指标 */
    @Excel(name = "套餐指标")
    private String packageTarget;

    /** 项目编号 */
    @Excel(name = "项目编号")
    private String number;

    /** 0-false,1-true */
    private Long isDel;

    /** 添加人员id */
    @Excel(name = "添加人员id")
    private Long userId;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pakeageName", getPakeageName())
            .append("packageTarget", getPackageTarget())
            .append("number", getNumber())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("isDel", getIsDel())
            .append("userId", getUserId())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
