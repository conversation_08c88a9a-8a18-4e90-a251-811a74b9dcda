package com.ruoyi.check.controller;

import com.ruoyi.check.domain.dto.NumberQueryDTO;
import com.ruoyi.check.domain.entity.FrontNumberInfoEntity;
import com.ruoyi.check.domain.vo.CreateNumberVO;
import com.ruoyi.check.enums.HandleTypeEnum;
import com.ruoyi.check.service.FrontNumberInfoService;
import com.ruoyi.check.service.IFrontNumberService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 样本管理
 * <AUTHOR>
 * @date 2025/5/29 16:28
 */
@RestController
@RequestMapping("check/samplemanager")
public class SampleManagerController extends BaseController {

    @Autowired
    private IFrontNumberService frontNumberService;
    @Autowired
    private FrontNumberInfoService frontNumberInfoService;

    @GetMapping("/list")
    public TableDataInfo queryNumberList(NumberQueryDTO queryDTO)
    {
        startPage();
        queryDTO.setModule("sample");
        List<CreateNumberVO> list = frontNumberService.createNumberList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 导出样本管理列表
     */
    @RequiresPermissions("check:number:export")
    @Log(title = "样本管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NumberQueryDTO queryDTO)
    {
        List<CreateNumberVO> list = frontNumberService.createNumberList(queryDTO);
        ExcelUtil<CreateNumberVO> util = new ExcelUtil<>(CreateNumberVO.class);
        util.exportExcel(response, list, "样本管理数据");
    }

    /**
     * 查询样本操作记录列表
     * @param itemCode
     * @return
     */
    @GetMapping("/qeuryNumberInfoList/{itemCode}")
    public R qeuryNumberInfoList(@PathVariable("itemCode") String itemCode){
        List<FrontNumberInfoEntity> resultList = frontNumberInfoService.lambdaQuery()
                .eq(FrontNumberInfoEntity::getItemCode, itemCode)
                .eq(FrontNumberInfoEntity::getHandleType, HandleTypeEnum.SAMPLE.getCode())
                .list();
        return R.ok(resultList);
    }
}
