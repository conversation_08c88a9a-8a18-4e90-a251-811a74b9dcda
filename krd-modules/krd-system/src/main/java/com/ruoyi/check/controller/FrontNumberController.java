package com.ruoyi.check.controller;

import com.github.pagehelper.PageHelper;
import com.ruoyi.check.domain.dto.NumberQueryDTO;
import com.ruoyi.check.domain.entity.FrontNumber;
import com.ruoyi.check.domain.entity.FrontNumberInfoEntity;
import com.ruoyi.check.domain.vo.CreateNumberVO;
import com.ruoyi.check.enums.HandleTypeEnum;
import com.ruoyi.check.service.FrontNumberInfoService;
import com.ruoyi.check.service.IFrontNumberService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 编码管理Controller
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/number")
public class FrontNumberController extends BaseController
{
    @Autowired
    private IFrontNumberService frontNumberService;
    @Autowired
    private FrontNumberInfoService frontNumberInfoService;

    /**
     * 查询编码管理列表
     */
    @RequiresPermissions("check:number:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontNumber frontNumber)
    {
        startPage();
        List<FrontNumber> list = frontNumberService.selectFrontNumberList(frontNumber);
        return getDataTable(list);
    }

    /**
     * 获取编码管理详细信息
     */
    @RequiresPermissions("check:number:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontNumberService.selectFrontNumberById(id));
    }

    /**
     * 新增编码管理
     */
    @RequiresPermissions("check:number:add")
    @Log(title = "编码管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontNumber frontNumber)
    {
        return toAjax(frontNumberService.insertFrontNumber(frontNumber));
    }

    /**
     * 修改编码管理
     */
    @RequiresPermissions("check:number:edit")
    @Log(title = "编码管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontNumber frontNumber)
    {
        return toAjax(frontNumberService.updateFrontNumber(frontNumber));
    }

    /**
     * 删除编码管理
     */
    @RequiresPermissions("check:number:remove")
    @Log(title = "编码管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{itemCodes}")
    public AjaxResult remove(@PathVariable String[] itemCodes)
    {
        return toAjax(frontNumberService.deleteFrontNumberByIds(itemCodes));
    }

    @RequiresPermissions("check:number:query:list")
    @PostMapping("/query/list")
    public TableDataInfo queryNumberList(@RequestBody NumberQueryDTO queryDTO)
    {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        queryDTO.setTimeColumn("num.code_create_time");
        queryDTO.setOrderByColumn("num.code_create_time");
        queryDTO.setOrderByType("desc");
        List<CreateNumberVO> list = frontNumberService.createNumberList(queryDTO);
        return getDataTable(list);
    }

    @PostMapping("/querysample/list")
    public TableDataInfo querySampleList(@RequestBody NumberQueryDTO queryDTO)
    {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        queryDTO.setTimeColumn("num.receive_time");
        queryDTO.setOrderByColumn("num.receive_time");
        queryDTO.setOrderByType("desc");
        List<CreateNumberVO> list = frontNumberService.createNumberList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 导出编码管理列表
     */
    @RequiresPermissions("check:number:export")
    @Log(title = "编码管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NumberQueryDTO queryDTO)
    {
        List<CreateNumberVO> list = frontNumberService.createNumberList(queryDTO);
        ExcelUtil<CreateNumberVO> util = new ExcelUtil<>(CreateNumberVO.class);
        util.exportExcel(response, list, "编码管理数据");
    }

    /**
     * 查询编码操作记录列表
     * @param itemCode
     * @return
     */
    @GetMapping("/qeuryNumberInfoList/{itemCode}")
    public R qeuryNumberInfoList(@PathVariable("itemCode") String itemCode){
        List<FrontNumberInfoEntity> resultList = frontNumberInfoService.lambdaQuery()
                .eq(FrontNumberInfoEntity::getItemCode, itemCode)
                .eq(FrontNumberInfoEntity::getHandleType, HandleTypeEnum.NUMBER.getCode())
                .list();
        return R.ok(resultList);
    }
}
