package com.ruoyi.check.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 报告模版-套餐-指标解释
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 18:23:57
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("front_tmp_pactar")
public class FrontTmpPactarEntity implements Serializable {

	private static final long serialVersionUID = 1934919938212261889L;

	/**
	 * 
	 */
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 模版id
	 */
	private String tmpId;
	/**
	 * 套餐id
	 */
	private String packageId;
	/**
	 * 指标id
	 */
	private String targetId;
	/**
	 * 指标名称解释
	 */
	private String targetAnalysis;
	/**
	 * 指标参考范围
	 */
	private String targetReferenceRange;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 是否删除0-false ,1-true
	 */
	private Integer isDel;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;

}
