package com.ruoyi.check.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26 11:35
 */
@Data
public class CreateNumberDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> itemCodes;
    /**
     * 套餐id
     */
    private String packageId;

    /**
     * 套餐名称
     */
    private String pakeageName;

    /**
     *套餐-项目编号
     */
    private String itemNumber;

    /**
     * 自定义编码
     */
    private String number;

    /**
     * 二维码数量
     */
    private Integer qrcount;

    /**
     * 条形码数量
     */
    private Integer barcount;

    /**
     * 生成套数
     */
    private int createCount;

    /**
     * 是否按套裁剪
     */
    private String isCrop;
}
