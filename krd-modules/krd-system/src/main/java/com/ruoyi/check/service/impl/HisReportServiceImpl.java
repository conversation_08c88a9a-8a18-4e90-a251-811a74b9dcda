package com.ruoyi.check.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.check.domain.entity.TTestPatientsHisReportEntity;
import com.ruoyi.check.domain.entity.TTestResultsHisReportEntity;
import com.ruoyi.check.mapper.HisReportMapper;
import com.ruoyi.check.service.HisReportService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@DS("check-db")
@Service("tTestResultsHisReportService")
public class HisReportServiceImpl implements HisReportService {

    @Resource
    private HisReportMapper hisReportMapper;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public List<TTestPatientsHisReportEntity> getPatientsListByItemCode(List<String> itemCodes) {
        return hisReportMapper.getPatientsListByItemCode(itemCodes);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public List<TTestResultsHisReportEntity> getResultsListByItemCode(List<String> itemCodes) {
        return hisReportMapper.getResultsListByItemCode(itemCodes);
    }
}