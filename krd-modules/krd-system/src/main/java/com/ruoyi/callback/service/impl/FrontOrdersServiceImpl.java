package com.ruoyi.callback.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.callback.service.FrontOrdersService;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.mapper.FrontOrdersMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Author: suhai
 * @Date: 2025/6/19
 * @Description:
 */
@Service
@AllArgsConstructor
public class FrontOrdersServiceImpl extends ServiceImpl<FrontOrdersMapper, FrontOrders> implements FrontOrdersService {


    private final FrontOrdersMapper frontOrdersMapper;

    @Override
    public FrontOrders getInfoByEntity(FrontOrders frontOrders) {
        LambdaQueryWrapper<FrontOrders> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.setEntity(frontOrders);
        return frontOrdersMapper.selectOne(lambdaQueryWrapper);
    }
}
