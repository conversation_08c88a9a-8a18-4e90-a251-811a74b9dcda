package com.ruoyi.callback.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.callback.service.WechatPayService;
import com.ruoyi.system.api.domain.WechatPayInfo;
import com.ruoyi.system.api.mapper.WechatPayInfoMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Author: suhai
 * @Date: 2025/6/19
 * @Description:
 */
@Service
@AllArgsConstructor
public class WechatPayServiceImpl extends ServiceImpl<WechatPayInfoMapper, WechatPayInfo>  implements WechatPayService {

    private final WechatPayInfoMapper dao;


    /**
     * 获取详情（商户订单号）
     * @param outTradeNo 商户订单号
     * @return WechatPayInfo
     */
    @Override
    public WechatPayInfo getByNo(String outTradeNo) {
        LambdaQueryWrapper<WechatPayInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(WechatPayInfo::getOutTradeNo, outTradeNo);
        return dao.selectOne(lqw);
    }
}
