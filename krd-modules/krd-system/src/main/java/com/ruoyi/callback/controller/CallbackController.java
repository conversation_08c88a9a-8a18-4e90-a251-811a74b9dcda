package com.ruoyi.callback.controller;


import com.ruoyi.callback.service.CallbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: suhai
 * @Date: 2025/6/18
 * @Description: 小程序支付 充值成功后回调处理
 */
@Slf4j
@RestController
@RequestMapping("/callback")
@Tag(name = "支付回调", description = "支付回调")
@RequiredArgsConstructor
public class CallbackController {

    private final CallbackService callbackService;


    /**
     * 微信支付回调
     */
    @Operation(description = "微信支付回调")
    @RequestMapping(value = "/wechat", method = RequestMethod.POST)
    public String weChat(@RequestBody String  request) {
        log.info("微信支付回调 request ===> {}" , request);
        return callbackService.weChat(request);
    }

    /**
     * 微信退款回调
     */
    @Operation(description = "微信退款回调")
    @RequestMapping(value = "/wechat/refund", method = RequestMethod.POST)
    public String weChatRefund(@RequestBody String request) {
        log.info("微信退款回调 request ===> {}" , request);
        return callbackService.weChatRefund(request);
    }
}
