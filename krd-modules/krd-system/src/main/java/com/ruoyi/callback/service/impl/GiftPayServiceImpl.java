package com.ruoyi.callback.service.impl;

import com.ruoyi.callback.service.GiftPayService;
import com.ruoyi.system.api.domain.*;
import com.ruoyi.system.api.mapper.FrontGiftInfoMapper;
import com.ruoyi.system.api.mapper.FrontGiftMapper;
import com.ruoyi.system.api.mapper.FrontGiftUserInfoMapper;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.system.api.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @ClassName GiftPayServiceImpl
 * @Description 礼品卡订单支付成功处理
 * <AUTHOR>
 * @Date 2025/6/25 上午10:16
 */
@Service
@Slf4j
public class GiftPayServiceImpl implements GiftPayService {

    @Autowired
    private FrontGiftInfoMapper frontGiftInfoMapper;

    @Autowired
    private FrontGiftUserInfoMapper frontGiftUserInfoMapper;

    @Autowired
    private FrontGiftMapper frontGiftMapper;

    @Override
    public Boolean paySuccess(UserRecharge userRecharge) {
        FrontGift frontGift = frontGiftMapper.selectById(userRecharge.getGiftId());
        userRecharge.setPaid(true);
        userRecharge.setPayTime(new Date());
        BigDecimal payPrice = userRecharge.getPrice();
        log.info("用户购买礼品卡成功，用户id：{}，购买金额：{}" , userRecharge.getUid() , payPrice);
        FrontGiftInfo frontGiftInfo = new FrontGiftInfo();
        frontGiftInfo.setUserId(Long.valueOf(userRecharge.getUid()));
        frontGiftInfo.setCreateTime(LocalDateTime.now());
        frontGiftInfo.setStatus("0");
        if ("0".equals(frontGift.getExpirationDate())){
            frontGiftInfo.setUseTime(null);
        }else {
            frontGiftInfo.setUseTime(LocalDateTime.now().plusDays(Long.parseLong(frontGift.getExpirationDate())));
        }
        frontGiftInfo.setBalance(frontGift.getBalance());
        frontGiftInfo.setOrderNumber(CommonUtil.getOrderNo("gift"));
        frontGiftInfo.setGiftId(frontGift.getId());
        frontGiftInfo.setType(0);
        // 添加礼品卡明细
        FrontGiftUserInfo frontGiftUserInfo = new FrontGiftUserInfo();
        frontGiftUserInfo.setGiftInfoId(frontGiftInfo.getId());
        frontGiftUserInfo.setOrderNo(userRecharge.getOrderId());
        frontGiftUserInfo.setType(1);
        frontGiftUserInfo.setBalance(frontGift.getBalance());
        return frontGiftInfoMapper.insert(frontGiftInfo) > 0 && frontGiftUserInfoMapper.insert(frontGiftUserInfo) > 0;
    }
}
