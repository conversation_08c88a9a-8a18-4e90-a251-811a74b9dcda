package com.ruoyi.callback.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: suhai
 * @Date: 2025/6/19
 * @Description:
 */
@Data
public class OrderInfo {
    private Integer uid; // 用户ID
    private Boolean paid; // 支付状态
    private String orderId; // 订单ID
    private String outTradeNo; // 商户系统内部的订单号
    private Integer useIntegral;//使用积分
    private BigDecimal useYue;//使用余额
    private Integer id;//方便不同类型修改的索引id

    public OrderInfo(Integer uid, Boolean paid, String orderId, String outTradeNo, Integer id, Integer useIntegral, BigDecimal useYue) {
        this.uid = uid;
        this.paid = paid;
        this.orderId = orderId;
        this.outTradeNo = outTradeNo;
        this.id = id;
        this.useIntegral = useIntegral;
        this.useYue = useYue;
    }
}
