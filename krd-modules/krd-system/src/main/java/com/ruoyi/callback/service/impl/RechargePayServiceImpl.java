package com.ruoyi.callback.service.impl;

import com.ruoyi.callback.service.RechargePayService;
import com.ruoyi.system.api.domain.FrontBalanceInfo;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.UserRecharge;
import com.ruoyi.system.api.mapper.FrontBalanceInfoMapper;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Author: suhai
 * @Date: 2025/6/18
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class RechargePayServiceImpl implements RechargePayService {

    private final FrontUserMapper frontUserMapper;

    private final FrontBalanceInfoMapper frontBalanceInfoMapper;

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(RechargePayServiceImpl.class);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean paySuccess(UserRecharge userRecharge) {
        userRecharge.setPaid(true);
        userRecharge.setPayTime(new Date());
        FrontUser frontUser = frontUserMapper.selectFrontUserById(Long.valueOf(userRecharge.getUid()));
        BigDecimal payPrice = userRecharge.getPrice();
        BigDecimal balance = frontUser.getNowMoney().add(userRecharge.getPrice());
        frontUser.setNowMoney(balance);
        log.info("用户充值成功，用户id：{}，充值金额：{}，余额：{}" , userRecharge.getUid() , payPrice , balance);
        FrontBalanceInfo frontBalanceInfo = new FrontBalanceInfo();
        frontBalanceInfo.setUserId(Long.valueOf(userRecharge.getUid()));
        frontBalanceInfo.setType(0L);
        frontBalanceInfo.setMoveaccount(payPrice.toString());
        frontBalanceInfo.setBalance(balance);
        frontBalanceInfo.setOrderNumber(userRecharge.getOrderId());
        frontBalanceInfo.setTitle("用户充值");
        frontBalanceInfo.setCreateTime(LocalDateTime.now());
        return frontUserMapper.updateFrontUser(frontUser) > 0 && frontBalanceInfoMapper.insert(frontBalanceInfo) > 0;
    }
}
