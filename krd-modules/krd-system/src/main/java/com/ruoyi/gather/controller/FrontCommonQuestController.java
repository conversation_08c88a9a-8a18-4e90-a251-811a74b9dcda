package com.ruoyi.gather.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.gather.service.IFrontCommonQuestService;
import com.ruoyi.system.api.domain.FrontCommonQuest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 常见问题Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/quest")
public class FrontCommonQuestController extends BaseController
{
    @Autowired
    private IFrontCommonQuestService frontCommonQuestService;

    /**
     * 查询常见问题列表
     */
    @RequiresPermissions("gather:quest:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontCommonQuest frontCommonQuest)
    {
        startPage();
        List<FrontCommonQuest> list = frontCommonQuestService.selectFrontCommonQuestList(frontCommonQuest);
        return getDataTable(list);
    }

    /**
     * 导出常见问题列表
     */
    @RequiresPermissions("gather:quest:export")
    @Log(title = "常见问题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontCommonQuest frontCommonQuest)
    {
        List<FrontCommonQuest> list = frontCommonQuestService.selectFrontCommonQuestList(frontCommonQuest);
        ExcelUtil<FrontCommonQuest> util = new ExcelUtil<FrontCommonQuest>(FrontCommonQuest.class);
        util.exportExcel(response, list, "常见问题数据");
    }

    /**
     * 获取常见问题详细信息
     */
    @RequiresPermissions("gather:quest:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontCommonQuestService.selectFrontCommonQuestById(id));
    }

    /**
     * 新增常见问题
     */
    @RequiresPermissions("gather:quest:add")
    @Log(title = "常见问题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontCommonQuest frontCommonQuest)
    {
        return toAjax(frontCommonQuestService.insertFrontCommonQuest(frontCommonQuest));
    }

    /**
     * 修改常见问题
     */
    @RequiresPermissions("gather:quest:edit")
    @Log(title = "常见问题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontCommonQuest frontCommonQuest)
    {
        return toAjax(frontCommonQuestService.updateFrontCommonQuest(frontCommonQuest));
    }

    /**
     * 删除常见问题
     */
    @RequiresPermissions("gather:quest:remove")
    @Log(title = "常见问题", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontCommonQuestService.deleteFrontCommonQuestByIds(ids));
    }
}
