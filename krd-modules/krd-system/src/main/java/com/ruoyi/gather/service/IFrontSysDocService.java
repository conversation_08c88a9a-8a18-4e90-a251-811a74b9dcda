package com.ruoyi.gather.service;

import com.ruoyi.system.api.domain.FrontSysDoc;

import java.util.List;

/**
 * 文档管理Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface IFrontSysDocService
{
    /**
     * 查询文档管理
     *
     * @param id 文档管理主键
     * @return 文档管理
     */
    public FrontSysDoc selectFrontSysDocById(Long id);

    /**
     * 查询文档管理列表
     *
     * @param frontSysDoc 文档管理
     * @return 文档管理集合
     */
    public List<FrontSysDoc> selectFrontSysDocList(FrontSysDoc frontSysDoc);

    /**
     * 新增文档管理
     *
     * @param frontSysDoc 文档管理
     * @return 结果
     */
    public int insertFrontSysDoc(FrontSysDoc frontSysDoc);

    /**
     * 修改文档管理
     *
     * @param frontSysDoc 文档管理
     * @return 结果
     */
    public int updateFrontSysDoc(FrontSysDoc frontSysDoc);

    /**
     * 批量删除文档管理
     *
     * @param ids 需要删除的文档管理主键集合
     * @return 结果
     */
    public int deleteFrontSysDocByIds(Long[] ids);

    /**
     * 删除文档管理信息
     *
     * @param id 文档管理主键
     * @return 结果
     */
    public int deleteFrontSysDocById(Long id);
}
