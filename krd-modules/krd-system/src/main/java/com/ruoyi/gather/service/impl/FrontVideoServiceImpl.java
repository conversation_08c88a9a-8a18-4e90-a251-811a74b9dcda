package com.ruoyi.gather.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.gather.service.IFrontVideoService;
import com.ruoyi.system.api.domain.FrontVideo;
import com.ruoyi.system.api.mapper.FrontVideoMapper;
import com.ruoyi.system.api.oss.OssUrlCleanerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 采集视频Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class FrontVideoServiceImpl implements IFrontVideoService
{
    @Autowired
    private FrontVideoMapper frontVideoMapper;

    @Autowired
    private OssUrlCleanerUtil  ossUrlCleanerUtil;


    /**
     * 查询采集视频
     *
     * @param id 采集视频主键
     * @return 采集视频
     */
    @Override
    public FrontVideo selectFrontVideoById(Long id) throws JsonProcessingException {
        FrontVideo frontVideo = frontVideoMapper.selectFrontVideoById(id);
        frontVideo.setUrl(ossUrlCleanerUtil.getSignatureUrl(frontVideo.getUrl()));
        frontVideo.setCover(ossUrlCleanerUtil.getSignatureUrl(frontVideo.getCover()));
        String step = frontVideo.getStep();
        if (step != null) {
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, Object>> list = mapper.readValue(step, new TypeReference<List<Map<String, Object>>>() {});

            for (Map<String, Object> item : list) {
                String coverUrl = (String) item.get("cover");
                item.put("cover", ossUrlCleanerUtil.getSignatureUrl(coverUrl));
            }
            String updatedStepJson = mapper.writeValueAsString(list);
            frontVideo.setStep(updatedStepJson);
        }
        return frontVideo;
    }

    /**
     * 查询采集视频列表
     *
     * @param frontVideo 采集视频
     * @return 采集视频
     */
    @Override
    public List<FrontVideo> selectFrontVideoList(FrontVideo frontVideo)
    {
        List<FrontVideo> frontVideos = frontVideoMapper.selectFrontVideoList(frontVideo);
        frontVideos.forEach(video -> {
                    video.setUrl(ossUrlCleanerUtil.getSignatureUrl(video.getUrl()));
                    video.setCover(ossUrlCleanerUtil.getSignatureUrl(video.getCover()));
                    String step = video.getStep();
                    if (step!= null) {
                        ObjectMapper mapper = new ObjectMapper();
                        List<Map<String, Object>> list = null;
                        try {
                            list = mapper.readValue(step, new TypeReference<List<Map<String, Object>>>() {});
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                        for (Map<String, Object> item : list) {
                            String coverUrl = (String) item.get("cover");
                            item.put("cover", ossUrlCleanerUtil.getSignatureUrl(coverUrl));
                        }
                        try {
                            String updatedStepJson = mapper.writeValueAsString(list);
                            video.setStep(updatedStepJson);
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                    }

                }

        );
        return frontVideos;
    }

    /**
     * 新增采集视频
     *
     * @param frontVideo 采集视频
     * @return 结果
     */
    @Override
    public int insertFrontVideo(FrontVideo frontVideo) throws JsonProcessingException {
        frontVideo.setCreateTime(DateUtils.getNowDate());
        frontVideo.setUrl(ossUrlCleanerUtil.cleanUrlsToString(frontVideo.getUrl()));
        frontVideo.setCover(ossUrlCleanerUtil.cleanUrlsToString(frontVideo.getCover()));
        String step = frontVideo.getStep();
        if (step != null) {
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, Object>> list = mapper.readValue(step, new TypeReference<List<Map<String, Object>>>() {});

            for (Map<String, Object> item : list) {
                String coverUrl = (String) item.get("cover");
                item.put("cover", ossUrlCleanerUtil.cleanUrlsToString(coverUrl));
            }
            String updatedStepJson = mapper.writeValueAsString(list);
            frontVideo.setStep(updatedStepJson);
        }
        return frontVideoMapper.insertFrontVideo(frontVideo);
    }

    /**
     * 修改采集视频
     *
     * @param frontVideo 采集视频
     * @return 结果
     */
    @Override
    public int updateFrontVideo(FrontVideo frontVideo) throws JsonProcessingException {
        frontVideo.setUpdateTime(DateUtils.getNowDate());
        frontVideo.setUrl(ossUrlCleanerUtil.cleanUrlsToString(frontVideo.getUrl()));
        frontVideo.setCover(ossUrlCleanerUtil.cleanUrlsToString(frontVideo.getCover()));
        String step = frontVideo.getStep();
        if (step != null) {
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, Object>> list = mapper.readValue(step, new TypeReference<List<Map<String, Object>>>() {});
            for (Map<String, Object> item : list) {
                String coverUrl = (String) item.get("cover");
                item.put("cover", ossUrlCleanerUtil.cleanUrlsToString(coverUrl));
            }
            String updatedStepJson = mapper.writeValueAsString(list);
            frontVideo.setStep(updatedStepJson);
        }
        return frontVideoMapper.updateFrontVideo(frontVideo);
    }

    /**
     * 批量删除采集视频
     *
     * @param ids 需要删除的采集视频主键
     * @return 结果
     */
    @Override
    public int deleteFrontVideoByIds(Long[] ids)
    {
        return frontVideoMapper.deleteFrontVideoByIds(ids);
    }

    /**
     * 删除采集视频信息
     *
     * @param id 采集视频主键
     * @return 结果
     */
    @Override
    public int deleteFrontVideoById(Long id)
    {
        return frontVideoMapper.deleteFrontVideoById(id);
    }
}
