package com.ruoyi.gather.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.gather.service.IFrontFeedbackService;
import com.ruoyi.system.api.domain.FrontFeedback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 意见反馈Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/feedback")
public class FrontFeedbackController extends BaseController
{
    @Autowired
    private IFrontFeedbackService frontFeedbackService;

    /**
     * 查询意见反馈列表
     */
    @RequiresPermissions("system:feedback:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontFeedback frontFeedback)
    {
        startPage();
        List<FrontFeedback> list = frontFeedbackService.selectFrontFeedbackList(frontFeedback);
        return getDataTable(list);
    }

    /**
     * 导出意见反馈列表
     */
    @RequiresPermissions("system:feedback:export")
    @Log(title = "意见反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontFeedback frontFeedback)
    {
        List<FrontFeedback> list = frontFeedbackService.selectFrontFeedbackList(frontFeedback);
        ExcelUtil<FrontFeedback> util = new ExcelUtil<FrontFeedback>(FrontFeedback.class);
        util.exportExcel(response, list, "意见反馈数据");
    }

    /**
     * 获取意见反馈详细信息
     */
    @RequiresPermissions("system:feedback:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontFeedbackService.selectFrontFeedbackById(id));
    }

    /**
     * 新增意见反馈
     */
    @RequiresPermissions("system:feedback:add")
    @Log(title = "意见反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontFeedback frontFeedback)
    {
        return toAjax(frontFeedbackService.insertFrontFeedback(frontFeedback));
    }

    /**
     * 修改意见反馈
     */
    @RequiresPermissions("system:feedback:edit")
    @Log(title = "意见反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontFeedback frontFeedback)
    {
        return toAjax(frontFeedbackService.updateFrontFeedback(frontFeedback));
    }

    /**
     * 删除意见反馈
     */
    @RequiresPermissions("system:feedback:remove")
    @Log(title = "意见反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontFeedbackService.deleteFrontFeedbackByIds(ids));
    }
}
