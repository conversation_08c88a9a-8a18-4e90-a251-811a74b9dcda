package com.ruoyi.gather.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.gather.service.IFrontFeedbackService;
import com.ruoyi.system.api.domain.FrontFeedback;
import com.ruoyi.system.api.mapper.FrontFeedbackMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 意见反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class FrontFeedbackServiceImpl implements IFrontFeedbackService
{
    @Autowired
    private FrontFeedbackMapper frontFeedbackMapper;

    /**
     * 查询意见反馈
     *
     * @param id 意见反馈主键
     * @return 意见反馈
     */
    @Override
    public FrontFeedback selectFrontFeedbackById(Long id)
    {
        return frontFeedbackMapper.selectFrontFeedbackById(id);
    }

    /**
     * 查询意见反馈列表
     *
     * @param frontFeedback 意见反馈
     * @return 意见反馈
     */
    @Override
    public List<FrontFeedback> selectFrontFeedbackList(FrontFeedback frontFeedback)
    {

        return frontFeedbackMapper.selectFrontFeedbackList(frontFeedback);
    }

    /**
     * 新增意见反馈
     *
     * @param frontFeedback 意见反馈
     * @return 结果
     */
    @Override
    public int insertFrontFeedback(FrontFeedback frontFeedback)
    {
        frontFeedback.setCreateTime(DateUtils.getNowDate());
        return frontFeedbackMapper.insertFrontFeedback(frontFeedback);
    }

    /**
     * 修改意见反馈
     *
     * @param frontFeedback 意见反馈
     * @return 结果
     */
    @Override
    public int updateFrontFeedback(FrontFeedback frontFeedback)
    {
        return frontFeedbackMapper.updateFrontFeedback(frontFeedback);
    }

    /**
     * 批量删除意见反馈
     *
     * @param ids 需要删除的意见反馈主键
     * @return 结果
     */
    @Override
    public int deleteFrontFeedbackByIds(Long[] ids)
    {
        return frontFeedbackMapper.deleteFrontFeedbackByIds(ids);
    }

    /**
     * 删除意见反馈信息
     *
     * @param id 意见反馈主键
     * @return 结果
     */
    @Override
    public int deleteFrontFeedbackById(Long id)
    {
        return frontFeedbackMapper.deleteFrontFeedbackById(id);
    }
}
