package com.ruoyi.gather.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.gather.service.IFrontVideoService;
import com.ruoyi.system.api.domain.FrontVideo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 采集视频Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/video")
public class FrontVideoController extends BaseController
{
    @Autowired
    private IFrontVideoService frontVideoService;

    /**
     * 查询采集视频列表
     */
    @RequiresPermissions("system:video:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontVideo frontVideo)
    {
        startPage();
        List<FrontVideo> list = frontVideoService.selectFrontVideoList(frontVideo);
        return getDataTable(list);
    }

    /**
     * 导出采集视频列表
     */
    @RequiresPermissions("system:video:export")
    @Log(title = "采集视频", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontVideo frontVideo)
    {
        List<FrontVideo> list = frontVideoService.selectFrontVideoList(frontVideo);
        ExcelUtil<FrontVideo> util = new ExcelUtil<FrontVideo>(FrontVideo.class);
        util.exportExcel(response, list, "采集视频数据");
    }

    /**
     * 获取采集视频详细信息
     */
    @RequiresPermissions("system:video:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) throws JsonProcessingException {
        return success(frontVideoService.selectFrontVideoById(id));
    }

    /**
     * 新增采集视频
     */
    @RequiresPermissions("system:video:add")
    @Log(title = "采集视频", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontVideo frontVideo) throws JsonProcessingException {
        return toAjax(frontVideoService.insertFrontVideo(frontVideo));
    }

    /**
     * 修改采集视频
     */
    @RequiresPermissions("system:video:edit")
    @Log(title = "采集视频", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontVideo frontVideo) throws JsonProcessingException {
        return toAjax(frontVideoService.updateFrontVideo(frontVideo));
    }

    /**
     * 删除采集视频
     */
    @RequiresPermissions("system:video:remove")
    @Log(title = "采集视频", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontVideoService.deleteFrontVideoByIds(ids));
    }
}
