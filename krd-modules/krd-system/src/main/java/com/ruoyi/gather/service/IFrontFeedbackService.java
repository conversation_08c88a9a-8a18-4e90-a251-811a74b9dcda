package com.ruoyi.gather.service;

import com.ruoyi.system.api.domain.FrontFeedback;

import java.util.List;

/**
 * 意见反馈Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface IFrontFeedbackService
{
    /**
     * 查询意见反馈
     *
     * @param id 意见反馈主键
     * @return 意见反馈
     */
    public FrontFeedback selectFrontFeedbackById(Long id);

    /**
     * 查询意见反馈列表
     *
     * @param frontFeedback 意见反馈
     * @return 意见反馈集合
     */
    public List<FrontFeedback> selectFrontFeedbackList(FrontFeedback frontFeedback);

    /**
     * 新增意见反馈
     *
     * @param frontFeedback 意见反馈
     * @return 结果
     */
    public int insertFrontFeedback(FrontFeedback frontFeedback);

    /**
     * 修改意见反馈
     *
     * @param frontFeedback 意见反馈
     * @return 结果
     */
    public int updateFrontFeedback(FrontFeedback frontFeedback);

    /**
     * 批量删除意见反馈
     *
     * @param ids 需要删除的意见反馈主键集合
     * @return 结果
     */
    public int deleteFrontFeedbackByIds(Long[] ids);

    /**
     * 删除意见反馈信息
     *
     * @param id 意见反馈主键
     * @return 结果
     */
    public int deleteFrontFeedbackById(Long id);
}
