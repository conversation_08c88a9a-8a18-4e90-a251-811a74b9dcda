package com.ruoyi.gather.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.gather.service.IFrontSysDocService;
import com.ruoyi.system.api.domain.FrontSysDoc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文档管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/doc")
public class FrontSysDocController extends BaseController
{
    @Autowired
    private IFrontSysDocService frontSysDocService;

    /**
     * 查询文档管理列表
     */
    @RequiresPermissions("system:doc:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontSysDoc frontSysDoc)
    {
        startPage();
        List<FrontSysDoc> list = frontSysDocService.selectFrontSysDocList(frontSysDoc);
        return getDataTable(list);
    }

    /**
     * 导出文档管理列表
     */
    @RequiresPermissions("system:doc:export")
    @Log(title = "文档管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontSysDoc frontSysDoc)
    {
        List<FrontSysDoc> list = frontSysDocService.selectFrontSysDocList(frontSysDoc);
        ExcelUtil<FrontSysDoc> util = new ExcelUtil<FrontSysDoc>(FrontSysDoc.class);
        util.exportExcel(response, list, "文档管理数据");
    }

    /**
     * 获取文档管理详细信息
     */
    @RequiresPermissions("system:doc:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontSysDocService.selectFrontSysDocById(id));
    }

    /**
     * 新增文档管理
     */
    @RequiresPermissions("system:doc:add")
    @Log(title = "文档管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontSysDoc frontSysDoc)
    {
        return toAjax(frontSysDocService.insertFrontSysDoc(frontSysDoc));
    }

    /**
     * 修改文档管理
     */
    @RequiresPermissions("system:doc:edit")
    @Log(title = "文档管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontSysDoc frontSysDoc)
    {
        return toAjax(frontSysDocService.updateFrontSysDoc(frontSysDoc));
    }

    /**
     * 删除文档管理
     */
    @RequiresPermissions("system:doc:remove")
    @Log(title = "文档管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontSysDocService.deleteFrontSysDocByIds(ids));
    }
}
