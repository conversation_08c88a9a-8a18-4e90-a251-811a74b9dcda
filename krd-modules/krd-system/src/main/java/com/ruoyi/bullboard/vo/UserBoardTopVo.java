package com.ruoyi.bullboard.vo;

import lombok.Data;

/**
 * @Author: suhai
 * @Date: 2025/6/26
 * @Description: 用户数据看板头部列表信息
 */
@Data
public class UserBoardTopVo {

    //今日新增用户数
    private Integer todayNewsUserCount;

    //相比昨日趋势百分比
    private String todayNewsUserCountPercent;

    //昨日新增用户数
    private Integer yesterdayNewsUserCount;

    //7日新增用户数
    private Integer sevenDaysNewsUserCount;

    //相比7日趋势百分比
    private String sevenDaysNewsUserCountPercent;

    //30日新增用户数
    private Integer thirtyDaysNewsUserCount;

    //相比30日趋势百分比
    private String thirtyDaysNewsUserCountPercent;

    //一年新增用户数
    private Integer oneYearNewsUserCount;

    //相比一年趋势百分比
    private String oneYearNewsUserCountPercent;
}
