package com.ruoyi.bullboard.controller;

import com.ruoyi.bullboard.vo.UserAccountVo;
import com.ruoyi.bullboard.vo.UserAgeDistributionVo;
import com.ruoyi.bullboard.vo.UserDataInfoVo;
import com.ruoyi.bullboard.vo.UserRegionDistributionVo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.user.mapper.FrontUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据看板模块---用户数据
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("/board/user")
public class UserBoardController extends BaseController
{
    @Autowired
    private FrontUserMapper frontUserMapper;

    /**
     * 获取用户性别占比
     * @param type 0-新增用户占比 1-活跃用户占比 2-累计用户占比
     * @return 用户性别占比数据
     */
    @GetMapping("/getUserAccount")
    public R<UserAccountVo> getUserAccount(@RequestParam("type") Integer type) {
        UserAccountVo userAccount = frontUserMapper.getUserAccount(type);
        return R.ok(userAccount);
    }

    /**
     * 获取用户使用数据（按半天统计）
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 用户使用数据，每天包含上午(AM)和下午(PM)两个数据点，统计新用户和老用户数据
     */
    @GetMapping("/getUserDataInfo")
    public R<List<UserDataInfoVo>> getUserDataInfo(@RequestParam("startDate") String startDate,
                                                   @RequestParam("endDate") String endDate) {
        List<UserDataInfoVo> userDataInfo = frontUserMapper.getUserDataInfo(startDate, endDate);
        return R.ok(userDataInfo);
    }

    /**
     * 获取用户年龄分布
     * @param type 0-新增用户年龄分布 1-活跃用户年龄分布 2-累计用户年龄分布
     * @return 用户年龄分布数据
     */
    @GetMapping("/getUserAgeDistribution")
    public R<List<UserAgeDistributionVo>> getUserAgeDistribution(@RequestParam("type") Integer type) {
        List<UserAgeDistributionVo> ageDistribution = frontUserMapper.getUserAgeDistribution(type);
        return R.ok(ageDistribution);
    }

    /**
     * 获取用户地域分布（TOP 10）
     * @param type 0-新增用户地域分布 1-活跃用户地域分布 2-累计用户地域分布
     * @return 用户地域分布数据，返回TOP 10城市
     */
    @GetMapping("/getUserRegionDistribution")
    public R<List<UserRegionDistributionVo>> getUserRegionDistribution(@RequestParam("type") Integer type) {
        List<UserRegionDistributionVo> regionDistribution = frontUserMapper.getUserRegionDistribution(type);
        return R.ok(regionDistribution);
    }
}
