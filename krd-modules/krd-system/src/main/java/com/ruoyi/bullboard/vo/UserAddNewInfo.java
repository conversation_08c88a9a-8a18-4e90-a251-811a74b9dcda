package com.ruoyi.bullboard.vo;

import lombok.Data;

/**
 * @Author: suhai
 * @Date: 2025/6/26
 * @Description:
 */
@Data
public class UserAddNewInfo {

    //新增用户
    private int newUser;
    // 去年用户
    private int lastYearUser;
    // 同比增长
    private double yearOnYearGrowth;
    // 活跃用户
    private int activeUser;
    //去年活跃用户
    private int lastYearActiveUser;
    // 同比增长
    private double activeGrowth;
    //累计用户
    private int totalUser;
    //去年同期用户
    private int lastYearTotalUser;
    //同比增长
    private double totalGrowth;
}
