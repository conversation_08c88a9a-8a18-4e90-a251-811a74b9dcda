package com.ruoyi.store.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontGategory;
import com.ruoyi.store.domain.vo.FrontGategoryVo;
import com.ruoyi.store.service.IFrontGategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName FrontGategoryController
 * @Description 后台商品分类业务控制器
 * <AUTHOR>
 * @Date 2025/5/20 上午11:54
 */
@Slf4j
@Tag(name = "FrontGategoryController类", description = "后台商品分类业务控制器")
@RestController
@RequestMapping("/frontGategory")
public class FrontGategoryController extends BaseController {

    @Autowired
    private IFrontGategoryService frontGategoryService;

    @Operation(description = "商品分类详情")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontGategoryService.getGategoryById(id));
    }

    @RequiresPermissions("front:gategory:list")
    @Operation(description = "商品分类树")
    @GetMapping("/gategoryTree")
    public AjaxResult gategoryTree(FrontGategoryVo.FrontGategorySearch vo)
    {
        return success(frontGategoryService.selectGategoryTreeList(vo));
    }

    @Operation(description = "全部商品顶级分类列表")
    @GetMapping("/all")
    public AjaxResult all()
    {
        return success(frontGategoryService.getAllTopGategory());
    }

    @Operation(description = "分类下级列表")
    @GetMapping("/children")
    public AjaxResult children()
    {
        return success(frontGategoryService.getChildrenList());
    }

    @RequiresPermissions("front:gategory:list")
    @Operation(description = "商品分类列表")
    @GetMapping("/list")
    public TableDataInfo list(FrontGategoryVo.FrontGategorySearch vo)
    {
        startPage();
        return getDataTable(frontGategoryService.getGategoryList(vo));
    }

    @RequiresPermissions("front:gategory:add")
    @Operation(description = "新增商品分类")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FrontGategory frontGategory)
    {
        return toAjax(frontGategoryService.addGategory(frontGategory));
    }

    @RequiresPermissions("front:gategory:update")
    @Operation(description = "修改商品分类")
    @PostMapping("/update")
    public AjaxResult update(@RequestBody FrontGategory frontGategory)
    {
        return toAjax(frontGategoryService.updateGategory(frontGategory));
    }

    @RequiresPermissions("front:gategory:delete")
    @Operation(description = "删除商品分类")
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long[] ids)
    {
        frontGategoryService.deleteGategory(ids);
        return success();
    }

    @RequiresPermissions("front:gategory:goods")
    @Operation(description = "移除分类下对应所有的商品")
    @PostMapping("/deleteGategoryGoods/{id}")
    public AjaxResult deleteGategoryGoods(@PathVariable("id") Long id)
    {
        return toAjax(frontGategoryService.deleteGategoryGoods(id));
    }

    @RequiresPermissions("front:gategory:sort")
    @Operation(description = "修改分类排序")
    @PostMapping("/updateGategorySort")
    public AjaxResult updateGategorySort(@RequestBody FrontGategoryVo.FrontGategorySort vo)
    {
        return toAjax(frontGategoryService.updateGategorySort(vo));
    }

    @RequiresPermissions("front:gategory:status")
    @Operation(description = "修改分类状态")
    @PostMapping("/updateGategoryStatus")
    public AjaxResult updateGategoryStatus(@RequestBody FrontGategoryVo.FrontGategoryStatus vo)
    {
        return toAjax(frontGategoryService.updateGategoryStatus(vo));
    }

}
