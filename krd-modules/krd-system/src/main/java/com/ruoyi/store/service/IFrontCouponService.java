package com.ruoyi.store.service;

import java.util.List;

import com.ruoyi.system.api.domain.FrontCouponInfo;
import com.ruoyi.system.api.domain.StoreFrontCoupon;
import com.ruoyi.system.api.domain.vo.FrontCouponVo;

/**
 * 优惠券Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IFrontCouponService 
{
    /**
     * 查询优惠券
     * 
     * @param id 优惠券主键
     * @return 优惠券
     */
    public StoreFrontCoupon selectFrontCouponById(Long id);

    /**
     * 查询优惠券详情
     * @param id
     * @return
     */
    FrontCouponVo.FrontCouponDetail selectFrontCouponDetailById(Long id);

    /**
     * 查询优惠券对应详情明细
     * @param vo
     * @return
     */
    List<FrontCouponInfo> selectFrontCouponInfoById(FrontCouponVo.FrontCouponDetailSearch vo);

    /**
     * 查询优惠券列表
     * 
     * @param vo 查询参数
     * @return 优惠券集合
     */
    public List<FrontCouponVo.FrontCouponList> selectFrontCouponList(FrontCouponVo.FrontCouponListSearch vo);

    /**
     * 新增优惠券
     * 
     * @param frontCoupon 优惠券
     * @return 结果
     */
    public int insertFrontCoupon(StoreFrontCoupon frontCoupon);

    /**
     * 修改优惠券
     * 
     * @param frontCoupon 优惠券
     * @return 结果
     */
    public int updateFrontCoupon(StoreFrontCoupon frontCoupon);

    /**
     * 批量删除优惠券
     * 
     * @param ids 需要删除的优惠券主键集合
     * @return 结果
     */
    public int deleteFrontCouponByIds(Long[] ids);

    /**
     * 删除优惠券信息
     * 
     * @param id 优惠券主键
     * @return 结果
     */
    public int deleteFrontCouponById(Long id);

    /**
     * 优惠券上下架
     *
     * @param id 优惠券主键
     *           0-下架 1-上架
     * @return 结果
     */
    Boolean updateFrontCouponStatus(Long id);
}
