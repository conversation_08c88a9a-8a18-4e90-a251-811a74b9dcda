package com.ruoyi.store.service;

import com.ruoyi.store.domain.res.StoreOrderRefundRequest;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.FrontOrdersInvoice;
import com.ruoyi.system.api.domain.vo.FrontOrdersVo;

import java.util.List;

/**
 * 订单总Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IFrontSysOrdersService
{
    /**
     * 查询订单总
     *
     * @param id 订单总主键
     * @return 订单总
     */
    public FrontOrders selectFrontOrdersById(Long id);


    /**
     * 查询订单总列表
     *
     * @param vo 订单查询参数
     * @return 订单总集合
     */
    public List<FrontOrders> selectFrontOrdersList(FrontOrdersVo.FrontOrdersSearch vo);

    /**
     * 新增订单总
     *
     * @param frontOrders 订单总
     * @return 结果
     */
    public int insertFrontOrders(FrontOrders frontOrders);

    /**
     * 修改订单总
     *
     * @param frontOrders 订单总
     * @return 结果
     */
    public int updateFrontOrders(FrontOrders frontOrders);

    /**
     * 批量删除订单总
     *
     * @param ids 需要删除的订单总主键集合
     * @return 结果
     */
    public int deleteFrontOrdersByIds(Long[] ids);

    /**
     * 删除订单总信息
     *
     * @param id 订单总主键
     * @return 结果
     */
    public int deleteFrontOrdersById(Long id);

    /**
     * 根据订单编号查询
     * @param orderNum
     * @return
     */
    FrontOrdersVo.FrontOrdersDetail selectOrderNum(String orderNum);

    /**
     * 根据订单编号查询发货信息
     * @param orderNum
     * @return
     */
    FrontOrdersVo.FrontOrdersDeliveryInfo selectOrderDelivery(String orderNum);

    /**
     * 修改订单收货信息
     * @param vo
     */
    Boolean updateOrderDelivery(FrontOrdersVo.FrontOrdersReceiveUpdateInfo vo);

    /**
     * 修改订单备注
     * @param vo
     */
    Boolean updateOrderRemark(FrontOrdersVo.FrontOrdersRemarkUpdateInfo vo);

    /**
     * 修改发票信息
     * @param frontOrdersInvoice
     */
    Boolean updateOrderInvoice(FrontOrdersInvoice frontOrdersInvoice);

    //修改订单状态 关闭订单用
    Boolean updateOrderStatus(Long id);

    //退款
    boolean refund(StoreOrderRefundRequest request);
}
