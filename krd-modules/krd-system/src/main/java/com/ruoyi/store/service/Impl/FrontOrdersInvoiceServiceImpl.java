package com.ruoyi.store.service.Impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.oss.OssUrlCleanerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.api.mapper.FrontOrdersInvoiceMapper;
import com.ruoyi.system.api.domain.FrontOrdersInvoice;
import com.ruoyi.store.service.IFrontOrdersInvoiceService;

/**
 * 订单-发票信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Service
public class FrontOrdersInvoiceServiceImpl implements IFrontOrdersInvoiceService
{
    @Autowired
    private FrontOrdersInvoiceMapper frontOrdersInvoiceMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    /**
     * 查询订单-发票信息
     *
     * @param id 订单-发票信息主键
     * @return 订单-发票信息
     */
    @Override
    public FrontOrdersInvoice selectFrontOrdersInvoiceById(Long id)
    {
        FrontOrdersInvoice frontOrdersInvoice = frontOrdersInvoiceMapper.selectFrontOrdersInvoiceById(id);
        if (StringUtils.isNotEmpty(frontOrdersInvoice.getUrl())){
            frontOrdersInvoice.setUrl(ossUrlCleanerUtil.getSignatureUrl(frontOrdersInvoice.getUrl()));
        }
        return frontOrdersInvoice;
    }

    /**
     * 查询订单-发票信息列表
     *
     * @param frontOrdersInvoice 订单-发票信息
     * @return 订单-发票信息
     */
    @Override
    public List<FrontOrdersInvoice> selectFrontOrdersInvoiceList(FrontOrdersInvoice frontOrdersInvoice)
    {
        return frontOrdersInvoiceMapper.selectFrontOrdersInvoiceList(frontOrdersInvoice);
    }

    /**
     * 新增订单-发票信息
     *
     * @param frontOrdersInvoice 订单-发票信息
     * @return 结果
     */
    @Override
    public int insertFrontOrdersInvoice(FrontOrdersInvoice frontOrdersInvoice)
    {
        frontOrdersInvoice.setCreateTime(DateUtils.getLocalDateTime());
        return frontOrdersInvoiceMapper.insertFrontOrdersInvoice(frontOrdersInvoice);
    }

    /**
     * 修改订单-发票信息
     *
     * @param frontOrdersInvoice 订单-发票信息
     * @return 结果
     */
    @Override
    public int updateFrontOrdersInvoice(FrontOrdersInvoice frontOrdersInvoice)
    {
        if (StringUtils.isNotEmpty(frontOrdersInvoice.getUrl())){
            frontOrdersInvoice.setUrl(ossUrlCleanerUtil.cleanUrlsToString(frontOrdersInvoice.getUrl()));
        }
        frontOrdersInvoice.setUpdateTime(DateUtils.getLocalDateTime());
        return frontOrdersInvoiceMapper.updateFrontOrdersInvoice(frontOrdersInvoice);
    }

    /**
     * 批量删除订单-发票信息
     *
     * @param ids 需要删除的订单-发票信息主键
     * @return 结果
     */
    @Override
    public int deleteFrontOrdersInvoiceByIds(Long[] ids)
    {
        return frontOrdersInvoiceMapper.deleteFrontOrdersInvoiceByIds(ids);
    }

    /**
     * 删除订单-发票信息信息
     *
     * @param id 订单-发票信息主键
     * @return 结果
     */
    @Override
    public int deleteFrontOrdersInvoiceById(Long id)
    {
        return frontOrdersInvoiceMapper.deleteFrontOrdersInvoiceById(id);
    }
}
