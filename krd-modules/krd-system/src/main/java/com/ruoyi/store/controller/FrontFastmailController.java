package com.ruoyi.store.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.store.service.IFrontFastmailService;
import com.ruoyi.system.api.domain.FrontFastmail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 快递单管理Controller
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@RestController
@RequestMapping("/fastmail")
public class FrontFastmailController extends BaseController
{
    @Autowired
    private IFrontFastmailService frontFastmailService;

    /**
     * 查询快递单管理列表
     */
    @RequiresPermissions("system:fastmail:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontFastmail frontFastmail)
    {
        startPage();
        List<FrontFastmail> list = frontFastmailService.selectFrontFastmailList(frontFastmail);
        return getDataTable(list);
    }

    /**
     * 导出快递单管理列表
     */
    @RequiresPermissions("system:fastmail:export")
    @Log(title = "快递单管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontFastmail frontFastmail)
    {
        List<FrontFastmail> list = frontFastmailService.selectFrontFastmailList(frontFastmail);
        ExcelUtil<FrontFastmail> util = new ExcelUtil<FrontFastmail>(FrontFastmail.class);
        util.exportExcel(response, list, "快递单管理数据");
    }

    /**
     * 获取快递单管理详细信息
     */
    @RequiresPermissions("system:fastmail:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontFastmailService.selectFrontFastmailById(id));
    }

    /**
     * 新增快递单管理
     */
    @RequiresPermissions("system:fastmail:add")
    @Log(title = "快递单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontFastmail frontFastmail)
    {
        return toAjax(frontFastmailService.insertFrontFastmail(frontFastmail));
    }

    /**
     * 修改快递单管理
     */
    @RequiresPermissions("system:fastmail:edit")
    @Log(title = "快递单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontFastmail frontFastmail)
    {
        return toAjax(frontFastmailService.updateFrontFastmail(frontFastmail));
    }

    /**
     * 删除快递单管理
     */
    @RequiresPermissions("system:fastmail:remove")
    @Log(title = "快递单管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontFastmailService.deleteFrontFastmailByIds(ids));
    }

    /***
     * 修改快递状态完成
     */
    @RequiresPermissions("system:fastmail:update")
    @Log(title = "快递单管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus/{id}")
    public AjaxResult updateStatus(@PathVariable("id") Long id){
        return toAjax(frontFastmailService.updateFrontFastmailStatus(id));
    }
}
