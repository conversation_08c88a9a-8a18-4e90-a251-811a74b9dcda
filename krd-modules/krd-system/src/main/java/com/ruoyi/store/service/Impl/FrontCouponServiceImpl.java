package com.ruoyi.store.service.Impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.FrontCouponInfo;
import com.ruoyi.system.api.domain.StoreFrontCoupon;
import com.ruoyi.system.api.domain.vo.FrontCouponVo;
import com.ruoyi.system.api.mapper.FrontCouponInfoMapper;
import com.ruoyi.system.api.mapper.FrontCouponMapper;
import com.ruoyi.store.service.IFrontCouponService;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 优惠券Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class FrontCouponServiceImpl implements IFrontCouponService
{
    @Autowired
    private FrontCouponMapper frontCouponMapper;

    @Autowired
    private FrontCouponInfoMapper frontCouponInfoMapper;

    @Autowired
    private FrontUserMapper frontUserMapper;

    /**
     * 查询优惠券
     *
     * @param id 优惠券主键
     * @return 优惠券
     */
    @Override
    public StoreFrontCoupon selectFrontCouponById(Long id)
    {
        return frontCouponMapper.selectFrontCouponById(id);
    }

    @Override
    public FrontCouponVo.FrontCouponDetail selectFrontCouponDetailById(Long id) {
        FrontCouponVo.FrontCouponDetail frontCouponDetail = frontCouponMapper.selectFrontCouponDetailById(id);
        String[] split = frontCouponDetail.getExpirationDate().split(",");
        // 判断是否为时间数组
        if (split.length == 1){
            if (!"0".equals(frontCouponDetail.getExpirationDate())){
                // 创建年月日+天数
                // 获取创建时间年月日
                LocalDateTime createTime = frontCouponDetail.getCreateTime();
                LocalDateTime plusDays = createTime.plusDays(Integer.parseInt(frontCouponDetail.getExpirationDate()));

                //将plusDays + item.getExpirationDate
                LocalDate date = plusDays.toLocalDate();
                LocalDate localDate = date.plusDays(Integer.parseInt(frontCouponDetail.getExpirationDate()));

                long between = date.until(localDate, java.time.temporal.ChronoUnit.DAYS);

                frontCouponDetail.setExpirationDate(String.valueOf(between));
                frontCouponDetail.setTime(date + "至" + localDate);

                // 通过开始时间和结束时间 判断优惠券是否过期
                if (LocalDateTime.now().isAfter(date.atStartOfDay()) && LocalDateTime.now().isBefore(localDate.atStartOfDay())) {
                    frontCouponDetail.setStatus("有效");
                } else if (LocalDateTime.now().isAfter(localDate.atStartOfDay())){
                    frontCouponDetail.setStatus("无效");
                } else {
                    frontCouponDetail.setStatus("无效");
                }
            }
        }else {
            frontCouponDetail.setTime(split[0] + "至" + split[1]);
            // 计算出开始结束时间中间存在多少天
            long between = LocalDate.parse(split[0]).until(LocalDate.parse(split[1]), java.time.temporal.ChronoUnit.DAYS);
            frontCouponDetail.setExpirationDate(String.valueOf(between));

            // 通过开始时间和结束时间 判断优惠券是否过期
            if (LocalDate.now().isAfter(LocalDate.parse(split[0])) && LocalDate.now().isBefore(LocalDate.parse(split[1]))) {
                frontCouponDetail.setStatus("有效");
            } else if (LocalDate.now().isAfter(LocalDate.parse(split[1]))){
                frontCouponDetail.setStatus("无效");
            } else {
                frontCouponDetail.setStatus("无效");
            }
        }
        // 使用门榄
        if (!"0".equals(frontCouponDetail.getType())){
            frontCouponDetail.setThreshold("满" + frontCouponDetail.getThreshold() + "减" + frontCouponDetail.getBalance() + "元");
        }
        return frontCouponDetail;
    }

    @Override
    public List<FrontCouponInfo> selectFrontCouponInfoById(FrontCouponVo.FrontCouponDetailSearch vo) {
        LambdaQueryWrapper<FrontCouponInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontCouponInfo::getCouponId, vo.getId());
        if (vo.getKeyword() != null){
            queryWrapper.like(FrontCouponInfo::getUserId, vo.getKeyword())
                    .or()
                    .like(FrontCouponInfo::getOrderNumber, vo.getKeyword());
        }
        List<FrontCouponInfo> frontCouponInfos = frontCouponInfoMapper.selectList(queryWrapper);
        frontCouponInfos.forEach(item -> {
            // 获取用户信息
            item.setPhone(frontUserMapper.selectFrontUserById(item.getUserId()).getUserMobile());
            //获取优惠券名称
            item.setName(frontCouponMapper.selectFrontCouponById(item.getCouponId()).getTitle());
        });

        return frontCouponInfos;
    }

    /**
     * 查询优惠券列表
     *
     * @param vo  查询参数 优惠券
     * @return 优惠券
     */
    @Override
    public List<FrontCouponVo.FrontCouponList> selectFrontCouponList(FrontCouponVo.FrontCouponListSearch vo)
    {
        List<FrontCouponVo.FrontCouponList> frontCouponLists = frontCouponMapper.selectFrontCouponList(vo);
        frontCouponLists.forEach(item -> {
            String[] split = item.getExpirationDate().split(",");
            // 判断是否为时间数组
            if (split.length == 1){
                if (StringUtils.isNotEmpty(item.getExpirationDate())){
                    // 创建年月日+天数
                    // 获取创建时间年月日
                    LocalDateTime createTime = item.getCreateTime();
                    LocalDateTime plusDays = createTime.plusDays(Integer.parseInt(item.getExpirationDate()));

                    //将plusDays + item.getExpirationDate
                    LocalDate date = plusDays.toLocalDate();
                    LocalDate localDate = date.plusDays(Integer.parseInt(item.getExpirationDate()));

                    long between = date.until(localDate, java.time.temporal.ChronoUnit.DAYS);

                    item.setExpirationDate(String.valueOf(between));
                    item.setTime(date + "至" + localDate);

                    // 通过开始时间和结束时间 判断优惠券是否过期
                    if (LocalDateTime.now().isAfter(date.atStartOfDay()) && LocalDateTime.now().isBefore(localDate.atStartOfDay())) {
                        item.setStatus("有效");
                    } else if (LocalDateTime.now().isAfter(localDate.atStartOfDay())){
                        item.setStatus("无效");
                    } else {
                        item.setStatus("无效");
                    }
                }else {
                    item.setExpirationDate("无限制");
                    item.setTime("无限制");
                    item.setStatus("有效");
                }
            }else {
                item.setTime(split[0] + "至" + split[1]);
                // 计算出开始结束时间中间存在多少天
                long between = LocalDate.parse(split[0]).until(LocalDate.parse(split[1]), java.time.temporal.ChronoUnit.DAYS);
                item.setExpirationDate(String.valueOf(between));

                // 通过开始时间和结束时间 判断优惠券是否过期
                if (LocalDate.now().isAfter(LocalDate.parse(split[0])) && LocalDate.now().isBefore(LocalDate.parse(split[1]))) {
                    item.setStatus("有效");
                } else if (LocalDate.now().isAfter(LocalDate.parse(split[1]))){
                    item.setStatus("无效");
                } else {
                    item.setStatus("无效");
                }
            }
            // 使用门榄
            if (!"0".equals(item.getThreshold())){
                item.setThreshold("满" + item.getThreshold() + "减" + item.getBalance() + "元");
            }else {
                item.setThreshold("无门槛");
            }
        });
        return frontCouponLists;
    }

    /**
     * 新增优惠券
     *
     * @param frontCoupon 优惠券
     * @return 结果
     */
    @Override
    public int insertFrontCoupon(StoreFrontCoupon frontCoupon)
    {
        frontCoupon.setCreateTime(LocalDateTime.now());
        return frontCouponMapper.insert(frontCoupon);
    }

    /**
     * 修改优惠券
     *
     * @param frontCoupon 优惠券
     * @return 结果
     */
    @Override
    public int updateFrontCoupon(StoreFrontCoupon frontCoupon)
    {
        frontCoupon.setUpdateTime(LocalDateTime.now());
        return frontCouponMapper.updateById(frontCoupon);
    }

    /**
     * 批量删除优惠券
     *
     * @param ids 需要删除的优惠券主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteFrontCouponByIds(Long[] ids)
    {
        return frontCouponMapper.deleteFrontCouponByIds(ids);
    }

    /**
     * 删除优惠券信息
     *
     * @param id 优惠券主键
     * @return 结果
     */
    @Override
    public int deleteFrontCouponById(Long id)
    {
        return frontCouponMapper.deleteFrontCouponById(id);
    }

    @Override
    public Boolean updateFrontCouponStatus(Long id) {
        // 查询当前优惠券详情
        StoreFrontCoupon frontCoupon = frontCouponMapper.selectFrontCouponById(id);
        if (frontCoupon != null){
            frontCoupon.setIsStatus((long) (frontCoupon.getIsStatus() == 0 ? 1 : 0));
            frontCouponMapper.updateById(frontCoupon);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
