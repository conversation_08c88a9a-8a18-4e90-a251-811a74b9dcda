package com.ruoyi.store.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontGoods;
import com.ruoyi.store.domain.vo.FrontGoodsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName FrontGoodsMapper
 * @Description 商品管理Mapper接口
 * <AUTHOR>
 * @Date 2025/5/20 下午2:25
 */
@Mapper
public interface FrontGoodsMapper extends BaseMapper<FrontGoods> {
    /**
     * 查询商品
     *
     * @param id 商品主键
     * @return 商品
     */
    public FrontGoods selectFrontGoodsById(Long id);

    /**
     * 查询商品列表
     *
     * @param vo 商品查询搜索参数
     * @return 商品集合
     */
    public List<FrontGoods> selectFrontGoodsList(@Param("query")FrontGoodsVo.SearchParam vo);

    /**
     * 新增商品
     *
     * @param frontGoods 商品
     * @return 结果
     */
    public int insertFrontGoods(FrontGoods frontGoods);

    /**
     * 修改商品
     *
     * @param frontGoods 商品
     * @return 结果
     */
    public int updateFrontGoods(FrontGoods frontGoods);

    /**
     * 删除商品
     *
     * @param id 商品主键
     * @return 结果
     */
    public int deleteFrontGoodsById(Long id);

    /**
     * 批量删除商品
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontGoodsByIds(Long[] ids);
}
