package com.ruoyi.store.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.store.service.IFrontCouponTypeService;
import com.ruoyi.system.api.domain.FrontCouponType;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2025-05-30
 */
@RestController
@Slf4j
@Tag(name = "优惠券类型")
@RequestMapping("/coupon/type")
public class FrontCouponTypeController extends BaseController
{
    @Autowired
    private IFrontCouponTypeService frontCouponTypeService;

    /**
     * 查询【请填写功能名称】列表
     */
    @RequiresPermissions("system:type:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontCouponType frontCouponType)
    {
        startPage();
        List<FrontCouponType> list = frontCouponTypeService.selectFrontCouponTypeList(frontCouponType);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @RequiresPermissions("system:type:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontCouponType frontCouponType)
    {
        List<FrontCouponType> list = frontCouponTypeService.selectFrontCouponTypeList(frontCouponType);
        ExcelUtil<FrontCouponType> util = new ExcelUtil<FrontCouponType>(FrontCouponType.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @RequiresPermissions("system:type:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontCouponTypeService.selectFrontCouponTypeById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @RequiresPermissions("system:type:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FrontCouponType frontCouponType)
    {
        return toAjax(frontCouponTypeService.insertFrontCouponType(frontCouponType));
    }

    /**
     * 修改【请填写功能名称】
     */
    @RequiresPermissions("system:type:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontCouponType frontCouponType)
    {
        return toAjax(frontCouponTypeService.updateFrontCouponType(frontCouponType));
    }

    /**
     * 删除【请填写功能名称】
     */
    @RequiresPermissions("system:type:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(frontCouponTypeService.deleteFrontCouponTypeById(id));
    }
}
