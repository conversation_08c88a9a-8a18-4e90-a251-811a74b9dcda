package com.ruoyi.store.service.Impl;

import com.ruoyi.system.api.domain.FrontOrdersGoods;
import com.ruoyi.system.api.mapper.FrontOrdersGoodsMapper;
import com.ruoyi.store.service.IFrontOrdersGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单-商品信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class FrontOrdersGoodsServiceImpl implements IFrontOrdersGoodsService
{
    @Autowired
    private FrontOrdersGoodsMapper frontOrdersGoodsMapper;

    /**
     * 查询订单-商品信息
     *
     * @param id 订单-商品信息主键
     * @return 订单-商品信息
     */
    @Override
    public FrontOrdersGoods selectFrontOrdersGoodsById(Long id)
    {
        return frontOrdersGoodsMapper.selectFrontOrdersGoodsById(id);
    }

    /**
     * 查询订单-商品信息列表
     *
     * @param frontOrdersGoods 订单-商品信息
     * @return 订单-商品信息
     */
    @Override
    public List<FrontOrdersGoods> selectFrontOrdersGoodsList(FrontOrdersGoods frontOrdersGoods)
    {
        return frontOrdersGoodsMapper.selectFrontOrdersGoodsList(frontOrdersGoods);
    }

    /**
     * 新增订单-商品信息
     *
     * @param frontOrdersGoods 订单-商品信息
     * @return 结果
     */
    @Override
    public int insertFrontOrdersGoods(FrontOrdersGoods frontOrdersGoods)
    {
        frontOrdersGoods.setCreateTime(LocalDateTime.now());
        return frontOrdersGoodsMapper.insertFrontOrdersGoods(frontOrdersGoods);
    }

    /**
     * 修改订单-商品信息
     *
     * @param frontOrdersGoods 订单-商品信息
     * @return 结果
     */
    @Override
    public int updateFrontOrdersGoods(FrontOrdersGoods frontOrdersGoods)
    {
        frontOrdersGoods.setUpdateTime(LocalDateTime.now());
        return frontOrdersGoodsMapper.updateFrontOrdersGoods(frontOrdersGoods);
    }

    /**
     * 批量删除订单-商品信息
     *
     * @param ids 需要删除的订单-商品信息主键
     * @return 结果
     */
    @Override
    public int deleteFrontOrdersGoodsByIds(Long[] ids)
    {
        return frontOrdersGoodsMapper.deleteFrontOrdersGoodsByIds(ids);
    }

    /**
     * 删除订单-商品信息信息
     *
     * @param id 订单-商品信息主键
     * @return 结果
     */
    @Override
    public int deleteFrontOrdersGoodsById(Long id)
    {
        return frontOrdersGoodsMapper.deleteFrontOrdersGoodsById(id);
    }
}
