package com.ruoyi.store.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontOrdersInvoice;
import com.ruoyi.store.service.IFrontOrdersInvoiceService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 订单-发票信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/invoice")
public class FrontOrdersInvoiceController extends BaseController
{
    @Autowired
    private IFrontOrdersInvoiceService frontOrdersInvoiceService;

    /**
     * 查询订单-发票信息列表
     */
    @RequiresPermissions("system:invoice:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontOrdersInvoice frontOrdersInvoice)
    {
        startPage();
        List<FrontOrdersInvoice> list = frontOrdersInvoiceService.selectFrontOrdersInvoiceList(frontOrdersInvoice);
        return getDataTable(list);
    }

    /**
     * 导出订单-发票信息列表
     */
    @RequiresPermissions("system:invoice:export")
    @Log(title = "订单-发票信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontOrdersInvoice frontOrdersInvoice)
    {
        List<FrontOrdersInvoice> list = frontOrdersInvoiceService.selectFrontOrdersInvoiceList(frontOrdersInvoice);
        ExcelUtil<FrontOrdersInvoice> util = new ExcelUtil<FrontOrdersInvoice>(FrontOrdersInvoice.class);
        util.exportExcel(response, list, "订单-发票信息数据");
    }

    /**
     * 获取订单-发票信息详细信息
     */
    @RequiresPermissions("system:invoice:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontOrdersInvoiceService.selectFrontOrdersInvoiceById(id));
    }

    /**
     * 新增订单-发票信息
     */
    @RequiresPermissions("system:invoice:add")
    @Log(title = "订单-发票信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FrontOrdersInvoice frontOrdersInvoice)
    {
        return toAjax(frontOrdersInvoiceService.insertFrontOrdersInvoice(frontOrdersInvoice));
    }

    /**
     * 修改订单-发票信息
     */
    @RequiresPermissions("system:invoice:edit")
    @Log(title = "订单-发票信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontOrdersInvoice frontOrdersInvoice)
    {
        return toAjax(frontOrdersInvoiceService.updateFrontOrdersInvoice(frontOrdersInvoice));
    }

    /**
     * 删除订单-发票信息
     */
    @RequiresPermissions("system:invoice:remove")
    @Log(title = "订单-发票信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontOrdersInvoiceService.deleteFrontOrdersInvoiceByIds(ids));
    }
}
