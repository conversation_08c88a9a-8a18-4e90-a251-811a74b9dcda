package com.ruoyi.store.domain.res;

import lombok.Data;

/**
 * @ClassName Skus
 * @Description 商品规格列表
 * <AUTHOR>
 * @Date 2025/6/16 下午4:46
 */
@Data
public class Skus {
    /**
     * 商品规格名称
     */
    private String attributes;
    /**
     * 创建时间，格式:yyyy-MM-dd HH:mm:ss
     */
    private String create_time;
    /**
     * 商品id
     */
    private String item_id;
    /**
     * 最新修改时间，格式:yyyy-MM-dd HH:mm:ss
     */
    private String modifiy_time;
    /**
     * 规格单价
     */
    private Number price;
    /**
     * 规格库存
     */
    private Integer quantity;
    /**
     * 第三方规格编号 （不能为空）
     */
    private String sku_id;
    /**
     * 状态,0：已删除，1：在售，2：待售，仓库中
     */
    private Integer status;
}
