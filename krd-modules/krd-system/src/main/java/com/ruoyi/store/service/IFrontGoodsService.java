package com.ruoyi.store.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.check.domain.entity.FrontPackage;
import com.ruoyi.check.domain.vo.PackageListVO;
import com.ruoyi.system.api.domain.FrontGoods;
import com.ruoyi.store.domain.vo.FrontGoodsVo;

/**
 * 商品Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IFrontGoodsService extends IService<FrontGoods>
{
    /**
     * 查询商品
     * 
     * @param id 商品主键
     * @return 商品
     */
    public FrontGoods selectFrontGoodsById(Long id);

    /**
     * 查询商品列表
     * 
     * @param vo 商品搜索查询参数
     * @return 商品集合
     */
    public List<FrontGoods> selectFrontGoodsList(FrontGoodsVo.SearchParam vo);

    /**
     * 新增商品
     * 
     * @param frontGoods 商品
     * @return 结果
     */
    public Boolean insertFrontGoods(FrontGoods frontGoods);

    /**
     * 修改商品
     * 
     * @param frontGoods 商品
     * @return 结果
     */
    public Boolean updateFrontGoods(FrontGoods frontGoods);

    /**
     * 批量删除商品
     * 
     * @param ids 需要删除的商品主键集合
     * @return 结果
     */
    public int deleteFrontGoodsByIds(Long[] ids);

    /**
     * 删除商品信息
     * 
     * @param id 商品主键
     * @return 结果
     */
    public int deleteFrontGoodsById(Long id);

    /**
     * 批量上下架商品
     * @param ids 商品id
     *           0:下架 1:上架
     * @return true/false
     */
    public Boolean updateGoodsStatusByIds(Long[] ids);

    /**
     * 上下架商品
     * @param id 商品id
     *           0:下架 1:上架
     * @return 更改条数
     */
    public int updateGoodsStatusById(Long id);

    /**
     * 修改商品排序
     * @param id 商品id
     * @param sort 排序
     * @return ture/false
     */
    public boolean updateGoodsSortById(Long id,Integer sort);

    /**
     * 生成商品货号
     * @param num
     * @Param id 顶级分类ID
     * @return
     */
    String generateGoodsNo(String num,Long id);

    List<PackageListVO> selectFrontPackageList(FrontPackage frontPackage);
}
