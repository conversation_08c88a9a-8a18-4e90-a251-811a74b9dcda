package com.ruoyi.store.service.Impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.check.domain.entity.FrontPackage;
import com.ruoyi.check.domain.vo.PackageListVO;
import com.ruoyi.check.mapper.FrontPackageMapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PinyinUtils;
import com.ruoyi.store.service.StoreErpService;
import com.ruoyi.system.api.domain.FrontGategory;
import com.ruoyi.system.api.domain.FrontGoodsSpec;
import com.ruoyi.store.domain.vo.FrontGoodsVo;
import com.ruoyi.system.api.mapper.FrontGategoryMapper;
import com.ruoyi.system.api.mapper.FrontGoodsSpecMapper;
import com.ruoyi.store.util.SerialNumberUtil;
import com.ruoyi.system.api.oss.OssUrlCleanerUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.store.mapper.FrontGoodsMapper;
import com.ruoyi.system.api.domain.FrontGoods;
import com.ruoyi.store.service.IFrontGoodsService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 商品Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class FrontGoodsServiceImpl extends ServiceImpl<FrontGoodsMapper, FrontGoods> implements IFrontGoodsService
{
    @Autowired
    private FrontGoodsMapper frontGoodsMapper;

    @Autowired
    private FrontGoodsSpecMapper frontGoodsSpecMapper;

    @Autowired
    private FrontGategoryMapper frontGategoryMapper;

    @Autowired
    private FrontPackageMapper frontPackageMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    @Autowired
    private StoreErpService storeErpService;

    /**
     * 查询商品
     * 
     * @param id 商品主键
     * @return 商品
     */
    @Override
    public FrontGoods selectFrontGoodsById(Long id)
    {
        FrontGoods frontGoods = frontGoodsMapper.selectById(id);
        if (frontGoods.getIndexImage() != null){
            frontGoods.setIndexImage(ossUrlCleanerUtil.getSignatureUrl(frontGoods.getIndexImage()));
        }
        if (frontGoods.getFirstPic() != null){
            frontGoods.setFirstPic(ossUrlCleanerUtil.getSignatureUrl(frontGoods.getFirstPic()));
        }
        if (frontGoods.getBanner() != null){
            frontGoods.setBanner(ossUrlCleanerUtil.getSignatureUrl(frontGoods.getBanner()));
        }
        if (frontGoods.getDetails() != null){
            frontGoods.setDetails(ossUrlCleanerUtil.getSignatureEditor(frontGoods.getDetails()));
        }
        List<FrontGoodsSpec> frontGoodsSpecs = frontGoodsSpecMapper.selectList(new LambdaQueryWrapper<FrontGoodsSpec>().eq(FrontGoodsSpec::getGoodsId, id));
        frontGoodsSpecs.forEach(item ->{
            if (item.getPic() != null){
                item.setPic(ossUrlCleanerUtil.getSignatureUrl(item.getPic()));
            }
        });
        frontGoods.setFrontGoodsSpecList(frontGoodsSpecs);
        return frontGoods;
    }

    /**
     * 查询商品列表
     * 
     * @param vo 商品
     * @return 商品
     */
    @Override
    public List<FrontGoods> selectFrontGoodsList(FrontGoodsVo.SearchParam vo)
    {
        List<FrontGoods> goods = frontGoodsMapper.selectFrontGoodsList(vo);
        goods.forEach(item ->{
            if (item.getGoodsType() == 0){
                item.setCategoryName(frontPackageMapper.selectFrontPackageById(item.getPackageId()).getPakeageName());
            }
            if (item.getGoodsType() == 1){
                item.setCategoryName(frontGategoryMapper.selectById(item.getGategoryId()).getTitle());
            }
            if (item.getIndexImage() != null){
                item.setIndexImage(ossUrlCleanerUtil.getSignatureUrl(item.getIndexImage()));
            }
            if (item.getFirstPic() != null){
                item.setFirstPic(ossUrlCleanerUtil.getSignatureUrl(item.getFirstPic()));
            }
            if (item.getBanner() != null){
                item.setBanner(ossUrlCleanerUtil.getSignatureUrl(item.getBanner()));
            }
        });
        return goods;
    }

    /**
     * 新增商品
     * 
     * @param frontGoods 商品
     * @return 结果
     */
    @Override
    @Transactional
    public Boolean insertFrontGoods(FrontGoods frontGoods)
    {
        // 判断是否为商品 且 货号不为空
        if (frontGoods.getGoodsType() == 1 && StringUtils.isNotBlank(frontGoods.getGoodsNo())){
            if (getGoodsId(frontGoods.getGoodsNo()) != null){
                throw new RuntimeException("商品货号：" + frontGoods.getGoodsNo() + "已存在！");
            }else {
                handleImage(frontGoods);

                frontGoods.setCreateTime(DateUtils.getLocalDateTime());
                frontGoods.setUpdateTime(DateUtils.getLocalDateTime());
                frontGoodsMapper.insert(frontGoods);

                // 批量新增对应的规格
                List<FrontGoodsSpec> frontGoodsSpecList = frontGoods.getFrontGoodsSpecList();
                frontGoodsSpecList.forEach(item ->{
                    item.setGoodsType(1);
                    if (item.getPic() != null){
                        item.setPic(ossUrlCleanerUtil.cleanUrlsToString(item.getPic()));
                    }
                    item.setGoodsId(frontGoods.getId());
                    item.setCreateTime(DateUtils.getLocalDateTime());
                    item.setUpdateTime(DateUtils.getLocalDateTime());
                    frontGoodsSpecMapper.insert(item);
                });
                frontGoods.setFrontGoodsSpecList(frontGoodsSpecList);
            }

            try {
                storeErpService.pushGoodsToErp(frontGoods);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            return Boolean.TRUE;
        }else if (!StringUtils.isNotBlank(frontGoods.getGoodsNo()) && frontGoods.getGoodsType() == 1){//  货号为空 生成货号

            String goodsNo = this.generateGoodsNo(null, frontGoods.getGategoryId());
            frontGoods.setGoodsNo(goodsNo);
            handleImage(frontGoods);

            frontGoods.setCreateTime(DateUtils.getLocalDateTime());
            frontGoods.setUpdateTime(DateUtils.getLocalDateTime());

            // 添加商品
            frontGoodsMapper.insert(frontGoods);

            // 批量新增对应的规格
            List<FrontGoodsSpec> frontGoodsSpecList = frontGoods.getFrontGoodsSpecList();
            frontGoodsSpecList.forEach(item ->{
                item.setGoodsType(1);
                if (item.getPic() != null){
                    item.setPic(ossUrlCleanerUtil.cleanUrlsToString(item.getPic()));
                }
                item.setGoodsId(frontGoods.getId());
                item.setCreateTime(DateUtils.getLocalDateTime());
                item.setUpdateTime(DateUtils.getLocalDateTime());
                frontGoodsSpecMapper.insert(item);
            });
            frontGoods.setFrontGoodsSpecList(frontGoodsSpecList);
            try {
                storeErpService.pushGoodsToErp(frontGoods);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            return Boolean.TRUE;
        }

        if (frontGoods.getGoodsType() == 0){ // 套餐新增
            handleImage(frontGoods);
            // 添加套餐
            frontGoods.setCreateTime(DateUtils.getLocalDateTime());
            frontGoods.setUpdateTime(DateUtils.getLocalDateTime());
            frontGoodsMapper.insert(frontGoods);

            List<FrontGoodsSpec> frontGoodsSpecList = frontGoods.getFrontGoodsSpecList();
            frontGoodsSpecList.forEach(item ->{
                item.setGoodsType(0);
                if (item.getPic() != null){
                    item.setPic(ossUrlCleanerUtil.cleanUrlsToString(item.getPic()));
                }
                item.setGoodsId(frontGoods.getId());
                item.setCreateTime(DateUtils.getLocalDateTime());
                item.setUpdateTime(DateUtils.getLocalDateTime());
                frontGoodsSpecMapper.insert(item);
            });

            frontGoods.setFrontGoodsSpecList(frontGoodsSpecList);

            try {
                storeErpService.pushGoodsToErp(frontGoods);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     *  通过货号查询商品id
     * @param goodsNo
     * @return
     */
    private Long getGoodsId(String goodsNo){
        FrontGoods frontGoods = frontGoodsMapper.selectOne(new LambdaQueryWrapper<FrontGoods>().eq(FrontGoods::getGoodsNo, goodsNo));
        if (frontGoods != null){
            return frontGoods.getId();
        }
        return null;
    }

    /**
     * 修改商品
     * 
     * @param frontGoods 商品
     * @return 结果
     */
    @Override
    @Transactional
    public Boolean updateFrontGoods(FrontGoods frontGoods)
    {
        // 判断商品类型
        if (frontGoods.getGoodsType() == 0){
            // 获取规格 进行相关处理
            saveOrUpdateFrontGoodsSpec(frontGoods.getFrontGoodsSpecList(), frontGoods);

            frontGoods.setUpdateTime(DateUtils.getLocalDateTime());
            handleImage(frontGoods);
            frontGoodsMapper.updateById(frontGoods);

            try {
                storeErpService.pushGoodsToErp(frontGoods);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            return Boolean.TRUE;
        }

        if (frontGoods.getGoodsType() == 1){
            if (frontGoods.getGoodsNo() == null){
                String goodsNo = this.generateGoodsNo(null, frontGoods.getGategoryId());
                frontGoods.setGoodsNo(goodsNo);
            }

            // 获取规格 进行相关处理
            saveOrUpdateFrontGoodsSpec(frontGoods.getFrontGoodsSpecList(), frontGoods);

            frontGoods.setUpdateTime(DateUtils.getLocalDateTime());
            handleImage(frontGoods);
            frontGoodsMapper.updateById(frontGoods);

            try {
                storeErpService.pushGoodsToErp(frontGoods);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     * 处理图片
     */
    private void handleImage(FrontGoods frontGoods){
        if (frontGoods.getIndexImage() != null){
            frontGoods.setIndexImage(ossUrlCleanerUtil.cleanUrlsToString(frontGoods.getIndexImage()));
        }

        if (frontGoods.getFirstPic() != null){
            frontGoods.setFirstPic(ossUrlCleanerUtil.cleanUrlsToString(frontGoods.getFirstPic()));
        }

        if (frontGoods.getBanner() != null){
            frontGoods.setBanner(ossUrlCleanerUtil.cleanUrlsToString(frontGoods.getBanner()));
        }
    }

    private void saveOrUpdateFrontGoodsSpec(List<FrontGoodsSpec> frontGoodsSpecList,FrontGoods frontGoods){
        // 遍历判断新增还是修改
        frontGoodsSpecList.forEach(frontGoodsSpec -> {
            if (frontGoodsSpec.getId() == null){
                if (frontGoodsSpec.getPic() != null){
                    frontGoodsSpec.setPic(ossUrlCleanerUtil.cleanUrlsToString(frontGoodsSpec.getPic()));
                }
                frontGoodsSpec.setCreateTime(DateUtils.getLocalDateTime());
                frontGoodsSpec.setGoodsId(frontGoods.getId());
                frontGoodsSpecMapper.insert(frontGoodsSpec);
            }else{
                if (frontGoodsSpec.getPic() != null){
                    frontGoodsSpec.setPic(ossUrlCleanerUtil.cleanUrlsToString(frontGoodsSpec.getPic()));
                }
                frontGoodsSpec.setUpdateTime(DateUtils.getLocalDateTime());
                frontGoodsSpecMapper.updateById(frontGoodsSpec);
            }
        });
    }

    /**
     * 批量删除商品
     * 
     * @param ids 需要删除的商品主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteFrontGoodsByIds(Long[] ids)
    {
        return frontGoodsMapper.deleteFrontGoodsByIds(ids);
    }

    /**
     * 删除商品信息
     * 
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public int deleteFrontGoodsById(Long id)
    {
        return frontGoodsMapper.deleteFrontGoodsById(id);
    }

    @Override
    @Transactional
    public Boolean updateGoodsStatusByIds(Long[] ids) {
        // 批量修改
        List<FrontGoods> list = new ArrayList<>();
        for (Long id : ids) {
            // 获取商品信息
            FrontGoods frontGoods = frontGoodsMapper.selectById(id);
            frontGoods.setIsStatus(frontGoods.getIsStatus() == 1 ? 0 : 1);
            list.add(frontGoods);
        }
        // 批量修改
        return this.updateBatchById(list);
    }

    @Override
    public int updateGoodsStatusById(Long id) {
        // 查询商品信息
        FrontGoods frontGoods = frontGoodsMapper.selectById(id);
        frontGoods.setIsStatus(frontGoods.getIsStatus() == 1 ? 0 : 1);
        return frontGoodsMapper.updateById(frontGoods);
    }

    @Override
    public boolean updateGoodsSortById(Long id, Integer sort) {
        FrontGoods frontGoods = this.getById(id);
        frontGoods.setSort(sort);
        return this.updateById(frontGoods);
    }

    @Override
    public String generateGoodsNo(String num,Long id) {
        if (num != null){
            // 判断num是否为汉字
            if (num.matches("[\\u4e00-\\u9fa5]+")){
                throw new RuntimeException("前缀不能为汉字");
            }
            return SerialNumberUtil.generate(num.toUpperCase(), 6);
        }else{
            // 获取顶级分类详情
            FrontGategory frontGategory = frontGategoryMapper.selectById(id);
            String firstLetters = PinyinUtils.getFirstLetters(frontGategory.getTitle());
            return SerialNumberUtil.generate(firstLetters, 6);
        }
    }

    @Override
    public List<PackageListVO> selectFrontPackageList(FrontPackage frontPackage) {
        List<PackageListVO> frontPackages = frontPackageMapper.selectFrontPackageList(frontPackage);
        // 查询当前关联的套餐id
        List<FrontGoods> goods = frontGoodsMapper.selectList(new LambdaQueryWrapper<FrontGoods>()
                .eq(FrontGoods::getGoodsType, 0));

        // 获取goods的id集合并去重
        List<Long> ids = goods.stream().map(FrontGoods::getId).distinct().collect(Collectors.toList());

        // 过滤包含ids的数据
        frontPackages = frontPackages.stream().filter(item -> !ids.contains(item.getId())).collect(Collectors.toList());
        return frontPackages;
    }
}
