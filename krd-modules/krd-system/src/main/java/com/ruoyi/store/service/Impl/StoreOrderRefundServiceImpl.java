package com.ruoyi.store.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.store.domain.res.StoreOrderRefundRequest;
import com.ruoyi.store.domain.vo.WxRefundResponseVo;
import com.ruoyi.store.service.StoreOrderRefundService;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.WechatExceptions;
import com.ruoyi.system.api.mapper.WechatexceptionsMapper;
import com.ruoyi.system.api.utils.CommonUtil;
import com.ruoyi.system.api.utils.RestTemplateUtil;
import com.ruoyi.system.api.utils.WxPayUtil;
import com.ruoyi.system.api.utils.XmlUtil;
import com.ruoyi.system.api.vo.WxRefundVo;
import com.ruoyi.user.mapper.FrontOrderRefundMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;

/**
 * @Author: suhai
 * @Date: 2025/6/19
 * @Description:
 */
@Service
@AllArgsConstructor
public class StoreOrderRefundServiceImpl extends ServiceImpl<FrontOrderRefundMapper, FrontOrders> implements StoreOrderRefundService {

    private final RestTemplateUtil restTemplateUtil;

    private final WechatexceptionsMapper wechatExceptionsService;
    @Override
    public void refund(StoreOrderRefundRequest request , FrontOrders storeOrder) {
        String appId = "wxbbfc64e72fa3980f";
        String mchId = "1720010260";
        String signKey = "frB6YEFq2FxlJrJJ6K0hATIiSawPexfg";
        String path = "/Users/<USER>/IdeaProjects/menstrual-back/krd-modules/krd-system/src/main/resources/apiclient_cert.p12";
        String apiDomain = "https://yjt.beten.cn";

        //统一下单数据
        WxRefundVo wxRefundVo = new WxRefundVo();
        wxRefundVo.setAppid(appId);
        wxRefundVo.setMch_id(mchId);
        wxRefundVo.setNonce_str(WxPayUtil.getNonceStr());
        wxRefundVo.setOut_trade_no(storeOrder.getOutTradeNo());
        wxRefundVo.setOut_refund_no(storeOrder.getOrderNumber());
        wxRefundVo.setTotal_fee(storeOrder.getPayPrice().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).intValue());
        wxRefundVo.setRefund_fee(request.getAmount().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).intValue());
        wxRefundVo.setNotify_url(apiDomain + PayConstants.WX_PAY_REFUND_NOTIFY_API_URI);
        String sign = WxPayUtil.getSign(wxRefundVo, signKey);
        wxRefundVo.setSign(sign);
        payRefund(wxRefundVo, path);
    }

    public WxRefundResponseVo payRefund(WxRefundVo wxRefundVo, String path) {
        String xmlStr = XmlUtil.objectToXml(wxRefundVo);
        String url = PayConstants.WX_PAY_API_URL + PayConstants.WX_PAY_REFUND_API_URI;
        HashMap<String, Object> map = CollUtil.newHashMap();
        String xml = "";
        try {
            xml = restTemplateUtil.postWXRefundXml(url, xmlStr, wxRefundVo.getMch_id(), path);
            map = XmlUtil.xmlToMap(xml);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GlobalException("xmlToMap错误，xml = " + xml);
        }
        if (null == map) {
            throw new GlobalException("微信无信息返回，微信申请退款失败！");
        }

        WxRefundResponseVo responseVo = CommonUtil.mapToObj(map, WxRefundResponseVo.class);
        if ("FAIL".equals(responseVo.getReturnCode().toUpperCase())) {
            wxPayExceptionDispose(map, "微信申请退款异常1");
            throw new GlobalException("微信申请退款失败1！" +  responseVo.getReturnMsg());
        }

        if ("FAIL".equals(responseVo.getResultCode().toUpperCase())) {
            wxPayExceptionDispose(map, "微信申请退款业务异常");
            throw new GlobalException("微信申请退款失败2！" + responseVo.getErrCodeDes());
        }
        System.out.println("================微信申请退款结束=========================");
        return responseVo;
    }
    /**
     * 微信支付异常处理
     * @param map 微信返回数据
     * @param remark 备注
     */
    private void wxPayExceptionDispose(HashMap<String, Object> map, String remark) {
        WechatExceptions wechatExceptions = new WechatExceptions();
        String returnCode = (String) map.get("return_code");
        if ("FAIL".equals(returnCode.toUpperCase())) {
            wechatExceptions.setErrcode("-100");
            wechatExceptions.setErrmsg(map.get("return_msg").toString());
        } else {
            wechatExceptions.setErrcode(map.get("err_code").toString());
            wechatExceptions.setErrmsg(map.get("err_code_des").toString());
        }
        wechatExceptions.setData(JSONObject.toJSONString(map));
        wechatExceptions.setRemark(remark);
        wechatExceptions.setCreateTime(DateUtil.date());
        wechatExceptions.setUpdateTime(DateUtil.date());
        wechatExceptionsService.insert(wechatExceptions);
    }
}
