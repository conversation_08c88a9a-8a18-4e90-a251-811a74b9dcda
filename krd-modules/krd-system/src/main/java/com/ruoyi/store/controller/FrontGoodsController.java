package com.ruoyi.store.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.check.domain.entity.FrontPackage;
import com.ruoyi.store.domain.vo.FrontGoodsVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontGoods;
import com.ruoyi.store.service.IFrontGoodsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 商品Controller
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/goods")
public class FrontGoodsController extends BaseController
{
    @Autowired
    private IFrontGoodsService frontGoodsService;

    /**
     * 查询商品列表
     */
    @RequiresPermissions("front:goods:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontGoodsVo.SearchParam vo)
    {
        startPage();
        List<FrontGoods> list = frontGoodsService.selectFrontGoodsList(vo);
        return getDataTable(list);
    }

    /**
     * 导出商品列表
     */
    @RequiresPermissions("front:goods:export")
    @Log(title = "商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontGoodsVo.SearchParam vo)
    {
        List<FrontGoods> list = frontGoodsService.selectFrontGoodsList(vo);
        ExcelUtil<FrontGoods> util = new ExcelUtil<FrontGoods>(FrontGoods.class);
        util.exportExcel(response, list, "商品数据");
    }

    /**
     * 获取商品详细信息
     */
    @RequiresPermissions("front:goods:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontGoodsService.selectFrontGoodsById(id));
    }

    /**
     * 新增商品
     */
    @RequiresPermissions("front:goods:add")
    @Log(title = "商品", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FrontGoods frontGoods)
    {
        return toAjax(frontGoodsService.insertFrontGoods(frontGoods));
    }

    /**
     * 修改商品
     */
    @RequiresPermissions("front:goods:edit")
    @Log(title = "商品", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody FrontGoods frontGoods)
    {
        return toAjax(frontGoodsService.updateFrontGoods(frontGoods));
    }

    /**
     * 删除商品
     */
    @RequiresPermissions("front:goods:remove")
    @Log(title = "商品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontGoodsService.deleteFrontGoodsByIds(ids));
    }

    @RequiresPermissions("front:goods:remove")
    @Log(title = "商品删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id)
    {
        return toAjax(frontGoodsService.deleteFrontGoodsById(id));
    }

    @RequiresPermissions("front:goods:isStatus")
    @Log(title = "商品批量修改上下架", businessType = BusinessType.UPDATE)
    @PostMapping("/isStatusBatch/{ids}")
    public AjaxResult isStatusBatch(@PathVariable Long[] ids)
    {
        return toAjax(frontGoodsService.updateGoodsStatusByIds(ids));
    }

    @RequiresPermissions("front:goods:isStatus")
    @Log(title = "商品修改上下架", businessType = BusinessType.UPDATE)
    @PostMapping("/isStatus/{id}")
    public AjaxResult isStatus(@PathVariable("id") Long id)
    {
        return toAjax(frontGoodsService.updateGoodsStatusById(id));
    }

    @RequiresPermissions("front:goods:sort")
    @Log(title = "商品修改排序", businessType = BusinessType.UPDATE)
    @PostMapping("/sort/{id}/{sort}")
    public AjaxResult sort(@PathVariable("id") Long id,@PathVariable("sort") Integer sort){
        return toAjax(frontGoodsService.updateGoodsSortById(id,sort));
    }

    /**
     * 生成商品货号
     */
    @Operation(summary = "生成商品货号")
    @GetMapping("/generateGoodsNo/{num}")
    public AjaxResult generateGoodsNo(@PathVariable("num") String num)
    {
        return success("成功",frontGoodsService.generateGoodsNo(num,null));
    }

    @Operation(summary = "获取未管理的套餐数据")
    @GetMapping("/getUnManagePackage")
    public AjaxResult getUnManagePackage(FrontPackage frontPackage)
    {
        return success(frontGoodsService.selectFrontPackageList(frontPackage));
    }

}
