package com.ruoyi.store.service.Impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.oss.OssUrlCleanerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.api.mapper.FrontGoodsSpecMapper;
import com.ruoyi.system.api.domain.FrontGoodsSpec;
import com.ruoyi.store.service.IFrontGoodsSpecService;

/**
 * 商品规格Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class FrontGoodsSpecServiceImpl implements IFrontGoodsSpecService 
{
    @Autowired
    private FrontGoodsSpecMapper frontGoodsSpecMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    /**
     * 查询商品规格
     * 
     * @param id 商品规格主键
     * @return 商品规格
     */
    @Override
    public FrontGoodsSpec selectFrontGoodsSpecById(Long id)
    {
        FrontGoodsSpec frontGoodsSpec = frontGoodsSpecMapper.selectFrontGoodsSpecById(id);
        frontGoodsSpec.setPic(ossUrlCleanerUtil.getSignatureUrl(frontGoodsSpec.getPic()));
        return frontGoodsSpec;
    }

    /**
     * 查询商品规格列表
     * 
     * @param frontGoodsSpec 商品规格
     * @return 商品规格
     */
    @Override
    public List<FrontGoodsSpec> selectFrontGoodsSpecList(FrontGoodsSpec frontGoodsSpec)
    {
        List<FrontGoodsSpec> frontGoodsSpecs = frontGoodsSpecMapper.selectFrontGoodsSpecList(frontGoodsSpec);
        frontGoodsSpecs.forEach(item ->{
            item.setPic(ossUrlCleanerUtil.getSignatureUrl(item.getPic()));
        });
        return frontGoodsSpecs;
    }

    /**
     * 新增商品规格
     * 
     * @param frontGoodsSpec 商品规格
     * @return 结果
     */
    @Override
    public int insertFrontGoodsSpec(FrontGoodsSpec frontGoodsSpec)
    {
        frontGoodsSpec.setPic(ossUrlCleanerUtil.getSignatureUrl(frontGoodsSpec.getPic()));
        frontGoodsSpec.setCreateTime(DateUtils.getLocalDateTime());
        return frontGoodsSpecMapper.insert(frontGoodsSpec);
    }

    /**
     * 修改商品规格
     * 
     * @param frontGoodsSpec 商品规格
     * @return 结果
     */
    @Override
    public int updateFrontGoodsSpec(FrontGoodsSpec frontGoodsSpec)
    {
        frontGoodsSpec.setPic(ossUrlCleanerUtil.getSignatureUrl(frontGoodsSpec.getPic()));
        frontGoodsSpec.setUpdateTime(DateUtils.getLocalDateTime());
        return frontGoodsSpecMapper.updateById(frontGoodsSpec);
    }

    /**
     * 批量删除商品规格
     * 
     * @param ids 需要删除的商品规格主键
     * @return 结果
     */
    @Override
    public int deleteFrontGoodsSpecByIds(Long[] ids)
    {
        return frontGoodsSpecMapper.deleteFrontGoodsSpecByIds(ids);
    }

    /**
     * 删除商品规格信息
     * 
     * @param id 商品规格主键
     * @return 结果
     */
    @Override
    public int deleteFrontGoodsSpecById(Long id)
    {
        return frontGoodsSpecMapper.deleteFrontGoodsSpecById(id);
    }
}
