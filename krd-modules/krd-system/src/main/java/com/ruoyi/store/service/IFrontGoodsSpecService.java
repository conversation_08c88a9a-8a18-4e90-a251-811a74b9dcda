package com.ruoyi.store.service;

import java.util.List;
import com.ruoyi.system.api.domain.FrontGoodsSpec;

/**
 * 商品规格Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IFrontGoodsSpecService 
{
    /**
     * 查询商品规格
     * 
     * @param id 商品规格主键
     * @return 商品规格
     */
    public FrontGoodsSpec selectFrontGoodsSpecById(Long id);

    /**
     * 查询商品规格列表
     * 
     * @param frontGoodsSpec 商品规格
     * @return 商品规格集合
     */
    public List<FrontGoodsSpec> selectFrontGoodsSpecList(FrontGoodsSpec frontGoodsSpec);

    /**
     * 新增商品规格
     * 
     * @param frontGoodsSpec 商品规格
     * @return 结果
     */
    public int insertFrontGoodsSpec(FrontGoodsSpec frontGoodsSpec);

    /**
     * 修改商品规格
     * 
     * @param frontGoodsSpec 商品规格
     * @return 结果
     */
    public int updateFrontGoodsSpec(FrontGoodsSpec frontGoodsSpec);

    /**
     * 批量删除商品规格
     * 
     * @param ids 需要删除的商品规格主键集合
     * @return 结果
     */
    public int deleteFrontGoodsSpecByIds(Long[] ids);

    /**
     * 删除商品规格信息
     * 
     * @param id 商品规格主键
     * @return 结果
     */
    public int deleteFrontGoodsSpecById(Long id);
}
