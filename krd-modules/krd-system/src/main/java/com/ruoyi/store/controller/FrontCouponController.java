package com.ruoyi.store.controller;

import java.util.List;

import com.ruoyi.system.api.domain.FrontCouponInfo;
import com.ruoyi.system.api.domain.StoreFrontCoupon;
import com.ruoyi.system.api.domain.vo.FrontCouponVo;
import com.ruoyi.store.service.IFrontCouponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 优惠券Controller
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/coupon")
public class FrontCouponController extends BaseController
{
    @Autowired
    private IFrontCouponService frontCouponService;

    /**
     * 查询优惠券列表
     */
    @RequiresPermissions("front:coupon:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontCouponVo.FrontCouponListSearch vo)
    {
        startPage();
        List<FrontCouponVo.FrontCouponList> list = frontCouponService.selectFrontCouponList(vo);
        return getDataTable(list);
    }

    @RequiresPermissions("front:coupon:detail")
    @GetMapping("/detail/{id}")
    public AjaxResult detail(@PathVariable("id")Long id) {
        return success(frontCouponService.selectFrontCouponDetailById(id));
    }

    @RequiresPermissions("front:coupon:detail")
    @GetMapping("/detailList")
    public TableDataInfo detailList(FrontCouponVo.FrontCouponDetailSearch vo) {
        startPage();
        List<FrontCouponInfo> list = frontCouponService.selectFrontCouponInfoById(vo);
        return getDataTable(list);
    }

    /**
     * 获取优惠券详细信息
     */
    @RequiresPermissions("front:coupon:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontCouponService.selectFrontCouponById(id));
    }

    /**
     * 新增优惠券
     */
    @RequiresPermissions("front:coupon:add")
    @Log(title = "优惠券", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody StoreFrontCoupon frontCoupon)
    {
        return toAjax(frontCouponService.insertFrontCoupon(frontCoupon));
    }

    /**
     * 修改优惠券
     */
    @RequiresPermissions("front:coupon:edit")
    @Log(title = "优惠券", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody StoreFrontCoupon frontCoupon)
    {
        return toAjax(frontCouponService.updateFrontCoupon(frontCoupon));
    }

    /**
     * 删除优惠券
     */
    @RequiresPermissions("front:coupon:remove")
    @Log(title = "优惠券", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontCouponService.deleteFrontCouponByIds(ids));
    }

    /**
     * 优惠券上下架
     */
    @RequiresPermissions("front:coupon:edit")
    @Log(title = "优惠券", businessType = BusinessType.UPDATE)
    @PostMapping("/status/{id}")
    public AjaxResult updateStatus(@PathVariable("id") Long id) {
        return toAjax(frontCouponService.updateFrontCouponStatus(id));
    }

}
