package com.ruoyi.store.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontGift;
import com.ruoyi.store.domain.vo.FrontGiftVo;
import com.ruoyi.store.service.IFrontGiftService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName FrontGiftController
 * @Description 后台礼品卡管理 控制层
 * <AUTHOR>
 * @Date 2025/5/19 下午5:42
 */
@Slf4j
@Tag(name = "礼品卡管理", description = "后台商城管理——礼品卡管理")
@RestController
@RequestMapping("/frontGift")
public class FrontGiftController extends BaseController {

    @Autowired
    private IFrontGiftService frontGiftService;

    @RequiresPermissions("front:gift:list")
    @Operation(description = "礼品卡列表")
    @GetMapping("/list")
    public TableDataInfo list(FrontGiftVo.FrontGiftSearch vo) {
        startPage();
        return getDataTable(frontGiftService.getGiftList(vo));
    }

    @RequiresPermissions("front:gift:add")
    @Operation(description = "新增礼品卡")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FrontGift frontGift) {
        return frontGiftService.addGift(frontGift) ? success() : error();
    }

    @RequiresPermissions("front:gift:update")
    @Operation(description = "修改礼品卡")
    @PostMapping("/update")
    public AjaxResult update(@RequestBody FrontGift frontGift) {
        return frontGiftService.updateGift(frontGift) ? success() : error();
    }

    @RequiresPermissions("front:gift:delete")
    @Operation(description = "删除礼品卡")
    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        frontGiftService.deleteGiftByIds(ids);
        return success();
    }

}
