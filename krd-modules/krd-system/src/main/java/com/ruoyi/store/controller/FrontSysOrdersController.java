package com.ruoyi.store.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.store.domain.res.StoreOrderRefundRequest;
import com.ruoyi.store.service.IFrontSysOrdersService;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.FrontOrdersInvoice;
import com.ruoyi.system.api.domain.vo.FrontOrdersVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName FrontSysOrdersController
 * @Description 后台订单管理控制
 * <AUTHOR>
 * @Date 2025/5/28 下午2:50
 */
@Slf4j
@RestController
@Tag(name = "后台订单管理控制", description = "后台订单管理控制")
@RequestMapping("/sys/orders")
public class FrontSysOrdersController extends BaseController {

    @Autowired
    private IFrontSysOrdersService frontSysOrdersService;

    @RequiresPermissions("front:orders:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontOrdersVo.FrontOrdersSearch vo)
    {
        startPage();
        List<FrontOrders> list = frontSysOrdersService.selectFrontOrdersList(vo);
        return getDataTable(list);
    }

    @GetMapping("/detail/{num}")
    public AjaxResult detail(@PathVariable("num") String num){
        return AjaxResult.success(frontSysOrdersService.selectOrderNum(num));
    }

    @RequiresPermissions("front:orders:delivery")
    @Operation(summary = "根据订单编号查询发货信息")
    @GetMapping("/delivery/{num}")
    public AjaxResult delivery(@PathVariable("num") String num){
        return AjaxResult.success(frontSysOrdersService.selectOrderDelivery(num));
    }

    @Operation(summary = "修改订单收货信息")
    @RequiresPermissions("front:orders:updateDelivery")
    @PostMapping("/updateDelivery")
    public AjaxResult updateDelivery(@RequestBody FrontOrdersVo.FrontOrdersReceiveUpdateInfo vo){
        return toAjax(frontSysOrdersService.updateOrderDelivery(vo));
    }

    @Operation(summary = "修改订单备注信息")
    @RequiresPermissions("front:orders:updateRemark")
    @PostMapping("/updateRemark")
    public AjaxResult updateRemark(@RequestBody FrontOrdersVo.FrontOrdersRemarkUpdateInfo vo){
        return toAjax(frontSysOrdersService.updateOrderRemark(vo));
    }

    @Operation(summary = "修改订单发票信息")
    @RequiresPermissions("front:orders:updateInvoice")
    @PostMapping("/updateInvoice")
    public AjaxResult updateInvoice(@RequestBody FrontOrdersInvoice frontOrdersInvoice){
        return toAjax(frontSysOrdersService.updateOrderInvoice(frontOrdersInvoice));
    }

    /**
     * 导出订单总列表
     */
    @RequiresPermissions("system:orders:export")
    @Log(title = "订单总", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontOrdersVo.FrontOrdersSearch vo)
    {
        List<FrontOrders> list = frontSysOrdersService.selectFrontOrdersList(vo);
        ExcelUtil<FrontOrders> util = new ExcelUtil<FrontOrders>(FrontOrders.class);
        util.exportExcel(response, list, "订单总数据");
    }

    //  修改订单状态
    @Operation(summary = "修改订单状态")
    @GetMapping("/updateCancelStatus/{id}")
    public AjaxResult updateStatus(@PathVariable("id") Long id) {
        return toAjax(frontSysOrdersService.updateOrderStatus(id));
    }



    @Operation(summary = "退款")
    @RequestMapping(value = "/refund", method = RequestMethod.GET)
    public AjaxResult send(@Validated StoreOrderRefundRequest request) {
        return toAjax(frontSysOrdersService.refund(request));
    }
}
