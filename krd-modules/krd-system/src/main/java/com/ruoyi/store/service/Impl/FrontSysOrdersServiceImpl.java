package com.ruoyi.store.service.Impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.common.redis.util.RedisUtil;
import com.ruoyi.store.domain.res.StoreOrderRefundRequest;
import com.ruoyi.store.mapper.FrontGoodsMapper;
import com.ruoyi.store.service.IFrontSysOrdersService;
import com.ruoyi.store.service.StoreOrderRefundService;
import com.ruoyi.system.api.domain.*;
import com.ruoyi.system.api.domain.vo.FrontOrdersVo;
import com.ruoyi.system.api.mapper.*;
import com.ruoyi.system.api.oss.OssUrlCleanerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单总Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class FrontSysOrdersServiceImpl implements IFrontSysOrdersService
{
    @Autowired
    private FrontOrdersMapper frontOrdersMapper;

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Autowired
    private FrontOrdersGoodsMapper frontOrdersGoodsMapper;

    @Autowired
    private FrontGoodsSpecMapper frontGoodsSpecMapper;

    @Autowired
    private FrontGoodsMapper  frontGoodsMapper;

    @Autowired
    private FrontOrdersInvoiceMapper frontOrdersInvoiceMapper;

    @Autowired
    private FrontFastmailMapper frontFastmailMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    @Autowired
    private FrontOrdersMapper dao;

    @Autowired
    private StoreOrderRefundService storeOrderRefundService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 查询订单总
     *
     * @param id 订单总主键
     * @return 订单总
     */
    @Override
    public FrontOrders selectFrontOrdersById(Long id)
    {
        return frontOrdersMapper.selectFrontOrdersById(id);
    }

    /**
     * 查询订单总列表
     *
     * @param vo 查询参数
     * @return 订单总
     */
    @Override
    public List<FrontOrders> selectFrontOrdersList(FrontOrdersVo.FrontOrdersSearch vo)
    {
        return frontOrdersMapper.selectFrontOrdersList(vo);
    }

    /**
     * 新增订单总
     *
     * @param frontOrders 订单总
     * @return 结果
     */
    @Override
    public int insertFrontOrders(FrontOrders frontOrders)
    {
        frontOrders.setCreateTime(LocalDateTime.now());
        return frontOrdersMapper.insertFrontOrders(frontOrders);
    }

    /**
     * 修改订单总
     *
     * @param frontOrders 订单总
     * @return 结果
     */
    @Override
    public int updateFrontOrders(FrontOrders frontOrders)
    {
        frontOrders.setUpdateTime(LocalDateTime.now());
        return frontOrdersMapper.updateFrontOrders(frontOrders);
    }

    /**
     * 批量删除订单总
     *
     * @param ids 需要删除的订单总主键
     * @return 结果
     */
    @Override
    public int deleteFrontOrdersByIds(Long[] ids)
    {
        return frontOrdersMapper.deleteFrontOrdersByIds(ids);
    }

    /**
     * 删除订单总信息
     *
     * @param id 订单总主键
     * @return 结果
     */
    @Override
    public int deleteFrontOrdersById(Long id)
    {
        return frontOrdersMapper.deleteFrontOrdersById(id);
    }

    @Override
    public FrontOrdersVo.FrontOrdersDetail selectOrderNum(String orderNum) {
        // 根据订单号查询订单信息
        FrontOrders frontOrders = frontOrdersMapper.selectOne(new LambdaQueryWrapper<FrontOrders>().eq(FrontOrders::getOrderNumber, orderNum));

        FrontOrdersVo.FrontOrdersDetail frontOrdersDetail = new FrontOrdersVo.FrontOrdersDetail();

        // 查询用户信息
        FrontUser frontUser = frontUserMapper.selectFrontUserById(frontOrders.getUserId());

        // 保存订单信息
        FrontOrdersVo.FrontOrdersBaseInfo frontOrdersBaseInfo = new FrontOrdersVo.FrontOrdersBaseInfo();

        frontOrdersBaseInfo.setStatus(frontOrders.getStatus());
        frontOrdersBaseInfo.setOrderNum(frontOrders.getOrderNumber());
//        frontOrdersBaseInfo.setType(frontOrders.getType());
        frontOrdersBaseInfo.setUserName(frontUser.getUserMobile() + " " +frontUser.getUserName());
        frontOrdersBaseInfo.setPushGoodsNo(frontOrders.getPushGoodsNo());
        frontOrdersBaseInfo.setCreateTime(frontOrders.getCreateTime());
        // 通过订单号获取订单信息
        FrontFastmail frontFastmail = frontFastmailMapper.selectFrontFastmailByOrderNum(frontOrders.getOrderNumber());
        if (frontFastmail != null){
            frontOrdersBaseInfo.setDeliveryCompany(frontFastmail.getCompany());
            frontOrdersBaseInfo.setDeliveryNum(frontFastmail.getNumber());
        }

        frontOrdersDetail.setBaseInfo(frontOrdersBaseInfo);

        // 通过订单信息查询商品信息
        List<FrontOrdersGoods> frontOrdersGoodsList = frontOrdersGoodsMapper.selectList(new LambdaQueryWrapper<FrontOrdersGoods>().eq(FrontOrdersGoods::getOrderId, frontOrders.getId()));

        //  保存商品信息
        List<FrontOrdersVo.FrontOrdersGoodsInfo> frontOrdersGoodsInfoList = new ArrayList<>();

        frontOrdersGoodsList.forEach(item -> {
            FrontOrdersVo.FrontOrdersGoodsInfo goodsInfo = new FrontOrdersVo.FrontOrdersGoodsInfo();

            // 获取商品信息 规格信息
            FrontGoods frontGoods = frontGoodsMapper.selectById(item.getGoodsId());

            FrontGoodsSpec frontGoodsSpec = frontGoodsSpecMapper.selectById(item.getGoodsSpecId());

            if (frontGoods != null){
                goodsInfo.setGoodsImg(ossUrlCleanerUtil.getSignatureUrl(frontGoods.getIndexImage()));
                goodsInfo.setGoodsName(frontGoods.getName());
                goodsInfo.setGoodsNo(frontGoods.getGoodsNo());
            }

            if (frontGoodsSpec != null){
                goodsInfo.setGoodsPrice(frontGoodsSpec.getPrice());
                goodsInfo.setGoodsSpec(frontGoodsSpec.getTitle());
            }

            goodsInfo.setGoodsNum(item.getCount());
            goodsInfo.setDiscount(item.getDiscount());

            // 应付小计
            goodsInfo.setSubtotal(item.getPrice().multiply(BigDecimal.valueOf(item.getCount())));

            goodsInfo.setPayAmount(item.getPayPrice());
            frontOrdersGoodsInfoList.add(goodsInfo);
        });

        frontOrdersDetail.setGoodsInfos(frontOrdersGoodsInfoList);

        // 保存收货人信息及备注
        FrontOrdersVo.FrontOrdersReceiveInfo frontOrdersReceiveInfo = getFrontOrdersReceiveInfo(frontOrders);
        frontOrdersDetail.setReceiveInfo(frontOrdersReceiveInfo);

        // 保存发票信息
        FrontOrdersInvoice frontOrdersInvoice = frontOrdersInvoiceMapper.selectOne(new LambdaQueryWrapper<FrontOrdersInvoice>().eq(FrontOrdersInvoice::getOrderId, frontOrders.getId()));
        frontOrdersDetail.setInvoiceInfo(frontOrdersInvoice);

        // 保存付款信息
        FrontOrdersVo.FrontOrdersPayInfo frontOrdersPayInfo = getFrontOrdersPayInfo(frontOrders);
        frontOrdersDetail.setPayInfo(frontOrdersPayInfo);

        return frontOrdersDetail;
    }



    @Override
    public FrontOrdersVo.FrontOrdersDeliveryInfo selectOrderDelivery(String orderNum) {
        // 根据订单号查询订单信息
        LambdaQueryWrapper<FrontOrders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontOrders::getOrderNumber, orderNum);
        FrontOrders frontOrders = frontOrdersMapper.selectOne(queryWrapper);

        // 保存数据
        FrontOrdersVo.FrontOrdersDeliveryInfo frontOrdersDeliveryInfo = new FrontOrdersVo.FrontOrdersDeliveryInfo();
        frontOrdersDeliveryInfo.setOrderNum(frontOrders.getOrderNumber());
        frontOrdersDeliveryInfo.setReceiveName(frontOrders.getReceiveName());
        frontOrdersDeliveryInfo.setReceivePhone(frontOrders.getReceivePhone());
        frontOrdersDeliveryInfo.setReceiveAddress(frontOrders.getReceiveAddress());
        frontOrdersDeliveryInfo.setReceiveAddressDetail(frontOrders.getReceiveAddressDetail());
        frontOrdersDeliveryInfo.setUserName(frontUserMapper.selectFrontUserById(frontOrders.getUserId()).getUserName());

        // 通过订单编号查询订单对应商品信息
        List<FrontOrdersGoods> frontOrdersGoodsList = frontOrdersGoodsMapper.selectList(new LambdaQueryWrapper<FrontOrdersGoods>().eq(FrontOrdersGoods::getOrderId, frontOrders.getId()));

        List<FrontOrdersVo.FrontOrdersGoodsInfo> frontOrdersGoodsInfoList = new ArrayList<>();

        if (frontOrdersGoodsList != null){
            frontOrdersGoodsList.forEach(frontOrdersGoods -> {
                FrontOrdersVo.FrontOrdersGoodsInfo goodsInfo = new FrontOrdersVo.FrontOrdersGoodsInfo();

                // 获取商品id和规格id查询对应数据
                FrontGoods frontGoods = frontGoodsMapper.selectOne(new LambdaQueryWrapper<FrontGoods>().eq(FrontGoods::getId, frontOrdersGoods.getGoodsId()));

                FrontGoodsSpec frontGoodsSpec = frontGoodsSpecMapper.selectOne(new LambdaQueryWrapper<FrontGoodsSpec>().eq(FrontGoodsSpec::getId, frontOrdersGoods.getGoodsSpecId()));

                if (frontGoods != null){
                    goodsInfo.setGoodsName(frontGoods.getName());
                    goodsInfo.setGoodsType(frontGoods.getGoodsType());
                    goodsInfo.setUnit(frontGoods.getUnit());
                }

                if (frontGoodsSpec != null){
                    goodsInfo.setGoodsSpec(frontGoodsSpec.getTitle());
                }

                goodsInfo.setGoodsNum(frontOrdersGoods.getCount());

                goodsInfo.setGoodsPrice(frontOrdersGoods.getPrice());

                goodsInfo.setGoodsAmount(frontOrdersGoods.getPrice().multiply(new BigDecimal(frontOrdersGoods.getCount())));

            });
        }

        frontOrdersDeliveryInfo.setGoodsInfos(frontOrdersGoodsInfoList);

        return frontOrdersDeliveryInfo;
    }

    @Override
    public Boolean updateOrderDelivery(FrontOrdersVo.FrontOrdersReceiveUpdateInfo vo) {
        //  修改订单收货信息
        FrontOrders frontOrders = frontOrdersMapper.selectOne(new LambdaQueryWrapper<FrontOrders>().eq(FrontOrders::getOrderNumber, vo.getOrderNum()));
        frontOrders.setReceiveName(vo.getReceiveName());
        frontOrders.setReceivePhone(vo.getReceivePhone());
        frontOrders.setReceiveAddress(vo.getReceiveAddress());
        frontOrders.setReceiveAddressDetail(vo.getReceiveAddressDetail());
        return frontOrdersMapper.updateById(frontOrders) > 0;
    }

    @Override
    public Boolean updateOrderRemark(FrontOrdersVo.FrontOrdersRemarkUpdateInfo vo) {
        //  修改订单备注信息
        FrontOrders frontOrders = frontOrdersMapper.selectOne(new LambdaQueryWrapper<FrontOrders>().eq(FrontOrders::getOrderNumber, vo.getOrderNum()));
        frontOrders.setUserRemark(vo.getUserRemark());
        frontOrders.setPlatRemark(vo.getPlatformRemark());
        return frontOrdersMapper.updateById(frontOrders) > 0;
    }

    @Override
    public Boolean updateOrderInvoice(FrontOrdersInvoice frontOrdersInvoice) {
        return frontOrdersInvoiceMapper.updateFrontOrdersInvoice(frontOrdersInvoice) > 0;
    }

    @Override
    public Boolean updateOrderStatus(Long id) {
        LambdaUpdateWrapper<FrontOrders> wrapper =  new LambdaUpdateWrapper<>();
        wrapper.eq(FrontOrders::getId, id);
        wrapper.set(FrontOrders::getStatus, 5);
        return frontOrdersMapper.update(null, wrapper) > 0;
    }



    /**
     * 获取订单收货人信息及备注
     * @param frontOrders
     * @return
     */
    private static FrontOrdersVo.FrontOrdersReceiveInfo getFrontOrdersReceiveInfo(FrontOrders frontOrders) {
        FrontOrdersVo.FrontOrdersReceiveInfo frontOrdersReceiveInfo = new FrontOrdersVo.FrontOrdersReceiveInfo();
        frontOrdersReceiveInfo.setReceiveName(frontOrders.getReceiveName());
        frontOrdersReceiveInfo.setReceivePhone(frontOrders.getReceivePhone());
        frontOrdersReceiveInfo.setReceiveAddress(frontOrders.getReceiveAddress());
        frontOrdersReceiveInfo.setReceiveAddressDetail(frontOrders.getReceiveAddressDetail());
        frontOrdersReceiveInfo.setUserRemark(frontOrders.getUserRemark());
        frontOrdersReceiveInfo.setPlatformRemark(frontOrders.getPlatRemark());
        return frontOrdersReceiveInfo;
    }

    /**
     * 获取订单付款明细
     * @param frontOrders
     * @return
     */
    private static FrontOrdersVo.FrontOrdersPayInfo getFrontOrdersPayInfo(FrontOrders frontOrders) {
        FrontOrdersVo.FrontOrdersPayInfo frontOrdersPayInfo = new FrontOrdersVo.FrontOrdersPayInfo();
        frontOrdersPayInfo.setGoodsTotal(frontOrders.getTotalPrice());
        frontOrdersPayInfo.setIntegral(frontOrders.getPointsDeduction());
        frontOrdersPayInfo.setCard(frontOrders.getGiftDeduction());
        frontOrdersPayInfo.setCoupon(frontOrders.getCouponDeduction());
        frontOrdersPayInfo.setSubtotal(frontOrders.getTotalPrice().subtract(frontOrders.getPointsDeduction() == null ? new BigDecimal(0) : frontOrders.getPointsDeduction()).subtract(frontOrders.getGiftDeduction() == null ? new BigDecimal(0) : frontOrders.getGiftDeduction()).subtract(frontOrders.getCouponDeduction() == null ? new BigDecimal(0) : frontOrders.getCouponDeduction()) );
        frontOrdersPayInfo.setPayAmount(frontOrders.getPayPrice());
        return frontOrdersPayInfo;
    }

    @Override
    public boolean refund(StoreOrderRefundRequest request) {
        FrontOrders orders = getInfoException(request.getOrderNo());
        if (!orders.getPaid()) {
            throw new GlobalException("未支付无法退款");
        }
        if (request.getAmount().compareTo(orders.getPayPrice()) > 0) {
            throw new GlobalException("退款金额大于支付金额，请修改退款金额");
        }
        if (request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            if (orders.getPayPrice().compareTo(BigDecimal.ZERO) != 0) {
                throw new GlobalException("退款金额不能为0，请修改退款金额");
            }
        }
        request.setOrderId(orders.getId());
        //退款
        if (orders.getPayType().equals(PayConstants.PAY_TYPE_WE_CHAT) && request.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            try {
                storeOrderRefundService.refund(request, orders);
            } catch (Exception e) {
                e.printStackTrace();
                throw new GlobalException("微信申请退款失败！");
            }
        }
        //这里只做修改状态 积分 余额 优惠卷 等都放到redis里处理 微信是不走redis到 微信支付需要回调后再添加到task里
        orders.setStatus(6);//售后订单状态 //待退款
        Boolean execute = transactionTemplate.execute(e -> {
            dao.updateFrontOrders( orders);
            // 退款task
            redisUtil.lPush(PayConstants.ORDER_TASK_REDIS_KEY_AFTER_REFUND_BY_USER, orders);
            return Boolean.TRUE;
        });
        return Boolean.TRUE.equals(execute);
    }


    private FrontOrders getInfoException(String orderNo) {
        LambdaQueryWrapper<FrontOrders> lqw = Wrappers.lambdaQuery();
        lqw.eq(FrontOrders::getOrderNumber, orderNo);
        FrontOrders storeOrder = dao.selectOne(lqw);
        if (ObjectUtil.isNull(storeOrder)) {
            throw new GlobalException("没有找到订单信息");
        }
        return storeOrder;
    }
}
