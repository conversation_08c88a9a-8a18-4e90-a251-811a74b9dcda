package com.ruoyi.store.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.FrontEvaluate;
import com.ruoyi.system.api.domain.FrontGategory;
import com.ruoyi.system.api.domain.FrontGoods;
import com.ruoyi.system.api.domain.vo.FrontEvaluateVo;
import com.ruoyi.system.api.mapper.FrontEvaluateMapper;
import com.ruoyi.system.api.mapper.FrontGategoryMapper;
import com.ruoyi.store.mapper.FrontGoodsMapper;
import com.ruoyi.store.service.IFrontEvaluateService;
import com.ruoyi.system.api.oss.OssUrlCleanerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName FrontEvaluateServiceImpl
 * @Description 评价管理业务实现类
 * <AUTHOR>
 * @Date 2025/5/20 下午4:55
 */
@Slf4j
@Service
public class FrontEvaluateServiceImpl extends ServiceImpl<FrontEvaluateMapper, FrontEvaluate> implements IFrontEvaluateService {

    @Autowired
    private FrontGoodsMapper frontGoodsMapper;

    @Autowired
    private FrontGategoryMapper frontGategoryMapper;

    @Autowired
    private FrontEvaluateMapper frontEvaluateMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    @Override
    public FrontEvaluateVo.FrontEvaluateDetail getEvaluateDetail(FrontEvaluateVo.FrontEvaluateSearch vo) {
        // 存商品id
        List<Long> ids = null;

        // 查询商品详情
        if (vo.getKeyword() != null) {
            LambdaQueryWrapper<FrontGoods> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(FrontGoods::getName, vo.getKeyword());
            List<FrontGoods> frontGoods = frontGoodsMapper.selectList(wrapper);
            ids = frontGoods.stream().map(FrontGoods::getId).collect(Collectors.toList());
        }

        // 查询分类信息
        if (vo.getCategoryId() != null) {
            FrontGategory frontGategory = frontGategoryMapper.selectById(vo.getCategoryId());
            if (frontGategory != null) {
                List<FrontGoods> frontGoods = frontGoodsMapper.selectList(
                        new LambdaQueryWrapper<FrontGoods>()
                                .eq(FrontGoods::getGategoryId, frontGategory.getId())
                );
                // Merge with existing ids if any
                List<Long> categoryIds = frontGoods.stream()
                        .map(FrontGoods::getId)
                        .collect(Collectors.toList());
                if (ids == null) {
                    ids = categoryIds;
                } else {
                    ids.addAll(categoryIds);
                    ids = ids.stream().distinct().collect(Collectors.toList());
                }
            }
        }

        FrontEvaluateVo.FrontEvaluateDetail frontEvaluateDetail = new FrontEvaluateVo.FrontEvaluateDetail();

        // Base query wrapper
        LambdaQueryWrapper<FrontEvaluate> baseWrapper = new LambdaQueryWrapper<FrontEvaluate>()
                .eq(FrontEvaluate::getIsReply, 0);

        if (ids != null) {
            baseWrapper.in(FrontEvaluate::getGoodsId, ids);
        }

        // 查询待回复评价表详情（好评 中评 差评）
        frontEvaluateDetail.setReplyGood(this.count(
                new LambdaQueryWrapper<FrontEvaluate>(baseWrapper.getEntityClass())
                        .eq(FrontEvaluate::getType, 0)
        ));
        frontEvaluateDetail.setReplyMiddle(this.count(
                new LambdaQueryWrapper<FrontEvaluate>(baseWrapper.getEntityClass())
                        .eq(FrontEvaluate::getType, 1)
        ));
        frontEvaluateDetail.setReplyBad(this.count(
                new LambdaQueryWrapper<FrontEvaluate>(baseWrapper.getEntityClass())
                        .eq(FrontEvaluate::getType, 2)
        ));

        // 获取当天时间范围
        LocalDateTime startOfDay = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime endOfDay = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);

        // 查询今日新增待回复数量
        frontEvaluateDetail.setReplyGoodNew(this.count(
                new LambdaQueryWrapper<FrontEvaluate>(baseWrapper.getEntityClass())
                        .eq(FrontEvaluate::getType, 0)
                        .ge(FrontEvaluate::getTickTime, startOfDay)
                        .lt(FrontEvaluate::getTickTime, endOfDay)
        ));
        frontEvaluateDetail.setReplyMiddleNew(this.count(
                new LambdaQueryWrapper<FrontEvaluate>(baseWrapper.getEntityClass())
                        .eq(FrontEvaluate::getType, 1)
                        .ge(FrontEvaluate::getTickTime, startOfDay)
                        .lt(FrontEvaluate::getTickTime, endOfDay)
        ));
        frontEvaluateDetail.setReplyBadNew(this.count(
                new LambdaQueryWrapper<FrontEvaluate>(baseWrapper.getEntityClass())
                        .eq(FrontEvaluate::getType, 2)
                        .ge(FrontEvaluate::getTickTime, startOfDay)
                        .lt(FrontEvaluate::getTickTime, endOfDay)
        ));

        // 时间范围计算
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.minusDays(30).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime startTimeSeven = now.minusDays(7).withHour(0).withMinute(0).withSecond(0);

        // 30天评价统计
        LambdaQueryWrapper<FrontEvaluate> thirtyDayWrapper = new LambdaQueryWrapper<FrontEvaluate>()
                .ne(FrontEvaluate::getIsDel, 0)
                .ge(FrontEvaluate::getTickTime, startTime)
                .le(FrontEvaluate::getTickTime, now);

        if (ids != null) {
            thirtyDayWrapper.in(FrontEvaluate::getGoodsId, ids);
        }

        long totalCount = this.count(thirtyDayWrapper);
        long goodCount = this.count(new LambdaQueryWrapper<FrontEvaluate>(thirtyDayWrapper.getEntityClass()).eq(FrontEvaluate::getType, 0));
        long middleCount = this.count(new LambdaQueryWrapper<FrontEvaluate>(thirtyDayWrapper.getEntityClass()).eq(FrontEvaluate::getType, 1));
        long badCount = this.count(new LambdaQueryWrapper<FrontEvaluate>(thirtyDayWrapper.getEntityClass()).eq(FrontEvaluate::getType, 2));

        // 设置30天评价率
        if (totalCount > 0) {
            frontEvaluateDetail.setReplyGoodRate(BigDecimal.valueOf(goodCount * 100.0 / totalCount));
            frontEvaluateDetail.setReplyMiddleRate(BigDecimal.valueOf(middleCount * 100.0 / totalCount));
            frontEvaluateDetail.setReplyBadRate(BigDecimal.valueOf(badCount * 100.0 / totalCount));
        } else {
            frontEvaluateDetail.setReplyGoodRate(BigDecimal.ZERO);
            frontEvaluateDetail.setReplyMiddleRate(BigDecimal.ZERO);
            frontEvaluateDetail.setReplyBadRate(BigDecimal.ZERO);
        }

        // 7天评价统计
        LambdaQueryWrapper<FrontEvaluate> sevenDayWrapper = new LambdaQueryWrapper<FrontEvaluate>()
                .ne(FrontEvaluate::getIsDel, 0)
                .ge(FrontEvaluate::getTickTime, startTimeSeven)
                .le(FrontEvaluate::getTickTime, now);

        if (ids != null) {
            sevenDayWrapper.in(FrontEvaluate::getGoodsId, ids);
        }

        long totalCountSeven = this.count(sevenDayWrapper);
        long goodCountSeven = this.count(new LambdaQueryWrapper<FrontEvaluate>(sevenDayWrapper.getEntityClass()).eq(FrontEvaluate::getType, 0));
        long middleCountSeven = this.count(new LambdaQueryWrapper<FrontEvaluate>(sevenDayWrapper.getEntityClass()).eq(FrontEvaluate::getType, 1));
        long badCountSeven = this.count(new LambdaQueryWrapper<FrontEvaluate>(sevenDayWrapper.getEntityClass()).eq(FrontEvaluate::getType, 2));

        // 设置7天评价率
        if (totalCountSeven > 0) {
            frontEvaluateDetail.setReplyGoodRateSeven(BigDecimal.valueOf(goodCountSeven * 100.0 / totalCountSeven));
            frontEvaluateDetail.setReplyMiddleRateSeven(BigDecimal.valueOf(middleCountSeven * 100.0 / totalCountSeven));
            frontEvaluateDetail.setReplyBadRateSeven(BigDecimal.valueOf(badCountSeven * 100.0 / totalCountSeven));
        } else {
            frontEvaluateDetail.setReplyGoodRateSeven(BigDecimal.ZERO);
            frontEvaluateDetail.setReplyMiddleRateSeven(BigDecimal.ZERO);
            frontEvaluateDetail.setReplyBadRateSeven(BigDecimal.ZERO);
        }

        return frontEvaluateDetail;
    }

    @Override
    public FrontEvaluateVo.FrontEvaluateDetailInfo getEvaluateDetailInfo(FrontEvaluateVo.FrontEvaluateDetailSearch vo) {
        FrontEvaluateVo.FrontEvaluateDetailInfo frontEvaluateDetailInfo = frontEvaluateMapper.selectGoodsEvaluateDetail(vo);
        if (frontEvaluateDetailInfo == null){
            return null;
        }
        FrontEvaluateVo.FrontEvaluateSearch frontEvaluateSearch = new FrontEvaluateVo.FrontEvaluateSearch();
        frontEvaluateSearch.setKeyword(frontEvaluateDetailInfo.getName());
        FrontEvaluateVo.FrontEvaluateDetail evaluateDetail = getEvaluateDetail(frontEvaluateSearch);
        BeanUtils.copyProperties(evaluateDetail, frontEvaluateDetailInfo);
        frontEvaluateDetailInfo.setFirstPic(ossUrlCleanerUtil.getSignatureUrl(frontEvaluateDetailInfo.getFirstPic()));
        return frontEvaluateDetailInfo;
    }

    @Override
    public List<FrontEvaluateVo.FrontEvaluateList> getEvaluateList(FrontEvaluateVo.FrontEvaluateSearch vo) {
        List<FrontEvaluateVo.FrontEvaluateList> frontEvaluateLists = frontEvaluateMapper.selectEvaluateList(vo);
        frontEvaluateLists.forEach(item  -> {
            item.setFirstPic(ossUrlCleanerUtil.getSignatureUrl(item.getFirstPic()));
        });
        return frontEvaluateLists;
    }

    @Override
    public List<FrontEvaluate> getList(FrontEvaluateVo.FrontEvaluateDetailSearch vo) {
        LambdaQueryWrapper<FrontEvaluate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontEvaluate::getGoodsId, vo.getGoodsId())
                .eq(FrontEvaluate::getIsDel, 0);

        if (vo.getEvaluateId() != null){
            queryWrapper.like(FrontEvaluate::getId, vo.getEvaluateId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public List<FrontEvaluate> getStatusList(FrontEvaluateVo.FrontEvaluateDetailSearch vo) {
        LambdaQueryWrapper<FrontEvaluate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontEvaluate::getType, vo.getStatus());

        if (vo.getKeyword() != null){
            queryWrapper.like(FrontEvaluate::getContent, vo.getKeyword());
        }

        if (vo.getGoodsId() != null){
            queryWrapper.eq(FrontEvaluate::getGoodsId, vo.getGoodsId());
        }

        return this.list(queryWrapper);
    }

    @Override
    public Boolean updateEvaluate(FrontEvaluate frontEvaluate) {
        frontEvaluate.setIsReply(1);
        frontEvaluate.setUpdateTime(LocalDateTime.now());
        frontEvaluate.setReplyTime(LocalDateTime.now());
        return this.updateById(frontEvaluate);
    }

    @Override
    public Boolean updateIsRefine(Long id) {
        FrontEvaluate byId = this.getById(id);
        byId.setIsRefine(1);
        byId.setUpdateTime(LocalDateTime.now());
        return this.updateById(byId);
    }

    @Override
    public Boolean updateIsShow(Long id) {
        FrontEvaluate byId = this.getById(id);
        byId.setIsShow(byId.getIsShow() == 0 ? 1 : 0);
        byId.setUpdateTime(LocalDateTime.now());
        return this.updateById(byId);
    }

    @Override
    @Transactional
    public void deleteEvaluateByIds(Long[] ids) {
        for (Long id : ids) {
            this.removeById(id);
        }
    }

    @Override
    @Transactional
    public void isShow(Long[] ids) {
        for (Long id : ids) {
            FrontEvaluate frontEvaluate = this.getById(id);
            frontEvaluate.setIsShow(frontEvaluate.getIsShow() == 0 ? 1 : 0);
            this.updateById(frontEvaluate);
        }
    }
}
