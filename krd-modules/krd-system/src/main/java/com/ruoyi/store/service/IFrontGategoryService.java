package com.ruoyi.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.FrontGategory;
import com.ruoyi.store.domain.vo.FrontGategoryVo;
import com.ruoyi.system.domain.vo.TreeSelect;

import java.util.List;

/**
 * @ClassName IFrontGategoryService
 * @Description 商品分类业务层
 * <AUTHOR>
 * @Date 2025/5/20 上午11:52
 */
public interface IFrontGategoryService extends IService<FrontGategory> {

    /**
     * 查询商品分类列表
     * @param vo
     * @return
     */
    List<FrontGategory> getGategoryList(FrontGategoryVo.FrontGategorySearch vo);

    /**
     * 查询全部顶级分类
     * @return
     */
    List<FrontGategory> getAllTopGategory();

    /**
     * 获取子分类
     * @return
     */
    List<FrontGategory> getChildrenList();

    /**
     * 添加商品分类
     * @param frontGategory
     * @return
     */
    Boolean addGategory(FrontGategory frontGategory);

    /**
     * 修改商品分类
     * @param frontGategory
     * @return
     */
    Boolean updateGategory(FrontGategory frontGategory);

    /**
     * 删除商品分类
     * @param ids
     */
    void deleteGategory(Long[] ids);

    /**
     * 移除分类下对应所有的商品
     * @param
     * @return
     */
    Boolean deleteGategoryGoods(Long id);

    /**
     * 修改商品分类排序
     * @param vo
     * @return
     */
    Boolean updateGategorySort(FrontGategoryVo.FrontGategorySort vo);

    /**
     * 修改商品分类状态
     * @param vo
     * @return
     */
    Boolean updateGategoryStatus(FrontGategoryVo.FrontGategoryStatus vo);

    /**
     * 商品分类树
     * @param vo
     * @return
     */
    List<TreeSelect> selectGategoryTreeList(FrontGategoryVo.FrontGategorySearch vo);

    /**
     * 获取分类详情
     * @param id
     * @return
     */
    FrontGategory getGategoryById(Long id);
}
