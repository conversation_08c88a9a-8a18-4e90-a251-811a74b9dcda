package com.ruoyi.store.service.Impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.store.service.IFrontCouponTypeService;
import com.ruoyi.system.api.domain.FrontCouponType;
import com.ruoyi.system.api.mapper.FrontCouponTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-30
 */
@Service
public class FrontCouponTypeServiceImpl implements IFrontCouponTypeService
{
    @Autowired
    private FrontCouponTypeMapper frontCouponTypeMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public FrontCouponType selectFrontCouponTypeById(Long id)
    {
        return frontCouponTypeMapper.selectFrontCouponTypeById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param frontCouponType 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<FrontCouponType> selectFrontCouponTypeList(FrontCouponType frontCouponType)
    {
        return frontCouponTypeMapper.selectList(null);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param frontCouponType 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertFrontCouponType(FrontCouponType frontCouponType)
    {
        frontCouponType.setCreateTime(DateUtils.getLocalDateTime());
        return frontCouponTypeMapper.insert(frontCouponType);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param frontCouponType 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateFrontCouponType(FrontCouponType frontCouponType)
    {
        return frontCouponTypeMapper.updateFrontCouponType(frontCouponType);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteFrontCouponTypeByIds(Long[] ids)
    {
        return frontCouponTypeMapper.deleteFrontCouponTypeByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteFrontCouponTypeById(Long id)
    {
        return frontCouponTypeMapper.deleteById(id);
    }
}
