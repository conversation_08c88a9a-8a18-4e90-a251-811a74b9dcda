package com.ruoyi.store.domain.res;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: suhai
 * @Date: 2025/6/19
 * @Description: 订单退款表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StoreOrderRefundRequest implements Serializable {
    private static final long serialVersionUID=1L;

    @Schema(name = "订单编号")
    @NotBlank(message = "订单编号不能为空")
    private String orderNo;

    @Schema(name = "退款金额")
    @DecimalMin(value = "0.00", message = "退款金额不能少于0.00")
    private BigDecimal amount;

    @Schema(name="订单id")
    private Long orderId;

    @Schema(name="余额支付")
    private BigDecimal useYue;
}
