package com.ruoyi.store.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontEvaluate;
import com.ruoyi.system.api.domain.vo.FrontEvaluateVo;
import com.ruoyi.store.service.IFrontEvaluateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName FrontEvaluateController
 * @Description 评价管理 控制层
 * <AUTHOR>
 * @Date 2025/5/20 下午4:50
 */
@Slf4j
@Tag(name = "评价管理", description = "后台商城管理——评价管理")
@RestController
@RequestMapping("/frontEvaluate")
public class FrontEvaluateController extends BaseController {

    @Autowired
    private IFrontEvaluateService frontEvaluateService;

    @RequiresPermissions("front:evaluate:list")
    @Operation(description = "评价列表")
    @GetMapping("/list")
    public TableDataInfo list(FrontEvaluateVo.FrontEvaluateSearch vo) {
        startPage();
        List<FrontEvaluateVo.FrontEvaluateList> evaluateList = frontEvaluateService.getEvaluateList(vo);
        return getDataTable(evaluateList);
    }

    @RequiresPermissions("front:evaluate:detailList")
    @Operation(description = "评价详情列表")
    @GetMapping("/detail/list")
    public TableDataInfo detailList(FrontEvaluateVo.FrontEvaluateDetailSearch vo) {
        startPage();
        return getDataTable(frontEvaluateService.getList(vo));
    }

    @RequiresPermissions("front:evaluate:detailList")
    @Operation(description = "对应商品评价统计数据")
    @GetMapping("/detail/goods/info")
    public AjaxResult detailInfo(FrontEvaluateVo.FrontEvaluateDetailSearch vo) {
        return success(frontEvaluateService.getEvaluateDetailInfo(vo));
    }

    @RequiresPermissions("front:evaluate:list")
    @Operation(description = "评价详情")
    @GetMapping("/detail/info")
    public AjaxResult info(FrontEvaluateVo.FrontEvaluateSearch vo) {
        return success(frontEvaluateService.getEvaluateDetail(vo));
    }

    @Operation(description = "获取对应状态评价详情列表")
    @RequiresPermissions("front:evaluate:detailList")
    @GetMapping("/detail/status/list")
    public TableDataInfo detailStatusList(FrontEvaluateVo.FrontEvaluateDetailSearch vo) {
        startPage();
        return getDataTable(frontEvaluateService.getStatusList(vo));
    }

    @Operation(description = "回复评价")
    @RequiresPermissions("front:evaluate:reply")
    @PostMapping("/reply")
    public AjaxResult reply(@RequestBody FrontEvaluate frontEvaluate) {
        return toAjax(frontEvaluateService.updateEvaluate(frontEvaluate));
    }

    @Operation(description = "评论加精")
    @RequiresPermissions("front:evaluate:refine")
    @PostMapping("/refine/{id}")
    public AjaxResult refine(@PathVariable("id") Long id) {
        return toAjax(frontEvaluateService.updateIsRefine(id));
    }

    @Operation(description = "评价内容显示隐藏")
    @RequiresPermissions("front:evaluate:show")
    @PostMapping("/show/{id}")
    public AjaxResult show(@PathVariable("id") Long id) {
        return toAjax(frontEvaluateService.updateIsShow(id));
    }

    @Operation(description = "批量删除")
    @RequiresPermissions("front:evaluate:remove")
    @DeleteMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable("ids") Long[] ids) {
        frontEvaluateService.deleteEvaluateByIds(ids);
        return success();
    }

    @Operation(description = "批量隐藏")
    @RequiresPermissions("front:evaluate:isShow")
    @PostMapping("/isShow/{ids}")
    public AjaxResult isShow(@PathVariable("ids") Long[] ids) {
        frontEvaluateService.isShow(ids);
        return success();
    }


}
