package com.ruoyi.store.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.store.service.StoreErpService;
import com.ruoyi.system.api.domain.FrontGategory;
import com.ruoyi.system.api.domain.FrontGoods;
import com.ruoyi.store.domain.vo.FrontGategoryVo;
import com.ruoyi.system.api.mapper.FrontGategoryMapper;
import com.ruoyi.store.mapper.FrontGoodsMapper;
import com.ruoyi.store.service.IFrontGategoryService;
import com.ruoyi.system.api.oss.OssUrlCleanerUtil;
import com.ruoyi.system.domain.vo.TreeSelect;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName FrontGategoryServiceImpl
 * @Description 后台商品分类业务实现类
 * <AUTHOR>
 * @Date 2025/5/20 上午11:53
 */
@Slf4j
@Service
public class FrontGategoryServiceImpl extends ServiceImpl<FrontGategoryMapper, FrontGategory> implements IFrontGategoryService {

    @Autowired
    private FrontGoodsMapper frontGoodsMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    @Autowired
    private StoreErpService storeErpService;

    @Override
    public List<FrontGategory> getGategoryList(FrontGategoryVo.FrontGategorySearch vo) {
        LambdaQueryWrapper<FrontGategory> queryWrapper = new LambdaQueryWrapper<>();

        if (vo.getId() != null){
            queryWrapper.eq(FrontGategory::getId, vo.getId()).or().eq(FrontGategory::getParentId, vo.getId());
        }

        if (vo.getTitle() != null){
            queryWrapper.like(FrontGategory::getTitle, vo.getTitle());
        }
        List<FrontGategory> list = this.list(queryWrapper);
        if (!list.isEmpty()){
            // 遍历list
            list.forEach(frontGategory -> {
                // 获取分类id
                Long id = frontGategory.getId();
                // 获取分类下的商品列表
                List<FrontGoods> frontGoods = frontGoodsMapper.selectList(new LambdaQueryWrapper<FrontGoods>().eq(FrontGoods::getGategoryId, id));

                // 获取frontGoods商品库存相加
                frontGategory.setNum(frontGoods.size());

                FrontGategory byId = this.getById(frontGategory.getParentId());

                if (byId != null){
                    frontGategory.setParentTitle(byId.getTitle());
                }else {
                    frontGategory.setParentTitle("顶级分类");
                }

                frontGategory.setIsSecond(frontGategory.getParentId() != 0);

            });
        }
        return list;
    }

    @Override
    public List<FrontGategory> getAllTopGategory() {
        return this.list(new LambdaQueryWrapper<FrontGategory>().eq(FrontGategory::getParentId, 0));
    }

    @Override
    public List<FrontGategory> getChildrenList() {
        return this.list(new LambdaQueryWrapper<FrontGategory>().ne(FrontGategory::getParentId, 0));
    }

    @Override
    @Transactional
    public Boolean addGategory(FrontGategory frontGategory) {
        frontGategory.setIcon(ossUrlCleanerUtil.cleanUrlsToString(frontGategory.getIcon()));
        frontGategory.setCreateTime(LocalDateTime.now());
        frontGategory.setCreateBy(tokenService.getLoginUser().getUsername());
        this.save(frontGategory);
        try {
            // 分类推送
            storeErpService.pushGategoryToErp(frontGategory);
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Boolean updateGategory(FrontGategory frontGategory) {
        frontGategory.setIcon(ossUrlCleanerUtil.cleanUrlsToString(frontGategory.getIcon()));
        frontGategory.setUpdateTime(LocalDateTime.now());
        frontGategory.setUpdateBy(tokenService.getLoginUser().getUsername());
        this.updateById(frontGategory);
        try {
            // 分类推送
            storeErpService.pushGategoryToErp(frontGategory);
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional
    public void deleteGategory(Long[] ids) {
        for (Long id : ids) {
            // 查询该分类是否为点击分类
            FrontGategory gategory = this.getById(id);
            if (gategory != null && gategory.getParentId() == 0){
                // 查询该分类下是否存在子分类
                List<FrontGategory> list = this.list(new LambdaQueryWrapper<FrontGategory>().eq(FrontGategory::getParentId, id));
                if (!list.isEmpty()){
                    throw new RuntimeException("分类ID：" + id + "存在子分类"+ list.size() +"个，请先移除字分类再删除！");
                }
            }
            // 查询该分类下面是否存在商品
            if (frontGoodsMapper.selectCount(new LambdaQueryWrapper<FrontGoods>().eq(FrontGoods::getGategoryId, id)) > 0){
                throw new RuntimeException("分类ID：" + id + "存在商品，不允许删除！");
            }
            this.removeById(id);
        }
    }

    @Override
    public Boolean deleteGategoryGoods(Long id) {
        // 删除商品分类id对应的商品
        LambdaQueryWrapper<FrontGoods> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FrontGoods::getGategoryId, id);
        return frontGoodsMapper.delete(wrapper) > 0;
    }

    @Override
    public Boolean updateGategorySort(FrontGategoryVo.FrontGategorySort vo) {
        FrontGategory frontGategory = new FrontGategory();
        frontGategory.setId(vo.getId());
        frontGategory.setSort(vo.getSort());
        frontGategory.setUpdateTime(LocalDateTime.now());
        frontGategory.setUpdateBy(tokenService.getLoginUser().getUsername());
        return this.updateById(frontGategory);
    }

    @Override
    public Boolean updateGategoryStatus(FrontGategoryVo.FrontGategoryStatus vo) {
        FrontGategory frontGategory = new FrontGategory();
        frontGategory.setId(vo.getId());
        frontGategory.setIsShow(vo.getIsShow());
        frontGategory.setUpdateTime(LocalDateTime.now());
        frontGategory.setUpdateBy(tokenService.getLoginUser().getUsername());
        return this.updateById(frontGategory);
    }

    @Override
    public List<TreeSelect> selectGategoryTreeList(FrontGategoryVo.FrontGategorySearch vo) {
        if (vo.getId() != null){
            LambdaQueryWrapper<FrontGategory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FrontGategory::getId, vo.getId()).or().eq(FrontGategory::getParentId, vo.getId());
            List<FrontGategory> depts = SpringUtils.getAopProxy(this).list(queryWrapper);
            return buildFrontGategoryTreeSelect(depts);
        }
        if (StringUtils.isNotNull(vo.getTitle())){
            LambdaQueryWrapper<FrontGategory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(FrontGategory::getTitle, vo.getTitle()).or().like(FrontGategory::getId, vo.getTitle());
            List<FrontGategory> depts = SpringUtils.getAopProxy(this).list(queryWrapper);
            return buildFrontGategoryTreeSelect(depts);
        }
        List<FrontGategory> depts = SpringUtils.getAopProxy(this).list();
        return buildFrontGategoryTreeSelect(depts);
    }

    @Override
    public FrontGategory getGategoryById(Long id) {
        FrontGategory byId = this.getById(id);
        if (byId != null){
            byId.setIcon(ossUrlCleanerUtil.getSignatureUrl(byId.getIcon()));
        }
        return byId;
    }


    private List<TreeSelect> buildFrontGategoryTreeSelect(List<FrontGategory> frontGategories)
    {
        List<FrontGategory> frontGategoryList = buildFrontGategoryTree(frontGategories);
        return frontGategoryList.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    private void recursionFn(List<FrontGategory> frontGategories, FrontGategory t)
    {
        // 得到子节点列表
        List<FrontGategory> childList = getChildList(frontGategories, t);
        t.setChildren(childList);
        for (FrontGategory tChild : childList)
        {
            if (hasChild(frontGategories, tChild))
            {
                recursionFn(frontGategories, tChild);
            }
        }
    }

    private List<FrontGategory> buildFrontGategoryTree(List<FrontGategory> frontGategories)
    {
        List<FrontGategory> returnList = new ArrayList<FrontGategory>();
        List<Long> tempList = frontGategories.stream().map(FrontGategory::getId).collect(Collectors.toList());
        for (FrontGategory frontGategory : frontGategories)
        {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(frontGategory.getParentId()))
            {
                recursionFn(frontGategories, frontGategory);
                returnList.add(frontGategory);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = frontGategories;
        }
        return returnList;
    }

    /**
     * 得到子节点列表
     */
    private List<FrontGategory> getChildList(List<FrontGategory> list, FrontGategory t)
    {
        List<FrontGategory> tlist = new ArrayList<FrontGategory>();
        Iterator<FrontGategory> it = list.iterator();
        while (it.hasNext())
        {
            FrontGategory n = (FrontGategory) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<FrontGategory> list, FrontGategory t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
