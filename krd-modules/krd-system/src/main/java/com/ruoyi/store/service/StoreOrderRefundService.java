package com.ruoyi.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.store.domain.res.StoreOrderRefundRequest;
import com.ruoyi.system.api.domain.FrontOrders;

/**
 * @Author: suhai
 * @Date: 2025/6/19
 * @Description: 退货单Service接口
 */
public interface StoreOrderRefundService extends IService<FrontOrders> {

    void refund(StoreOrderRefundRequest request, FrontOrders storeOrder);
}
