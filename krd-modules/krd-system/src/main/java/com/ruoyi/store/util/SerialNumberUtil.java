package com.ruoyi.store.util;

import java.sql.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @ClassName SerialNumberUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/23 下午4:40
 */
public class SerialNumberUtil {

    private static final String DB_URL = "jdbc:mysql://***************:3306/jx";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "BatenMysql123321";
    private static final Lock lock = new ReentrantLock();

    /**
     * 生成流水号（格式：分类-0001）
     * @param category 分类标识（如：ORDER、USER）
     * @param digits 序号位数（如：4 → 0001）
     * @return 流水号
     */
    public static String generate(String category, int digits) {
        lock.lock(); // 保证线程安全
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            conn.setAutoCommit(false);

            // 查询当前序号（加行锁）
            int currentValue = getCurrentValue(conn, category);

            // 生成新序号并更新
            int newValue = currentValue + 1;
            updateCurrentValue(conn, category, newValue);

            conn.commit();
            return formatSerialNumber(category, newValue, digits);
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException("生成流水号失败");
        } finally {
            lock.unlock();
        }
    }

    // 查询当前序号（使用 SELECT ... FOR UPDATE 锁定行）
    private static int getCurrentValue(Connection conn, String category) throws SQLException {
        String sql = "SELECT current_value FROM front_serial_number WHERE category = ? FOR UPDATE";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, category);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt("current_value");
            } else {
                // 初始化分类
                String insertSql = "INSERT INTO front_serial_number (category, current_value) VALUES (?, 0)";
                try (PreparedStatement insertStmt = conn.prepareStatement(insertSql)) {
                    insertStmt.setString(1, category);
                    insertStmt.executeUpdate();
                }
                return 0;
            }
        }
    }

    // 更新序号
    private static void updateCurrentValue(Connection conn, String category, int newValue) throws SQLException {
        String sql = "UPDATE front_serial_number SET current_value = ? WHERE category = ?";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, newValue);
            stmt.setString(2, category);
            stmt.executeUpdate();
        }
    }

    // 格式化流水号（示例：ORDER-0001）
    private static String formatSerialNumber(String category, int value, int digits) {
        return String.format("%s-%0" + digits + "d", category, value);
    }

    // 测试
    public static void main(String[] args) {
        System.out.println(generate("ORDER", 4));  // 输出：ORDER-0001
        System.out.println(generate("USER", 4));   // 输出：USER-0001
        System.out.println(generate("ORDER", 4));  // 输出：ORDER-0002
    }
}
