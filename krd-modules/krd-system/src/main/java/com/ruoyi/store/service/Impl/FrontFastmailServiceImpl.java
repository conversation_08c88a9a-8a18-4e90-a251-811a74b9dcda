package com.ruoyi.store.service.Impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.store.service.IFrontFastmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.api.mapper.FrontFastmailMapper;
import com.ruoyi.system.api.domain.FrontFastmail;

/**
 * 快递单管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@Service
public class FrontFastmailServiceImpl implements IFrontFastmailService
{
    @Autowired
    private FrontFastmailMapper frontFastmailMapper;

    /**
     * 查询快递单管理
     * 
     * @param id 快递单管理主键
     * @return 快递单管理
     */
    @Override
    public FrontFastmail selectFrontFastmailById(Long id)
    {
        return frontFastmailMapper.selectFrontFastmailById(id);
    }

    /**
     * 查询快递单管理列表
     * 
     * @param frontFastmail 快递单管理
     * @return 快递单管理
     */
    @Override
    public List<FrontFastmail> selectFrontFastmailList(FrontFastmail frontFastmail)
    {
        return frontFastmailMapper.selectFrontFastmailList(frontFastmail);
    }

    /**
     * 新增快递单管理
     * 
     * @param frontFastmail 快递单管理
     * @return 结果
     */
    @Override
    public int insertFrontFastmail(FrontFastmail frontFastmail)
    {
        frontFastmail.setCreateTime(DateUtils.getLocalDateTime());
        return frontFastmailMapper.insertFrontFastmail(frontFastmail);
    }

    /**
     * 修改快递单管理
     * 
     * @param frontFastmail 快递单管理
     * @return 结果
     */
    @Override
    public int updateFrontFastmail(FrontFastmail frontFastmail)
    {
        frontFastmail.setUpdateTime(DateUtils.getLocalDateTime());
        return frontFastmailMapper.updateFrontFastmail(frontFastmail);
    }

    /**
     * 批量删除快递单管理
     * 
     * @param ids 需要删除的快递单管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontFastmailByIds(Long[] ids)
    {
        return frontFastmailMapper.deleteFrontFastmailByIds(ids);
    }

    /**
     * 删除快递单管理信息
     * 
     * @param id 快递单管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontFastmailById(Long id)
    {
        return frontFastmailMapper.deleteFrontFastmailById(id);
    }

    @Override
    public Boolean updateFrontFastmailStatus(Long id) {
        FrontFastmail frontFastmail = new FrontFastmail();
        frontFastmail.setId(id);
        frontFastmail.setStatus(5L);
        return frontFastmailMapper.updateById(frontFastmail) > 0;
    }
}
