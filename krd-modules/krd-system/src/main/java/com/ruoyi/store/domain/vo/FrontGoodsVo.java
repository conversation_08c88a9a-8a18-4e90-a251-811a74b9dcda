package com.ruoyi.store.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName FrontGoodsVo
 * @Description 商品管理参数
 * <AUTHOR>
 * @Date 2025/5/23 上午11:09
 */
public interface FrontGoodsVo {

    @Data
    @Schema(description = "商城搜索参数")
    class SearchParam {
        @Schema(name = "id/商品信息")
        private String keyword;
        @Schema(name = "商品类型 0-检测套餐 1-商城商品")
        private String goodsType;
        @Schema(name = "商品分类")
        private Long categoryId;
    }

}
