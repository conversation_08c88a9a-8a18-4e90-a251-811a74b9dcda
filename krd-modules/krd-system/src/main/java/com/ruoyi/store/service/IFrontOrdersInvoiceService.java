package com.ruoyi.store.service;

import java.util.List;
import com.ruoyi.system.api.domain.FrontOrdersInvoice;

/**
 * 订单-发票信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
public interface IFrontOrdersInvoiceService
{
    /**
     * 查询订单-发票信息
     *
     * @param id 订单-发票信息主键
     * @return 订单-发票信息
     */
    public FrontOrdersInvoice selectFrontOrdersInvoiceById(Long id);

    /**
     * 查询订单-发票信息列表
     *
     * @param frontOrdersInvoice 订单-发票信息
     * @return 订单-发票信息集合
     */
    public List<FrontOrdersInvoice> selectFrontOrdersInvoiceList(FrontOrdersInvoice frontOrdersInvoice);

    /**
     * 新增订单-发票信息
     *
     * @param frontOrdersInvoice 订单-发票信息
     * @return 结果
     */
    public int insertFrontOrdersInvoice(FrontOrdersInvoice frontOrdersInvoice);

    /**
     * 修改订单-发票信息
     *
     * @param frontOrdersInvoice 订单-发票信息
     * @return 结果
     */
    public int updateFrontOrdersInvoice(FrontOrdersInvoice frontOrdersInvoice);

    /**
     * 批量删除订单-发票信息
     *
     * @param ids 需要删除的订单-发票信息主键集合
     * @return 结果
     */
    public int deleteFrontOrdersInvoiceByIds(Long[] ids);

    /**
     * 删除订单-发票信息信息
     *
     * @param id 订单-发票信息主键
     * @return 结果
     */
    public int deleteFrontOrdersInvoiceById(Long id);
}
