package com.ruoyi.store.service;

import java.util.List;
import com.ruoyi.system.api.domain.FrontFastmail;

/**
 * 快递单管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
public interface IFrontFastmailService 
{
    /**
     * 查询快递单管理
     * 
     * @param id 快递单管理主键
     * @return 快递单管理
     */
    public FrontFastmail selectFrontFastmailById(Long id);

    /**
     * 查询快递单管理列表
     * 
     * @param frontFastmail 快递单管理
     * @return 快递单管理集合
     */
    public List<FrontFastmail> selectFrontFastmailList(FrontFastmail frontFastmail);

    /**
     * 新增快递单管理
     * 
     * @param frontFastmail 快递单管理
     * @return 结果
     */
    public int insertFrontFastmail(FrontFastmail frontFastmail);

    /**
     * 修改快递单管理
     * 
     * @param frontFastmail 快递单管理
     * @return 结果
     */
    public int updateFrontFastmail(FrontFastmail frontFastmail);

    /**
     * 批量删除快递单管理
     * 
     * @param ids 需要删除的快递单管理主键集合
     * @return 结果
     */
    public int deleteFrontFastmailByIds(Long[] ids);

    /**
     * 删除快递单管理信息
     * 
     * @param id 快递单管理主键
     * @return 结果
     */
    public int deleteFrontFastmailById(Long id);

    /**
     * 修改快递状态为已完成
     * @param id
     * @return true/false
     */
    Boolean updateFrontFastmailStatus(Long id);
}
