package com.ruoyi.store.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontOrdersGoods;
import com.ruoyi.store.service.IFrontOrdersGoodsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 订单-商品信息Controller
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/order/goods")
public class FrontOrdersGoodsController extends BaseController
{
    @Autowired
    private IFrontOrdersGoodsService frontOrdersGoodsService;

    /**
     * 查询订单-商品信息列表
     */
    @RequiresPermissions("system:goods:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontOrdersGoods frontOrdersGoods)
    {
        startPage();
        List<FrontOrdersGoods> list = frontOrdersGoodsService.selectFrontOrdersGoodsList(frontOrdersGoods);
        return getDataTable(list);
    }

    /**
     * 导出订单-商品信息列表
     */
    @RequiresPermissions("system:goods:export")
    @Log(title = "订单-商品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontOrdersGoods frontOrdersGoods)
    {
        List<FrontOrdersGoods> list = frontOrdersGoodsService.selectFrontOrdersGoodsList(frontOrdersGoods);
        ExcelUtil<FrontOrdersGoods> util = new ExcelUtil<FrontOrdersGoods>(FrontOrdersGoods.class);
        util.exportExcel(response, list, "订单-商品信息数据");
    }

    /**
     * 获取订单-商品信息详细信息
     */
    @RequiresPermissions("system:goods:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontOrdersGoodsService.selectFrontOrdersGoodsById(id));
    }

    /**
     * 新增订单-商品信息
     */
    @RequiresPermissions("system:goods:add")
    @Log(title = "订单-商品信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontOrdersGoods frontOrdersGoods)
    {
        return toAjax(frontOrdersGoodsService.insertFrontOrdersGoods(frontOrdersGoods));
    }

    /**
     * 修改订单-商品信息
     */
    @RequiresPermissions("system:goods:edit")
    @Log(title = "订单-商品信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontOrdersGoods frontOrdersGoods)
    {
        return toAjax(frontOrdersGoodsService.updateFrontOrdersGoods(frontOrdersGoods));
    }

    /**
     * 删除订单-商品信息
     */
    @RequiresPermissions("system:goods:remove")
    @Log(title = "订单-商品信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontOrdersGoodsService.deleteFrontOrdersGoodsByIds(ids));
    }
}
