package com.ruoyi.store.service;

import java.util.List;
import com.ruoyi.system.api.domain.FrontOrdersGoods;

/**
 * 订单-商品信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IFrontOrdersGoodsService 
{
    /**
     * 查询订单-商品信息
     * 
     * @param id 订单-商品信息主键
     * @return 订单-商品信息
     */
    public FrontOrdersGoods selectFrontOrdersGoodsById(Long id);

    /**
     * 查询订单-商品信息列表
     * 
     * @param frontOrdersGoods 订单-商品信息
     * @return 订单-商品信息集合
     */
    public List<FrontOrdersGoods> selectFrontOrdersGoodsList(FrontOrdersGoods frontOrdersGoods);

    /**
     * 新增订单-商品信息
     * 
     * @param frontOrdersGoods 订单-商品信息
     * @return 结果
     */
    public int insertFrontOrdersGoods(FrontOrdersGoods frontOrdersGoods);

    /**
     * 修改订单-商品信息
     * 
     * @param frontOrdersGoods 订单-商品信息
     * @return 结果
     */
    public int updateFrontOrdersGoods(FrontOrdersGoods frontOrdersGoods);

    /**
     * 批量删除订单-商品信息
     * 
     * @param ids 需要删除的订单-商品信息主键集合
     * @return 结果
     */
    public int deleteFrontOrdersGoodsByIds(Long[] ids);

    /**
     * 删除订单-商品信息信息
     * 
     * @param id 订单-商品信息主键
     * @return 结果
     */
    public int deleteFrontOrdersGoodsById(Long id);

}
