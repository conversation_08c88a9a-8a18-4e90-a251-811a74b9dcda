package com.ruoyi.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.FrontGift;
import com.ruoyi.store.domain.vo.FrontGiftVo;

import java.util.List;

/**
 * @ClassName IFrontGiftService
 * @Description 后台 礼品卡 业务
 * <AUTHOR>
 * @Date 2025/5/19 下午5:45
 */
public interface IFrontGiftService extends IService<FrontGift> {

    /**
     * 获取礼品卡列表
     * @return
     */
    List<FrontGiftVo.FrontGiftList> getGiftList(FrontGiftVo.FrontGiftSearch vo);

    /**
     * 添加礼品卡
     * @param frontGift
     * @return
     */
    Boolean addGift(FrontGift frontGift);

    /**
     * 修改礼品卡
     * @param frontGift
     * @return
     */
    Boolean updateGift(FrontGift frontGift);

    /**
     * 批量删除礼品卡
     * @param ids
     */
    void deleteGiftByIds(Long[] ids);
}
