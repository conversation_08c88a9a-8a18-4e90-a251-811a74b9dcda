package com.ruoyi.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.FrontEvaluate;
import com.ruoyi.system.api.domain.vo.FrontEvaluateVo;

import java.util.List;

/**
 * @ClassName IFrontEvaluateService
 * @Description 评价管理业务接口
 * <AUTHOR>
 * @Date 2025/5/20 下午4:52
 */
public interface IFrontEvaluateService extends IService<FrontEvaluate> {

    /**
     * 获取评价详情
     * @param vo
     * @return
     */
    FrontEvaluateVo.FrontEvaluateDetail getEvaluateDetail(FrontEvaluateVo.FrontEvaluateSearch vo);

    /**
     * 获取对应商品评价详情
     * @param vo
     * @return
     */
    FrontEvaluateVo.FrontEvaluateDetailInfo getEvaluateDetailInfo(FrontEvaluateVo.FrontEvaluateDetailSearch vo);

    /**
     * 获取评价列表
     * @param vo
     * @return
     */
    List<FrontEvaluateVo.FrontEvaluateList> getEvaluateList(FrontEvaluateVo.FrontEvaluateSearch vo);

    /**
     * 获取评价详情列表
     * @param vo
     * @return
     */
    List<FrontEvaluate> getList(FrontEvaluateVo.FrontEvaluateDetailSearch vo);

    /**
     * 通过状态获取列表
     * @param vo
     * @return
     */
    List<FrontEvaluate> getStatusList(FrontEvaluateVo.FrontEvaluateDetailSearch vo);

    /**
     * 回复评价内容
     * @param frontEvaluate
     * @return
     */
    Boolean updateEvaluate(FrontEvaluate frontEvaluate);

    /**
     * 评价内容加精
     * @param id
     * @return
     */
    Boolean updateIsRefine(Long id);

    /**
     * 评价内容显示/隐藏
     * @param id
     * @return
     */
    Boolean updateIsShow(Long id);

    /**
     * 批量删除
     * @param ids
     */
    void deleteEvaluateByIds(Long[] ids);

    /**
     * 批量隐藏
     * @param ids
     */
    void isShow(Long[] ids);
}
