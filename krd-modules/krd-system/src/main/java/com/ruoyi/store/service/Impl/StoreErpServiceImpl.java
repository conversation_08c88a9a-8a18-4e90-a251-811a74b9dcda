package com.ruoyi.store.service.Impl;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.JiCeConstants;
import com.ruoyi.common.core.utils.erp.ApiExample;
import com.ruoyi.store.domain.res.GoodsPushDetail;
import com.ruoyi.store.domain.res.Skus;
import com.ruoyi.store.service.StoreErpService;
import com.ruoyi.system.api.domain.FrontGategory;
import com.ruoyi.system.api.domain.FrontGoods;
import com.ruoyi.system.api.domain.SysConfig;
import com.ruoyi.system.api.mapper.FrontGategoryMapper;
import com.ruoyi.system.api.mapper.SysConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * @ClassName PushToStoreErpServiceImpl
 * @Description 商城相关推送万里牛业务实现
 * <AUTHOR>
 * @Date 2025/6/16 下午3:23
 */
@Slf4j
@Service
public class StoreErpServiceImpl implements StoreErpService {

    @Autowired
    private FrontGategoryMapper frontGategoryMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Override
    public void pushGategoryToErp(FrontGategory frontGategory) throws Exception {
        // 获取配置
        Map<String, Object> config = getConfig();
        String apiUrl = String.valueOf(config.get(JiCeConstants.ERP_URL));
        String appKey = String.valueOf(config.get(JiCeConstants.ERP_APP_KEY));
        String appSecret = String.valueOf(config.get(JiCeConstants.ERP_SECRET));
        String shopNick = String.valueOf(config.get(JiCeConstants.ERP_SHOP_NICK));

        Map<String, Object> category = new LinkedHashMap<>();
        category.put("category_id", frontGategory.getId());
        category.put("name", frontGategory.getTitle());
        category.put("parent_id",frontGategory.getParentId() == null ? "" : frontGategory.getParentId() );
        category.put("shop_nick", shopNick);
        category.put("sort_order", 0);
        category.put("status", frontGategory.getIsShow());

        List<Map<String, Object>> list = new ArrayList<>();
        list.add(category);

        Map<String, Object> trades = ApiExample.bodyPush(list, "categories", appKey, appSecret);

        ApiExample.erpPost(trades, JiCeConstants.ERP_CATEGORY_PUSH, apiUrl);
    }

    @Override
    public void pushGoodsToErp(FrontGoods frontGoods) throws Exception {
        // 获取配置
        Map<String, Object> config = getConfig();
        String apiUrl = String.valueOf(config.get(JiCeConstants.ERP_URL));
        String appKey = String.valueOf(config.get(JiCeConstants.ERP_APP_KEY));
        String appSecret = String.valueOf(config.get(JiCeConstants.ERP_SECRET));
        String shopNick = String.valueOf(config.get(JiCeConstants.ERP_SHOP_NICK));

        GoodsPushDetail goodsPushDetail = new GoodsPushDetail();
        List<Skus> skusList = new ArrayList<>();

        // 组装商品信息
        goodsPushDetail.setCreate_time(frontGoods.getCreateTime().toString());
        goodsPushDetail.setItem_id(frontGoods.getId().toString());
        goodsPushDetail.setModify_time(frontGoods.getUpdateTime().toString());
        goodsPushDetail.setShop_nick(shopNick);
        goodsPushDetail.setStatus(frontGoods.getIsStatus());
        goodsPushDetail.setTitle(frontGoods.getName());
        goodsPushDetail.setCategory_id(frontGoods.getGoodsType() == 0 ? "" : frontGoods.getGategoryId().toString());
        goodsPushDetail.setUnit(frontGoods.getUnit());

        if (frontGoods.getFrontGoodsSpecList() != null){

            //组装商品规格
            frontGoods.getFrontGoodsSpecList().forEach(item ->{
                Skus skus = new Skus();
                skus.setAttributes(item.getTitle());
                skus.setPrice(item.getPrice());
                skus.setQuantity(Math.toIntExact(item.getAmount() == null ? Integer.MAX_VALUE : item.getAmount()));
                skus.setSku_id(item.getId().toString());
                skus.setStatus(1);
                skus.setCreate_time(item.getCreateTime().toString());
                skus.setModifiy_time(item.getUpdateTime().toString());
                skus.setItem_id(frontGoods.getId().toString());
                skusList.add(skus);
            });

            goodsPushDetail.setSkus(skusList);
        }else {
            goodsPushDetail.setQuantity(frontGoods.getAmount() == null ? Integer.MAX_VALUE : frontGoods.getAmount());
            goodsPushDetail.setPrice(frontGoods.getPrice());
        }

        Map<String, Object> map = convertToMap(goodsPushDetail);

        List<Map<String, Object>> list = new ArrayList<>();
        list.add(map);


        Map<String, Object> trades = ApiExample.bodyPush(list, "items", appKey, appSecret);

        ApiExample.erpPost(trades, JiCeConstants.ERP_SHOP_PUSH, apiUrl);

    }

    // 获取配置
    private Map<String, Object> getConfig() {
        SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique(JiCeConstants.WLN_ERP_CONFIG);
        String configValue = sysConfig.getConfigValue();
        return JSONObject.parseObject(configValue);
    }

    public static Map<String, Object> convertToMap(Object obj) throws IllegalAccessException, InvocationTargetException {
        Map<String, Object> map = new HashMap<>();
        Method[] methods = obj.getClass().getMethods();

        for (Method method : methods) {
            if (method.getName().startsWith("get") && method.getParameterCount() == 0) {
                String fieldName = method.getName().substring(3);
                if (!fieldName.isEmpty()) {
                    Object value = method.invoke(obj);
                    map.put(Character.toLowerCase(fieldName.charAt(0)) + fieldName.substring(1), value);
                }
            }
        }
        return map;
    }
}
