package com.ruoyi.store.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName FrontGategoryVo
 * @Description 后台商品分类 交互参数
 * <AUTHOR>
 * @Date 2025/5/20 下午2:16
 */
public interface FrontGategoryVo {

    @Data
    @Schema(description = "商品分类搜索参数")
    class FrontGategorySearch {
        @Schema(description = "商品分类id")
        private Long id;
        @Schema(description = "搜索关键词")
        private String title;
    }

    @Data
    @Schema(description = "修改商品分类排序")
    class FrontGategorySort {
        @Schema(description = "商品分类id")
        private Long id;
        @Schema(description = "排序")
        private Integer sort;
    }

    @Data
    @Schema(description = "修改商品分类状态")
    class FrontGategoryStatus {
        @Schema(description = "商品分类id")
        private Long id;
        @Schema(description = "状态")
        private Integer isShow;
    }
}
