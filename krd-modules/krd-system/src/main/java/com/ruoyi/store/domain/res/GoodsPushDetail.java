package com.ruoyi.store.domain.res;

import lombok.Data;

import java.util.List;

/**
 * @ClassName GoodsPushDetail
 * @Description 商品推送列表
 * <AUTHOR>
 * @Date 2025/6/16 下午4:28
 */
@Data
public class GoodsPushDetail {
    /**
     * 创建时间，格式:yyyy-MM-dd HH:mm:ss
     */
    private String create_time;
    /**
     * 商品编号
     */
    private String item_id;
    /**
     * 最新修改时间，格式:yyyy-MM-dd HH:mm:ss
     */
    private String modify_time;
    /**
     * 店铺名称
     */
    private String shop_nick;
    /**
     * 状态,0：已删除，1：在售，2：待售，仓库中
     */
    private Integer status;
    /**
     * 商品标题
     */
    private String title;
    /**
     * 分类ID
     */
    private String category_id;
    /**
     * 价格，若无子规格，必填
     */
    private Number price;
    /**
     * 库存，若无子规格，必填
     */
    private Integer quantity;
    /**
     * 规格集，无规格商品不填
     */
    private List<Skus> skus;
    /**
     * 单位
     */
    private String unit;
}
