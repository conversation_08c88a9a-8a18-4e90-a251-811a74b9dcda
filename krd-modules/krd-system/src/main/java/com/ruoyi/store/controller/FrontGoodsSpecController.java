package com.ruoyi.store.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontGoodsSpec;
import com.ruoyi.store.service.IFrontGoodsSpecService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 商品规格Controller
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/spec")
public class FrontGoodsSpecController extends BaseController
{
    @Autowired
    private IFrontGoodsSpecService frontGoodsSpecService;

    /**
     * 查询商品规格列表
     */
    @RequiresPermissions("system:spec:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontGoodsSpec frontGoodsSpec)
    {
        startPage();
        List<FrontGoodsSpec> list = frontGoodsSpecService.selectFrontGoodsSpecList(frontGoodsSpec);
        return getDataTable(list);
    }

    /**
     * 导出商品规格列表
     */
    @RequiresPermissions("system:spec:export")
    @Log(title = "商品规格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontGoodsSpec frontGoodsSpec)
    {
        List<FrontGoodsSpec> list = frontGoodsSpecService.selectFrontGoodsSpecList(frontGoodsSpec);
        ExcelUtil<FrontGoodsSpec> util = new ExcelUtil<FrontGoodsSpec>(FrontGoodsSpec.class);
        util.exportExcel(response, list, "商品规格数据");
    }

    /**
     * 获取商品规格详细信息
     */
    @RequiresPermissions("system:spec:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontGoodsSpecService.selectFrontGoodsSpecById(id));
    }

    /**
     * 新增商品规格
     */
    @RequiresPermissions("system:spec:add")
    @Log(title = "商品规格", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FrontGoodsSpec frontGoodsSpec)
    {
        return toAjax(frontGoodsSpecService.insertFrontGoodsSpec(frontGoodsSpec));
    }

    /**
     * 修改商品规格
     */
    @RequiresPermissions("system:spec:edit")
    @Log(title = "商品规格", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody FrontGoodsSpec frontGoodsSpec)
    {
        return toAjax(frontGoodsSpecService.updateFrontGoodsSpec(frontGoodsSpec));
    }

    /**
     * 删除商品规格
     */
    @RequiresPermissions("system:spec:remove")
    @Log(title = "商品规格", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontGoodsSpecService.deleteFrontGoodsSpecByIds(ids));
    }
}
