package com.ruoyi.operate.service.impl;

import com.ruoyi.operate.domain.FrontPublishType;
import com.ruoyi.operate.mapper.FrontPublishTypeMapper;
import com.ruoyi.operate.service.IFrontPublishTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消息推送类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class FrontPublishTypeServiceImpl implements IFrontPublishTypeService
{
    @Autowired
    private FrontPublishTypeMapper frontPublishTypeMapper;

    /**
     * 查询消息推送类型
     *
     * @param id 消息推送类型主键
     * @return 消息推送类型
     */
    @Override
    public FrontPublishType selectFrontPublishTypeById(Long id)
    {
        return frontPublishTypeMapper.selectFrontPublishTypeById(id);
    }

    /**
     * 查询消息推送类型列表
     *
     * @param frontPublishType 消息推送类型
     * @return 消息推送类型
     */
    @Override
    public List<FrontPublishType> selectFrontPublishTypeList(FrontPublishType frontPublishType)
    {
        return frontPublishTypeMapper.selectFrontPublishTypeList(frontPublishType);
    }

    /**
     * 新增消息推送类型
     *
     * @param frontPublishType 消息推送类型
     * @return 结果
     */
    @Override
    public int insertFrontPublishType(FrontPublishType frontPublishType)
    {
        return frontPublishTypeMapper.insertFrontPublishType(frontPublishType);
    }

    /**
     * 修改消息推送类型
     *
     * @param frontPublishType 消息推送类型
     * @return 结果
     */
    @Override
    public int updateFrontPublishType(FrontPublishType frontPublishType)
    {
        return frontPublishTypeMapper.updateFrontPublishType(frontPublishType);
    }

    /**
     * 批量删除消息推送类型
     *
     * @param ids 需要删除的消息推送类型主键
     * @return 结果
     */
    @Override
    public int deleteFrontPublishTypeByIds(Long[] ids)
    {
        return frontPublishTypeMapper.deleteFrontPublishTypeByIds(ids);
    }

    /**
     * 删除消息推送类型信息
     *
     * @param id 消息推送类型主键
     * @return 结果
     */
    @Override
    public int deleteFrontPublishTypeById(Long id)
    {
        return frontPublishTypeMapper.deleteFrontPublishTypeById(id);
    }
}
