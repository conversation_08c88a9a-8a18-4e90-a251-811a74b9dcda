package com.ruoyi.operate.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontChoice;
import com.ruoyi.operate.service.IFrontChoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 精选内容Controller
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/choice")
public class FrontChoiceController extends BaseController
{
    @Autowired
    private IFrontChoiceService frontChoiceService;

    /**
     * 查询精选内容列表
     */
    @RequiresPermissions("system:choice:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontChoice frontChoice)
    {
        startPage();
        List<FrontChoice> list = frontChoiceService.selectFrontChoiceList(frontChoice);
        return getDataTable(list);
    }

    /**
     * 导出精选内容列表
     */
    @RequiresPermissions("system:choice:export")
    @Log(title = "精选内容", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontChoice frontChoice)
    {
        List<FrontChoice> list = frontChoiceService.selectFrontChoiceList(frontChoice);
        ExcelUtil<FrontChoice> util = new ExcelUtil<FrontChoice>(FrontChoice.class);
        util.exportExcel(response, list, "精选内容数据");
    }

    /**
     * 获取精选内容详细信息
     */
    @RequiresPermissions("system:choice:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontChoiceService.selectFrontChoiceById(id));
    }

    /**
     * 新增精选内容
     */
    @RequiresPermissions("system:choice:add")
    @Log(title = "精选内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontChoice frontChoice)
    {
        return toAjax(frontChoiceService.insertFrontChoice(frontChoice));
    }

    /**
     * 修改精选内容
     */
    @RequiresPermissions("system:choice:edit")
    @Log(title = "精选内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontChoice frontChoice)
    {
        return toAjax(frontChoiceService.updateFrontChoice(frontChoice));
    }

    /**
     * 删除精选内容
     */
    @RequiresPermissions("system:choice:remove")
    @Log(title = "精选内容", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontChoiceService.deleteFrontChoiceByIds(ids));
    }
}
