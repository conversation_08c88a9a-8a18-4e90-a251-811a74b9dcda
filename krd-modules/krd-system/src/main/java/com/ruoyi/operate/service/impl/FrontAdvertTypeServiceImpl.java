package com.ruoyi.operate.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.api.mapper.FrontAdvertTypeMapper;
import com.ruoyi.system.api.domain.FrontAdvertType;
import com.ruoyi.operate.service.IFrontAdvertTypeService;

/**
 * 广告类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class FrontAdvertTypeServiceImpl implements IFrontAdvertTypeService
{
    @Autowired
    private FrontAdvertTypeMapper frontAdvertTypeMapper;

    /**
     * 查询广告类型
     *
     * @param id 广告类型主键
     * @return 广告类型
     */
    @Override
    public FrontAdvertType selectFrontAdvertTypeById(Long id)
    {
        return frontAdvertTypeMapper.selectFrontAdvertTypeById(id);
    }

    /**
     * 查询广告类型列表
     *
     * @param frontAdvertType 广告类型
     * @return 广告类型
     */
    @Override
    public List<FrontAdvertType> selectFrontAdvertTypeList(FrontAdvertType frontAdvertType)
    {
        return frontAdvertTypeMapper.selectFrontAdvertTypeList(frontAdvertType);
    }

    /**
     * 新增广告类型
     *
     * @param frontAdvertType 广告类型
     * @return 结果
     */
    @Override
    public int insertFrontAdvertType(FrontAdvertType frontAdvertType)
    {
        return frontAdvertTypeMapper.insertFrontAdvertType(frontAdvertType);
    }

    /**
     * 修改广告类型
     *
     * @param frontAdvertType 广告类型
     * @return 结果
     */
    @Override
    public int updateFrontAdvertType(FrontAdvertType frontAdvertType)
    {
        return frontAdvertTypeMapper.updateFrontAdvertType(frontAdvertType);
    }

    /**
     * 批量删除广告类型
     *
     * @param ids 需要删除的广告类型主键
     * @return 结果
     */
    @Override
    public int deleteFrontAdvertTypeByIds(Long[] ids)
    {
        return frontAdvertTypeMapper.deleteFrontAdvertTypeByIds(ids);
    }

    /**
     * 删除广告类型信息
     *
     * @param id 广告类型主键
     * @return 结果
     */
    @Override
    public int deleteFrontAdvertTypeById(Long id)
    {
        return frontAdvertTypeMapper.deleteFrontAdvertTypeById(id);
    }
}
