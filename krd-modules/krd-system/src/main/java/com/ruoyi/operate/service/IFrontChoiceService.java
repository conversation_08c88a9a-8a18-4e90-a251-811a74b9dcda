package com.ruoyi.operate.service;

import com.ruoyi.system.api.domain.FrontChoice;

import java.util.List;

/**
 * 精选内容Service接口
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IFrontChoiceService
{
    /**
     * 查询精选内容
     *
     * @param id 精选内容主键
     * @return 精选内容
     */
    public FrontChoice selectFrontChoiceById(Long id);

    /**
     * 查询精选内容列表
     *
     * @param frontChoice 精选内容
     * @return 精选内容集合
     */
    public List<FrontChoice> selectFrontChoiceList(FrontChoice frontChoice);

    /**
     * 新增精选内容
     *
     * @param frontChoice 精选内容
     * @return 结果
     */
    public int insertFrontChoice(FrontChoice frontChoice);

    /**
     * 修改精选内容
     *
     * @param frontChoice 精选内容
     * @return 结果
     */
    public int updateFrontChoice(FrontChoice frontChoice);

    /**
     * 批量删除精选内容
     *
     * @param ids 需要删除的精选内容主键集合
     * @return 结果
     */
    public int deleteFrontChoiceByIds(Long[] ids);

    /**
     * 删除精选内容信息
     *
     * @param id 精选内容主键
     * @return 结果
     */
    public int deleteFrontChoiceById(Long id);
}
