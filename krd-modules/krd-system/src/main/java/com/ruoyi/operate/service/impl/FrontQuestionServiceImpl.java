package com.ruoyi.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.check.domain.entity.FrontPackage;
import com.ruoyi.check.mapper.FrontPackageMapper;
import com.ruoyi.operate.service.IFrontQuestionService;
import com.ruoyi.system.api.domain.FrontHealthAnswer;
import com.ruoyi.system.api.domain.FrontQuestion;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.mapper.FrontHealthAnswerMapper;
import com.ruoyi.system.api.mapper.FrontQuestionMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 问卷管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class FrontQuestionServiceImpl implements IFrontQuestionService
{
    @Autowired
    private FrontQuestionMapper frontQuestionMapper;

    @Autowired
    private FrontHealthAnswerMapper frontHealthAnswerMapper;

    @Autowired
    private FrontPackageMapper frontPackageMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询问卷管理
     *
     * @param id 问卷管理主键
     * @return 问卷管理
     */
    @Override
    public FrontQuestion selectFrontQuestionById(Long id)
    {
        return frontQuestionMapper.selectFrontQuestionById(id);
    }

    /**
     * 查询问卷管理列表
     *
     * @param frontQuestion 问卷管理
     * @return 问卷管理
     */
    @Override
    public List<FrontQuestion> selectFrontQuestionList(FrontQuestion frontQuestion)
    {

        List<FrontQuestion> frontQuestions = frontQuestionMapper.selectFrontQuestionList(frontQuestion);
        frontQuestions.forEach(question -> {
            LambdaQueryWrapper<FrontPackage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FrontPackage::getId, question.getPackageId());
            FrontPackage frontPackage = frontPackageMapper.selectOne(wrapper);
            question.setPackageName(frontPackage.getPakeageName());
            SysUser frontUser = sysUserMapper.selectUserById(question.getCreateUserId());
            question.setCreateUserName(frontUser.getUserName());
        });
        return frontQuestions;
    }

    /**
     * 新增问卷管理
     *
     * @param frontQuestion 问卷管理
     * @return 结果
     */
    @Override
    public int insertFrontQuestion(FrontQuestion frontQuestion)
    {
        frontQuestion.setCreateTime(new Date());
        return frontQuestionMapper.insertFrontQuestion(frontQuestion);
    }

    /**
     * 修改问卷管理
     *
     * @param frontQuestion 问卷管理
     * @return 结果
     */
    @Override
    public int updateFrontQuestion(FrontQuestion frontQuestion)
    {
        return frontQuestionMapper.updateFrontQuestion(frontQuestion);
    }

    /**
     * 批量删除问卷管理
     *
     * @param ids 需要删除的问卷管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontQuestionByIds(Long[] ids)
    {
        return frontQuestionMapper.deleteFrontQuestionByIds(ids);
    }

    /**
     * 删除问卷管理信息
     *
     * @param id 问卷管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontQuestionById(Long id)
    {
        return frontQuestionMapper.deleteFrontQuestionById(id);
    }

    @Override
    public Map<String, Object> getFrontQuestionList(int questId) {
        LambdaQueryWrapper<FrontHealthAnswer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FrontHealthAnswer::getQuestId, questId);
        wrapper.eq(FrontHealthAnswer::getType, 2);
        List<FrontHealthAnswer> frontHealthAnswers = frontHealthAnswerMapper.selectList(wrapper);
        Map<String, Object> result = new HashMap<>();
        Map<String, Map<String, Integer>> statistics = new HashMap<>();
        for (FrontHealthAnswer answer : frontHealthAnswers) {
            String content = answer.getContent();
            try {
                JSONArray jsonArray = JSON.parseArray(content);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject item = jsonArray.getJSONObject(i);
                    String title = item.getString("title");
                    String selectedOptionStr = item.getString("answer");
                    try {
                        Object selectedOption = JSON.parse(selectedOptionStr);
                        String selectedValue = getString(selectedOption, selectedOptionStr);
                        if (selectedValue == null) {
                            selectedValue = "未知";
                        }
                        statistics.putIfAbsent(title, new HashMap<>());
                        Map<String, Integer> labelCounts = statistics.get(title);
                        labelCounts.put(selectedValue, labelCounts.getOrDefault(selectedValue, 0) + 1);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //答题人数
        result.put("total", frontHealthAnswers.size());
        result.put("statistics", statistics);
        return result;
    }

    @Nullable
    private static String getString(Object selectedOption , String selectedOptionStr) {
        String selectedValue = null;
        if (selectedOption instanceof JSONArray) {
            JSONArray array = (JSONArray) selectedOption;
            if (!array.isEmpty()) {
                JSONObject obj = array.getJSONObject(0);
                selectedValue = obj.getString("value");
            }
        } else if (selectedOption instanceof JSONObject) {
            JSONObject obj = (JSONObject) selectedOption;
            selectedValue = obj.getString("value");
        } else {
            selectedValue = selectedOptionStr;
        }
        return selectedValue;
    }
}
