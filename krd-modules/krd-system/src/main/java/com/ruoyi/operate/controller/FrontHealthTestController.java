package com.ruoyi.operate.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontHealthTest;
import com.ruoyi.operate.service.IFrontHealthTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 健康管理测试Controller
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/health")
public class FrontHealthTestController extends BaseController
{
    @Autowired
    private IFrontHealthTestService frontHealthTestService;

    /**
     * 查询健康管理测试列表
     */
    @RequiresPermissions("operate:test:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontHealthTest frontHealthTest)
    {
        startPage();
        List<FrontHealthTest> list = frontHealthTestService.selectFrontHealthTestList(frontHealthTest);
        return getDataTable(list);
    }

    /**
     * 导出健康管理测试列表
     */
    @RequiresPermissions("operate:test:export")
    @Log(title = "健康管理测试", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontHealthTest frontHealthTest)
    {
        List<FrontHealthTest> list = frontHealthTestService.selectFrontHealthTestList(frontHealthTest);
        ExcelUtil<FrontHealthTest> util = new ExcelUtil<FrontHealthTest>(FrontHealthTest.class);
        util.exportExcel(response, list, "健康管理测试数据");
    }

    /**
     * 获取健康管理测试详细信息
     */
    @RequiresPermissions("operate:test:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontHealthTestService.selectFrontHealthTestById(id));
    }

    /**
     * 新增健康管理测试
     */
    @RequiresPermissions("operate:test:add")
    @Log(title = "健康管理测试", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontHealthTest frontHealthTest)
    {
        return toAjax(frontHealthTestService.insertFrontHealthTest(frontHealthTest));
    }

    /**
     * 修改健康管理测试
     */
    @RequiresPermissions("operate:test:edit")
    @Log(title = "健康管理测试", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontHealthTest frontHealthTest)
    {
        return toAjax(frontHealthTestService.updateFrontHealthTest(frontHealthTest));
    }

    /**
     * 删除健康管理测试
     */
    @RequiresPermissions("operate:test:remove")
    @Log(title = "健康管理测试", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontHealthTestService.deleteFrontHealthTestByIds(ids));
    }
}
