package com.ruoyi.operate.mapper;

import com.ruoyi.operate.domain.FrontQuestionMeun;

import java.util.List;

/**
 * 问卷套餐类型Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface FrontQuestionMeunMapper
{
    /**
     * 查询问卷套餐类型
     *
     * @param id 问卷套餐类型主键
     * @return 问卷套餐类型
     */
    public FrontQuestionMeun selectFrontQuestionMeunById(Long id);

    /**
     * 查询问卷套餐类型列表
     *
     * @param frontQuestionMeun 问卷套餐类型
     * @return 问卷套餐类型集合
     */
    public List<FrontQuestionMeun> selectFrontQuestionMeunList(FrontQuestionMeun frontQuestionMeun);

    /**
     * 新增问卷套餐类型
     *
     * @param frontQuestionMeun 问卷套餐类型
     * @return 结果
     */
    public int insertFrontQuestionMeun(FrontQuestionMeun frontQuestionMeun);

    /**
     * 修改问卷套餐类型
     *
     * @param frontQuestionMeun 问卷套餐类型
     * @return 结果
     */
    public int updateFrontQuestionMeun(FrontQuestionMeun frontQuestionMeun);

    /**
     * 删除问卷套餐类型
     *
     * @param id 问卷套餐类型主键
     * @return 结果
     */
    public int deleteFrontQuestionMeunById(Long id);

    /**
     * 批量删除问卷套餐类型
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontQuestionMeunByIds(Long[] ids);
}
