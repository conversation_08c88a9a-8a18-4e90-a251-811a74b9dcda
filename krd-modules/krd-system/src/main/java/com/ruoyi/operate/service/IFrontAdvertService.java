package com.ruoyi.operate.service;

import java.util.List;
import com.ruoyi.system.api.domain.FrontAdvert;

/**
 * 广告管理Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IFrontAdvertService
{
    /**
     * 查询广告管理
     *
     * @param id 广告管理主键
     * @return 广告管理
     */
    public FrontAdvert selectFrontAdvertById(Long id);

    /**
     * 查询广告管理列表
     *
     * @param frontAdvert 广告管理
     * @return 广告管理集合
     */
    public List<FrontAdvert> selectFrontAdvertList(FrontAdvert frontAdvert);

    /**
     * 新增广告管理
     *
     * @param frontAdvert 广告管理
     * @return 结果
     */
    public int insertFrontAdvert(FrontAdvert frontAdvert);

    /**
     * 修改广告管理
     *
     * @param frontAdvert 广告管理
     * @return 结果
     */
    public int updateFrontAdvert(FrontAdvert frontAdvert);

    /**
     * 批量删除广告管理
     *
     * @param ids 需要删除的广告管理主键集合
     * @return 结果
     */
    public int deleteFrontAdvertByIds(Long[] ids);

    /**
     * 删除广告管理信息
     *
     * @param id 广告管理主键
     * @return 结果
     */
    public int deleteFrontAdvertById(Long id);
}
