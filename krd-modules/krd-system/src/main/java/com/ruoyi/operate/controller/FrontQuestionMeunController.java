package com.ruoyi.operate.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.operate.domain.FrontQuestionMeun;
import com.ruoyi.operate.service.IFrontQuestionMeunService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 问卷套餐类型Controller
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/meun")
public class FrontQuestionMeunController extends BaseController
{
    @Autowired
    private IFrontQuestionMeunService frontQuestionMeunService;

    /**
     * 查询问卷套餐类型列表
     */
    @RequiresPermissions("operate:meun:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontQuestionMeun frontQuestionMeun)
    {
        startPage();
        List<FrontQuestionMeun> list = frontQuestionMeunService.selectFrontQuestionMeunList(frontQuestionMeun);
        return getDataTable(list);
    }

    /**
     * 导出问卷套餐类型列表
     */
    @RequiresPermissions("operate:meun:export")
    @Log(title = "问卷套餐类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontQuestionMeun frontQuestionMeun)
    {
        List<FrontQuestionMeun> list = frontQuestionMeunService.selectFrontQuestionMeunList(frontQuestionMeun);
        ExcelUtil<FrontQuestionMeun> util = new ExcelUtil<FrontQuestionMeun>(FrontQuestionMeun.class);
        util.exportExcel(response, list, "问卷套餐类型数据");
    }

    /**
     * 获取问卷套餐类型详细信息
     */
    @RequiresPermissions("operate:meun:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontQuestionMeunService.selectFrontQuestionMeunById(id));
    }

    /**
     * 新增问卷套餐类型
     */
    @RequiresPermissions("operate:meun:add")
    @Log(title = "问卷套餐类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontQuestionMeun frontQuestionMeun)
    {
        return toAjax(frontQuestionMeunService.insertFrontQuestionMeun(frontQuestionMeun));
    }

    /**
     * 修改问卷套餐类型
     */
    @RequiresPermissions("operate:meun:edit")
    @Log(title = "问卷套餐类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontQuestionMeun frontQuestionMeun)
    {
        return toAjax(frontQuestionMeunService.updateFrontQuestionMeun(frontQuestionMeun));
    }

    /**
     * 删除问卷套餐类型
     */
    @RequiresPermissions("operate:meun:remove")
    @Log(title = "问卷套餐类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontQuestionMeunService.deleteFrontQuestionMeunByIds(ids));
    }
}
