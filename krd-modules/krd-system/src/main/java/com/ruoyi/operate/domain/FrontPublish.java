package com.ruoyi.operate.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.time.LocalDateTime;

/**
 * 推送消息对象 front_publish
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@EqualsAndHashCode(callSuper = true)
@Setter
@Data
@TableName("front_publish")
public class FrontPublish extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 消息类型 */
    @Excel(name = "消息类型")
    private Long messageId;

    @TableField(exist = false)
    private String messageName;

    @Excel(name = "消息标题")
    private String title;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 封面图URL */
    @Excel(name = "封面图URL")
    private String picUrl;

    /** 链接URL */
    @Excel(name = "链接URL")
    private String linkUrl;

    /** 标签id */
    @Excel(name = "标签id")
    private Long tagId;

    /** 定时推送时间 */
    @Excel(name = "定时推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishDate;

    @Excel(name = "0-false 1-true")
    private Long publishType;

    /** 0-false 1-true */
    @Excel(name = "0-false 1-true")
    private Long isDel;

    /** 推送次数 */
    @Excel(name = "推送次数")
    private Long publishCount;

    @Excel(name = "推送量")
    private Long publishNum;

    @Excel(name = "接受量")
    private Long receiveNum;

    /** 状态 0-未推送 1-已推送 */
    @Excel(name = "状态 0-未推送 1-已推送")
    private Long status;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("messageId", getMessageId())
            .append("content", getContent())
            .append("picUrl", getPicUrl())
            .append("linkUrl", getLinkUrl())
            .append("tagId", getTagId())
            .append("publishDate", getPublishDate())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("isDel", getIsDel())
            .append("publishCount", getPublishCount())
            .append("status", getStatus())
            .toString();
    }
}
