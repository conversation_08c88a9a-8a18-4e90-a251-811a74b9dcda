package com.ruoyi.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 待办事项对象 front_todo
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@Setter
@Data
@TableName("front_todo")
public class FrontTodo
{
    private static final long serialVersionUID = 1L;

    /** 待办事项id */
    private Long id;

    /** 类型枚举  */
    @Excel(name = "类型枚举 ")
    private Long type;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    private LocalDateTime createTime;
}
