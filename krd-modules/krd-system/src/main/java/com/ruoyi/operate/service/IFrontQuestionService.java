package com.ruoyi.operate.service;

import com.ruoyi.system.api.domain.FrontQuestion;

import java.util.List;
import java.util.Map;

/**
 * 问卷管理Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IFrontQuestionService
{
    /**
     * 查询问卷管理
     *
     * @param id 问卷管理主键
     * @return 问卷管理
     */
    public FrontQuestion selectFrontQuestionById(Long id);

    /**
     * 查询问卷管理列表
     *
     * @param frontQuestion 问卷管理
     * @return 问卷管理集合
     */
    public List<FrontQuestion> selectFrontQuestionList(FrontQuestion frontQuestion);

    /**
     * 新增问卷管理
     *
     * @param frontQuestion 问卷管理
     * @return 结果
     */
    public int insertFrontQuestion(FrontQuestion frontQuestion);

    /**
     * 修改问卷管理
     *
     * @param frontQuestion 问卷管理
     * @return 结果
     */
    public int updateFrontQuestion(FrontQuestion frontQuestion);

    /**
     * 批量删除问卷管理
     *
     * @param ids 需要删除的问卷管理主键集合
     * @return 结果
     */
    public int deleteFrontQuestionByIds(Long[] ids);

    /**
     * 删除问卷管理信息
     *
     * @param id 问卷管理主键
     * @return 结果
     */
    public int deleteFrontQuestionById(Long id);

    //根据问卷答题统计结果
    Map<String, Object> getFrontQuestionList(int questId);
}
