package com.ruoyi.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 问卷套餐类型对象 front_question_meun
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@Setter
@Data
@TableName("front_question_meun")
public class FrontQuestionMeun extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 问卷套餐名称 */
    @Excel(name = "问卷套餐名称")
    private String name;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Long isDel;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setIsDel(Long isDel)
    {
        this.isDel = isDel;
    }

    public Long getIsDel()
    {
        return isDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("isDel", getIsDel())
            .toString();
    }
}
