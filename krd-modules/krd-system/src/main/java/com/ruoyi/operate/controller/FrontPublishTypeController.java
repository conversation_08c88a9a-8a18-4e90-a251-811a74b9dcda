package com.ruoyi.operate.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.operate.domain.FrontPublishType;
import com.ruoyi.operate.service.IFrontPublishTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 消息推送类型Controller
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/type")
public class FrontPublishTypeController extends BaseController
{
    @Autowired
    private IFrontPublishTypeService frontPublishTypeService;

    /**
     * 查询消息推送类型列表
     */
    @RequiresPermissions("system:type:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontPublishType frontPublishType)
    {
        startPage();
        List<FrontPublishType> list = frontPublishTypeService.selectFrontPublishTypeList(frontPublishType);
        return getDataTable(list);
    }

    /**
     * 导出消息推送类型列表
     */
    @RequiresPermissions("system:type:export")
    @Log(title = "消息推送类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontPublishType frontPublishType)
    {
        List<FrontPublishType> list = frontPublishTypeService.selectFrontPublishTypeList(frontPublishType);
        ExcelUtil<FrontPublishType> util = new ExcelUtil<FrontPublishType>(FrontPublishType.class);
        util.exportExcel(response, list, "消息推送类型数据");
    }

    /**
     * 获取消息推送类型详细信息
     */
    @RequiresPermissions("system:type:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontPublishTypeService.selectFrontPublishTypeById(id));
    }

    /**
     * 新增消息推送类型
     */
    @RequiresPermissions("system:type:add")
    @Log(title = "消息推送类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontPublishType frontPublishType)
    {
        return toAjax(frontPublishTypeService.insertFrontPublishType(frontPublishType));
    }

    /**
     * 修改消息推送类型
     */
    @RequiresPermissions("system:type:edit")
    @Log(title = "消息推送类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontPublishType frontPublishType)
    {
        return toAjax(frontPublishTypeService.updateFrontPublishType(frontPublishType));
    }

    /**
     * 删除消息推送类型
     */
    @RequiresPermissions("system:type:remove")
    @Log(title = "消息推送类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontPublishTypeService.deleteFrontPublishTypeByIds(ids));
    }
}
