package com.ruoyi.operate.service;

import com.ruoyi.operate.domain.FrontTodo;

import java.util.List;

/**
 * 待办事项Service接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface IFrontTodoService
{
    /**
     * 查询待办事项
     *
     * @param id 待办事项主键
     * @return 待办事项
     */
    public FrontTodo selectFrontTodoById(Long id);

    /**
     * 查询待办事项列表
     *
     * @param frontTodo 待办事项
     * @return 待办事项集合
     */
    public List<FrontTodo> selectFrontTodoList(FrontTodo frontTodo);

    /**
     * 新增待办事项
     *
     * @param frontTodo 待办事项
     * @return 结果
     */
    public int insertFrontTodo(FrontTodo frontTodo);

    /**
     * 修改待办事项
     *
     * @param frontTodo 待办事项
     * @return 结果
     */
    public int updateFrontTodo(FrontTodo frontTodo);

    /**
     * 批量删除待办事项
     *
     * @param ids 需要删除的待办事项主键集合
     * @return 结果
     */
    public int deleteFrontTodoByIds(Long[] ids);

    /**
     * 删除待办事项信息
     *
     * @param id 待办事项主键
     * @return 结果
     */
    public int deleteFrontTodoById(Long id);
}
