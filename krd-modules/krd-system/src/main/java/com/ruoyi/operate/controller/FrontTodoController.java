package com.ruoyi.operate.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.operate.domain.FrontTodo;
import com.ruoyi.operate.service.IFrontTodoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 待办事项Controller
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@RestController
@RequestMapping("/todo")
public class FrontTodoController extends BaseController
{
    @Autowired
    private IFrontTodoService frontTodoService;

    /**
     * 查询待办事项列表
     */
    @RequiresPermissions("system:todo:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontTodo frontTodo)
    {
        startPage();
        List<FrontTodo> list = frontTodoService.selectFrontTodoList(frontTodo);
        return getDataTable(list);
    }

    /**
     * 导出待办事项列表
     */
    @RequiresPermissions("system:todo:export")
    @Log(title = "待办事项", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontTodo frontTodo)
    {
        List<FrontTodo> list = frontTodoService.selectFrontTodoList(frontTodo);
        ExcelUtil<FrontTodo> util = new ExcelUtil<FrontTodo>(FrontTodo.class);
        util.exportExcel(response, list, "待办事项数据");
    }

    /**
     * 获取待办事项详细信息
     */
    @RequiresPermissions("system:todo:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontTodoService.selectFrontTodoById(id));
    }

    /**
     * 新增待办事项
     */
    @RequiresPermissions("system:todo:add")
    @Log(title = "待办事项", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontTodo frontTodo)
    {
        return toAjax(frontTodoService.insertFrontTodo(frontTodo));
    }

    /**
     * 修改待办事项
     */
    @RequiresPermissions("system:todo:edit")
    @Log(title = "待办事项", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontTodo frontTodo)
    {
        return toAjax(frontTodoService.updateFrontTodo(frontTodo));
    }

    /**
     * 删除待办事项
     */
    @RequiresPermissions("system:todo:remove")
    @Log(title = "待办事项", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontTodoService.deleteFrontTodoByIds(ids));
    }
}
