package com.ruoyi.operate.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.mapper.FrontAdvertMapper;
import com.ruoyi.operate.service.IFrontAdvertService;
import com.ruoyi.system.api.domain.FrontAdvert;
import com.ruoyi.system.api.oss.OssUrlCleanerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广告管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class FrontAdvertServiceImpl implements IFrontAdvertService
{
    @Autowired
    private FrontAdvertMapper frontAdvertMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    /**
     * 查询广告管理
     *
     * @param id 广告管理主键
     * @return 广告管理
     */
    @Override
    public FrontAdvert selectFrontAdvertById(Long id)
    {
        FrontAdvert frontAdvert = frontAdvertMapper.selectFrontAdvertById(id);
        frontAdvert.setMaterial(ossUrlCleanerUtil.getSignatureUrl(frontAdvert.getMaterial()));
        return frontAdvert;
    }

    /**
     * 查询广告管理列表
     *
     * @param frontAdvert 广告管理
     * @return 广告管理
     */
    @Override
    public List<FrontAdvert> selectFrontAdvertList(FrontAdvert frontAdvert)
    {
        List<FrontAdvert> frontAdverts = frontAdvertMapper.selectFrontAdvertList(frontAdvert);
        frontAdverts.forEach(advert -> {
            advert.setMaterial(ossUrlCleanerUtil.getSignatureUrl(advert.getMaterial()));
        });
        return frontAdverts;
    }

    /**
     * 新增广告管理
     *
     * @param frontAdvert 广告管理
     * @return 结果
     */
    @Override
    public int insertFrontAdvert(FrontAdvert frontAdvert)
    {
        frontAdvert.setMaterial(ossUrlCleanerUtil.cleanUrlsToString(frontAdvert.getMaterial()));
        frontAdvert.setCreateTime(DateUtils.getNowDate());
        return frontAdvertMapper.insertFrontAdvert(frontAdvert);
    }

    /**
     * 修改广告管理
     *
     * @param frontAdvert 广告管理
     * @return 结果
     */
    @Override
    public int updateFrontAdvert(FrontAdvert frontAdvert)
    {
        frontAdvert.setMaterial(ossUrlCleanerUtil.cleanUrlsToString(frontAdvert.getMaterial()));
        frontAdvert.setUpdateTime(DateUtils.getNowDate());
        return frontAdvertMapper.updateFrontAdvert(frontAdvert);
    }

    /**
     * 批量删除广告管理
     *
     * @param ids 需要删除的广告管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontAdvertByIds(Long[] ids)
    {
        return frontAdvertMapper.deleteFrontAdvertByIds(ids);
    }

    /**
     * 删除广告管理信息
     *
     * @param id 广告管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontAdvertById(Long id)
    {
        return frontAdvertMapper.deleteFrontAdvertById(id);
    }
}
