package com.ruoyi.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 消息推送类型对象 front_publish_type
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Data
@TableName("front_publish_type")
public class FrontPublishType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 推送消息类型名称 */
    @Excel(name = "推送消息类型名称")
    private String name;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Long isDel;

    public void setId(Long id)
    {
        this.id = id;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public void setIsDel(Long isDel)
    {
        this.isDel = isDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("isDel", getIsDel())
            .toString();
    }
}
