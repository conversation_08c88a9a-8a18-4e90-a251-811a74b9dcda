package com.ruoyi.operate.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.operate.service.IFrontQuestionService;
import com.ruoyi.system.api.domain.FrontQuestion;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 问卷管理Controller
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/question")
@Tag(name = "问卷管理", description = "问卷管理")
public class FrontQuestionController extends BaseController
{
    @Autowired
    private IFrontQuestionService frontQuestionService;

    /**
     * 查询问卷管理列表
     */
    @RequiresPermissions("operate:question:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontQuestion frontQuestion)
    {
        startPage();
        List<FrontQuestion> list = frontQuestionService.selectFrontQuestionList(frontQuestion);
        return getDataTable(list);
    }

    /**
     * 导出问卷管理列表
     */
    @RequiresPermissions("operate:question:export")
    @Log(title = "问卷管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontQuestion frontQuestion)
    {
        List<FrontQuestion> list = frontQuestionService.selectFrontQuestionList(frontQuestion);
        ExcelUtil<FrontQuestion> util = new ExcelUtil<FrontQuestion>(FrontQuestion.class);
        util.exportExcel(response, list, "问卷管理数据");
    }

    /**
     * 获取问卷管理详细信息
     */
    @RequiresPermissions("operate:question:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontQuestionService.selectFrontQuestionById(id));
    }

    /**
     * 新增问卷管理
     */
    @RequiresPermissions("operate:question:add")
    @Log(title = "问卷管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontQuestion frontQuestion)
    {
        Long userId = SecurityUtils.getUserId();
        frontQuestion.setCreateUserId(userId);

        return toAjax(frontQuestionService.insertFrontQuestion(frontQuestion));
    }

    /**
     * 修改问卷管理
     */
    @RequiresPermissions("operate:question:edit")
    @Log(title = "问卷管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontQuestion frontQuestion)
    {
        return toAjax(frontQuestionService.updateFrontQuestion(frontQuestion));
    }

    /**
     * 删除问卷管理
     */
    @RequiresPermissions("operate:question:remove")
    @Log(title = "问卷管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontQuestionService.deleteFrontQuestionByIds(ids));
    }


    //获取问卷统计信息
    @GetMapping("/getFrontQuestionList/{id}")
    @Operation(description = "获取问卷统计信息")
    public AjaxResult getFrontQuestionList(@PathVariable("id") int id)
        {
            return success(frontQuestionService.getFrontQuestionList(id));
        }
}
