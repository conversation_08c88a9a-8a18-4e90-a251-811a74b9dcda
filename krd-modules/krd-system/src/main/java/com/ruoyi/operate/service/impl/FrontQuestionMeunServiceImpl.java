package com.ruoyi.operate.service.impl;

import com.ruoyi.operate.domain.FrontQuestionMeun;
import com.ruoyi.operate.mapper.FrontQuestionMeunMapper;
import com.ruoyi.operate.service.IFrontQuestionMeunService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 问卷套餐类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class FrontQuestionMeunServiceImpl implements IFrontQuestionMeunService
{
    @Autowired
    private FrontQuestionMeunMapper frontQuestionMeunMapper;

    /**
     * 查询问卷套餐类型
     *
     * @param id 问卷套餐类型主键
     * @return 问卷套餐类型
     */
    @Override
    public FrontQuestionMeun selectFrontQuestionMeunById(Long id)
    {
        return frontQuestionMeunMapper.selectFrontQuestionMeunById(id);
    }

    /**
     * 查询问卷套餐类型列表
     *
     * @param frontQuestionMeun 问卷套餐类型
     * @return 问卷套餐类型
     */
    @Override
    public List<FrontQuestionMeun> selectFrontQuestionMeunList(FrontQuestionMeun frontQuestionMeun)
    {
        return frontQuestionMeunMapper.selectFrontQuestionMeunList(frontQuestionMeun);
    }

    /**
     * 新增问卷套餐类型
     *
     * @param frontQuestionMeun 问卷套餐类型
     * @return 结果
     */
    @Override
    public int insertFrontQuestionMeun(FrontQuestionMeun frontQuestionMeun)
    {
        return frontQuestionMeunMapper.insertFrontQuestionMeun(frontQuestionMeun);
    }

    /**
     * 修改问卷套餐类型
     *
     * @param frontQuestionMeun 问卷套餐类型
     * @return 结果
     */
    @Override
    public int updateFrontQuestionMeun(FrontQuestionMeun frontQuestionMeun)
    {
        return frontQuestionMeunMapper.updateFrontQuestionMeun(frontQuestionMeun);
    }

    /**
     * 批量删除问卷套餐类型
     *
     * @param ids 需要删除的问卷套餐类型主键
     * @return 结果
     */
    @Override
    public int deleteFrontQuestionMeunByIds(Long[] ids)
    {
        return frontQuestionMeunMapper.deleteFrontQuestionMeunByIds(ids);
    }

    /**
     * 删除问卷套餐类型信息
     *
     * @param id 问卷套餐类型主键
     * @return 结果
     */
    @Override
    public int deleteFrontQuestionMeunById(Long id)
    {
        return frontQuestionMeunMapper.deleteFrontQuestionMeunById(id);
    }
}
