package com.ruoyi.operate.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.operate.domain.FrontPublish;
import com.ruoyi.operate.service.IFrontPublishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 推送消息Controller
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/publish")
public class FrontPublishController extends BaseController
{
    @Autowired
    private IFrontPublishService frontPublishService;

    /**
     * 查询推送消息列表
     */
    @RequiresPermissions("operate:publish:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontPublish frontPublish)
    {
        startPage();
        List<FrontPublish> list = frontPublishService.selectFrontPublishList(frontPublish);
        return getDataTable(list);
    }

    /**
     * 导出推送消息列表
     */
    @RequiresPermissions("operate:publish:export")
    @Log(title = "推送消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontPublish frontPublish)
    {
        List<FrontPublish> list = frontPublishService.selectFrontPublishList(frontPublish);
        ExcelUtil<FrontPublish> util = new ExcelUtil<FrontPublish>(FrontPublish.class);
        util.exportExcel(response, list, "推送消息数据");
    }

    /**
     * 获取推送消息详细信息
     */
    @RequiresPermissions("operate:publish:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontPublishService.selectFrontPublishById(id));
    }

    /**
     * 新增推送消息
     */
    @RequiresPermissions("operate:publish:add")
    @Log(title = "推送消息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontPublish frontPublish)
    {
        return toAjax(frontPublishService.insertFrontPublish(frontPublish));
    }

    /**
     * 修改推送消息
     */
    @RequiresPermissions("operate:publish:edit")
    @Log(title = "推送消息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontPublish frontPublish)
    {
        return toAjax(frontPublishService.updateFrontPublish(frontPublish));
    }

    /**
     * 删除推送消息
     */
    @RequiresPermissions("operate:publish:remove")
    @Log(title = "推送消息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontPublishService.deleteFrontPublishByIds(ids));
    }
}
