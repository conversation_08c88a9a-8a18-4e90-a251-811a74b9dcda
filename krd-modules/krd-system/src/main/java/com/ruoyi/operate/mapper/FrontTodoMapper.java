package com.ruoyi.operate.mapper;

import com.ruoyi.operate.domain.FrontTodo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
/**
 * 待办事项Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface FrontTodoMapper
{
    /**
     * 查询待办事项
     *
     * @param id 待办事项主键
     * @return 待办事项
     */
    public FrontTodo selectFrontTodoById(Long id);

    /**
     * 查询待办事项列表
     *
     * @param frontTodo 待办事项
     * @return 待办事项集合
     */
    public List<FrontTodo> selectFrontTodoList(FrontTodo frontTodo);

    /**
     * 新增待办事项
     *
     * @param frontTodo 待办事项
     * @return 结果
     */
    public int insertFrontTodo(FrontTodo frontTodo);

    /**
     * 修改待办事项
     *
     * @param frontTodo 待办事项
     * @return 结果
     */
    public int updateFrontTodo(FrontTodo frontTodo);

    /**
     * 删除待办事项
     *
     * @param id 待办事项主键
     * @return 结果
     */
    public int deleteFrontTodoById(Long id);

    /**
     * 批量删除待办事项
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontTodoByIds(Long[] ids);
}
