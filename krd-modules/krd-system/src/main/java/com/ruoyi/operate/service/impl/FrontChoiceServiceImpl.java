package com.ruoyi.operate.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.FrontChoice;
import com.ruoyi.system.api.mapper.FrontChoiceMapper;
import com.ruoyi.operate.service.IFrontChoiceService;
import com.ruoyi.system.api.oss.OssUrlCleanerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 精选内容Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class FrontChoiceServiceImpl implements IFrontChoiceService
{
    @Autowired
    private FrontChoiceMapper frontChoiceMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    /**
     * 查询精选内容
     *
     * @param id 精选内容主键
     * @return 精选内容
     */
    @Override
    public FrontChoice selectFrontChoiceById(Long id)
    {
        FrontChoice frontChoice = frontChoiceMapper.selectFrontChoiceById(id);
        frontChoice.setCover(ossUrlCleanerUtil.getSignatureUrl(frontChoice.getCover()));
        frontChoice.setInfo(ossUrlCleanerUtil.getSignatureEditor(frontChoice.getInfo()));
        return frontChoice;
    }

    /**
     * 查询精选内容列表
     *
     * @param frontChoice 精选内容
     * @return 精选内容
     */
    @Override
    public List<FrontChoice> selectFrontChoiceList(FrontChoice frontChoice)
    {
        List<FrontChoice> frontChoices = frontChoiceMapper.selectFrontChoiceList(frontChoice);
        for (FrontChoice frontChoiceList : frontChoices) {
            frontChoiceList.setCover(ossUrlCleanerUtil.getSignatureUrl(frontChoiceList.getCover()));
            frontChoiceList.setInfo(ossUrlCleanerUtil.getSignatureEditor(frontChoiceList.getInfo()));
        }
        return frontChoices;
    }

    /**
     * 新增精选内容
     *
     * @param frontChoice 精选内容
     * @return 结果
     */
    @Override
    public int insertFrontChoice(FrontChoice frontChoice)
    {
        frontChoice.setCover(ossUrlCleanerUtil.cleanUrlsToString(frontChoice.getCover()));
        frontChoice.setInfo(ossUrlCleanerUtil.cleanUrlsToString(frontChoice.getInfo()));
        return frontChoiceMapper.insertFrontChoice(frontChoice);
    }

    /**
     * 修改精选内容
     *
     * @param frontChoice 精选内容
     * @return 结果
     */
    @Override
    public int updateFrontChoice(FrontChoice frontChoice)
    {
        frontChoice.setUpdateTime(DateUtils.getNowDate());
        frontChoice.setCover(ossUrlCleanerUtil.cleanUrlsToString(frontChoice.getCover()));
        return frontChoiceMapper.updateFrontChoice(frontChoice);
    }

    /**
     * 批量删除精选内容
     *
     * @param ids 需要删除的精选内容主键
     * @return 结果
     */
    @Override
    public int deleteFrontChoiceByIds(Long[] ids)
    {
        return frontChoiceMapper.deleteFrontChoiceByIds(ids);
    }

    /**
     * 删除精选内容信息
     *
     * @param id 精选内容主键
     * @return 结果
     */
    @Override
    public int deleteFrontChoiceById(Long id)
    {
        return frontChoiceMapper.deleteFrontChoiceById(id);
    }
}
