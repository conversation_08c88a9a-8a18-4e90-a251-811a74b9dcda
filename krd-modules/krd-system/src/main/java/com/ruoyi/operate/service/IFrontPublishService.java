package com.ruoyi.operate.service;

import java.util.List;
import com.ruoyi.operate.domain.FrontPublish;

/**
 * 推送消息Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IFrontPublishService 
{
    /**
     * 查询推送消息
     * 
     * @param id 推送消息主键
     * @return 推送消息
     */
    public FrontPublish selectFrontPublishById(Long id);

    /**
     * 查询推送消息列表
     * 
     * @param frontPublish 推送消息
     * @return 推送消息集合
     */
    public List<FrontPublish> selectFrontPublishList(FrontPublish frontPublish);

    /**
     * 新增推送消息
     * 
     * @param frontPublish 推送消息
     * @return 结果
     */
    public int insertFrontPublish(FrontPublish frontPublish);

    /**
     * 修改推送消息
     * 
     * @param frontPublish 推送消息
     * @return 结果
     */
    public int updateFrontPublish(FrontPublish frontPublish);

    /**
     * 批量删除推送消息
     * 
     * @param ids 需要删除的推送消息主键集合
     * @return 结果
     */
    public int deleteFrontPublishByIds(Long[] ids);

    /**
     * 删除推送消息信息
     * 
     * @param id 推送消息主键
     * @return 结果
     */
    public int deleteFrontPublishById(Long id);
}
