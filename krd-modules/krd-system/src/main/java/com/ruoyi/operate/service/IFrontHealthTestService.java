package com.ruoyi.operate.service;

import com.ruoyi.system.api.domain.FrontHealthTest;

import java.util.List;

/**
 * 健康管理测试Service接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IFrontHealthTestService
{
    /**
     * 查询健康管理测试
     *
     * @param id 健康管理测试主键
     * @return 健康管理测试
     */
    public FrontHealthTest selectFrontHealthTestById(Long id);

    /**
     * 查询健康管理测试列表
     *
     * @param frontHealthTest 健康管理测试
     * @return 健康管理测试集合
     */
    public List<FrontHealthTest> selectFrontHealthTestList(FrontHealthTest frontHealthTest);

    /**
     * 新增健康管理测试
     *
     * @param frontHealthTest 健康管理测试
     * @return 结果
     */
    public int insertFrontHealthTest(FrontHealthTest frontHealthTest);

    /**
     * 修改健康管理测试
     *
     * @param frontHealthTest 健康管理测试
     * @return 结果
     */
    public int updateFrontHealthTest(FrontHealthTest frontHealthTest);

    /**
     * 批量删除健康管理测试
     *
     * @param ids 需要删除的健康管理测试主键集合
     * @return 结果
     */
    public int deleteFrontHealthTestByIds(Long[] ids);

    /**
     * 删除健康管理测试信息
     *
     * @param id 健康管理测试主键
     * @return 结果
     */
    public int deleteFrontHealthTestById(Long id);
}
