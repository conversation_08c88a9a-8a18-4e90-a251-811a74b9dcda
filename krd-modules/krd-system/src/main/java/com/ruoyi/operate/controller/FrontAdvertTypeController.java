package com.ruoyi.operate.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontAdvertType;
import com.ruoyi.operate.service.IFrontAdvertTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 广告类型Controller
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/advertType")
public class FrontAdvertTypeController extends BaseController
{
    @Autowired
    private IFrontAdvertTypeService frontAdvertTypeService;

    /**
     * 查询广告类型列表
     */
    @RequiresPermissions("system:type:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontAdvertType frontAdvertType)
    {
        startPage();
        List<FrontAdvertType> list = frontAdvertTypeService.selectFrontAdvertTypeList(frontAdvertType);
        return getDataTable(list);
    }

    /**
     * 导出广告类型列表
     */
    @RequiresPermissions("system:type:export")
    @Log(title = "广告类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontAdvertType frontAdvertType)
    {
        List<FrontAdvertType> list = frontAdvertTypeService.selectFrontAdvertTypeList(frontAdvertType);
        ExcelUtil<FrontAdvertType> util = new ExcelUtil<FrontAdvertType>(FrontAdvertType.class);
        util.exportExcel(response, list, "广告类型数据");
    }

    /**
     * 获取广告类型详细信息
     */
    @RequiresPermissions("system:type:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontAdvertTypeService.selectFrontAdvertTypeById(id));
    }

    /**
     * 新增广告类型
     */
    @RequiresPermissions("system:type:add")
    @Log(title = "广告类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontAdvertType frontAdvertType)
    {
        return toAjax(frontAdvertTypeService.insertFrontAdvertType(frontAdvertType));
    }

    /**
     * 修改广告类型
     */
    @RequiresPermissions("system:type:edit")
    @Log(title = "广告类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontAdvertType frontAdvertType)
    {
        return toAjax(frontAdvertTypeService.updateFrontAdvertType(frontAdvertType));
    }

    /**
     * 删除广告类型
     */
    @RequiresPermissions("system:type:remove")
    @Log(title = "广告类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontAdvertTypeService.deleteFrontAdvertTypeByIds(ids));
    }
}
