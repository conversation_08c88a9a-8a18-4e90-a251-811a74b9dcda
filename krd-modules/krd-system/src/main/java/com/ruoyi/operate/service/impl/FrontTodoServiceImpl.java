package com.ruoyi.operate.service.impl;

import com.ruoyi.operate.domain.FrontTodo;
import com.ruoyi.operate.mapper.FrontTodoMapper;
import com.ruoyi.operate.service.IFrontTodoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 待办事项Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Service
public class FrontTodoServiceImpl implements IFrontTodoService
{
    @Autowired
    private FrontTodoMapper frontTodoMapper;

    /**
     * 查询待办事项
     *
     * @param id 待办事项主键
     * @return 待办事项
     */
    @Override
    public FrontTodo selectFrontTodoById(Long id)
    {
        return frontTodoMapper.selectFrontTodoById(id);
    }

    /**
     * 查询待办事项列表
     *
     * @param frontTodo 待办事项
     * @return 待办事项
     */
    @Override
    public List<FrontTodo> selectFrontTodoList(FrontTodo frontTodo)
    {
        return frontTodoMapper.selectFrontTodoList(frontTodo);
    }

    /**
     * 新增待办事项
     *
     * @param frontTodo 待办事项
     * @return 结果
     */
    @Override
    public int insertFrontTodo(FrontTodo frontTodo)
    {
        frontTodo.setCreateTime(LocalDateTime.now());
        return frontTodoMapper.insertFrontTodo(frontTodo);
    }

    /**
     * 修改待办事项
     *
     * @param frontTodo 待办事项
     * @return 结果
     */
    @Override
    public int updateFrontTodo(FrontTodo frontTodo)
    {
        return frontTodoMapper.updateFrontTodo(frontTodo);
    }

    /**
     * 批量删除待办事项
     *
     * @param ids 需要删除的待办事项主键
     * @return 结果
     */
    @Override
    public int deleteFrontTodoByIds(Long[] ids)
    {
        return frontTodoMapper.deleteFrontTodoByIds(ids);
    }

    /**
     * 删除待办事项信息
     *
     * @param id 待办事项主键
     * @return 结果
     */
    @Override
    public int deleteFrontTodoById(Long id)
    {
        return frontTodoMapper.deleteFrontTodoById(id);
    }
}
