package com.ruoyi.operate.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontAdvert;
import com.ruoyi.operate.service.IFrontAdvertService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 广告管理Controller
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/advert")
public class FrontAdvertController extends BaseController
{
    @Autowired
    private IFrontAdvertService frontAdvertService;

    /**
     * 查询广告管理列表
     */
    @RequiresPermissions("system:advert:list")
    @GetMapping("/list")
    public TableDataInfo list(FrontAdvert frontAdvert)
    {
        startPage();
        List<FrontAdvert> list = frontAdvertService.selectFrontAdvertList(frontAdvert);
        return getDataTable(list);
    }

    /**
     * 导出广告管理列表
     */
    @RequiresPermissions("system:advert:export")
    @Log(title = "广告管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontAdvert frontAdvert)
    {
        List<FrontAdvert> list = frontAdvertService.selectFrontAdvertList(frontAdvert);
        ExcelUtil<FrontAdvert> util = new ExcelUtil<FrontAdvert>(FrontAdvert.class);
        util.exportExcel(response, list, "广告管理数据");
    }

    /**
     * 获取广告管理详细信息
     */
    @RequiresPermissions("system:advert:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontAdvertService.selectFrontAdvertById(id));
    }

    /**
     * 新增广告管理
     */
    @RequiresPermissions("system:advert:add")
    @Log(title = "广告管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontAdvert frontAdvert)
    {
        return toAjax(frontAdvertService.insertFrontAdvert(frontAdvert));
    }

    /**
     * 修改广告管理
     */
    @RequiresPermissions("system:advert:edit")
    @Log(title = "广告管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontAdvert frontAdvert)
    {
        return toAjax(frontAdvertService.updateFrontAdvert(frontAdvert));
    }

    /**
     * 删除广告管理
     */
    @RequiresPermissions("system:advert:remove")
    @Log(title = "广告管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontAdvertService.deleteFrontAdvertByIds(ids));
    }
}
