package com.ruoyi.operate.service;

import com.ruoyi.operate.domain.FrontPublishType;

import java.util.List;

/**
 * 消息推送类型Service接口
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IFrontPublishTypeService
{
    /**
     * 查询消息推送类型
     *
     * @param id 消息推送类型主键
     * @return 消息推送类型
     */
    public FrontPublishType selectFrontPublishTypeById(Long id);

    /**
     * 查询消息推送类型列表
     *
     * @param frontPublishType 消息推送类型
     * @return 消息推送类型集合
     */
    public List<FrontPublishType> selectFrontPublishTypeList(FrontPublishType frontPublishType);

    /**
     * 新增消息推送类型
     *
     * @param frontPublishType 消息推送类型
     * @return 结果
     */
    public int insertFrontPublishType(FrontPublishType frontPublishType);

    /**
     * 修改消息推送类型
     *
     * @param frontPublishType 消息推送类型
     * @return 结果
     */
    public int updateFrontPublishType(FrontPublishType frontPublishType);

    /**
     * 批量删除消息推送类型
     *
     * @param ids 需要删除的消息推送类型主键集合
     * @return 结果
     */
    public int deleteFrontPublishTypeByIds(Long[] ids);

    /**
     * 删除消息推送类型信息
     *
     * @param id 消息推送类型主键
     * @return 结果
     */
    public int deleteFrontPublishTypeById(Long id);
}
