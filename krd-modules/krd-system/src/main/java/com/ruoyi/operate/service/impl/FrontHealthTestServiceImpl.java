package com.ruoyi.operate.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.FrontHealthTest;
import com.ruoyi.system.api.mapper.FrontHealthTestMapper;
import com.ruoyi.operate.service.IFrontHealthTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 健康管理测试Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class FrontHealthTestServiceImpl implements IFrontHealthTestService
{
    @Autowired
    private FrontHealthTestMapper frontHealthTestMapper;

    /**
     * 查询健康管理测试
     *
     * @param id 健康管理测试主键
     * @return 健康管理测试
     */
    @Override
    public FrontHealthTest selectFrontHealthTestById(Long id)
    {
        return frontHealthTestMapper.selectFrontHealthTestById(id);
    }

    /**
     * 查询健康管理测试列表
     *
     * @param frontHealthTest 健康管理测试
     * @return 健康管理测试
     */
    @Override
    public List<FrontHealthTest> selectFrontHealthTestList(FrontHealthTest frontHealthTest)
    {
        return frontHealthTestMapper.selectFrontHealthTestList(frontHealthTest);
    }

    /**
     * 新增健康管理测试
     *
     * @param frontHealthTest 健康管理测试
     * @return 结果
     */
    @Override
    public int insertFrontHealthTest(FrontHealthTest frontHealthTest)
    {
        frontHealthTest.setCreateTime(DateUtils.getNowDate());
        return frontHealthTestMapper.insertFrontHealthTest(frontHealthTest);
    }

    /**
     * 修改健康管理测试
     *
     * @param frontHealthTest 健康管理测试
     * @return 结果
     */
    @Override
    public int updateFrontHealthTest(FrontHealthTest frontHealthTest)
    {
        frontHealthTest.setUpdateTime(DateUtils.getNowDate());
        return frontHealthTestMapper.updateFrontHealthTest(frontHealthTest);
    }

    /**
     * 批量删除健康管理测试
     *
     * @param ids 需要删除的健康管理测试主键
     * @return 结果
     */
    @Override
    public int deleteFrontHealthTestByIds(Long[] ids)
    {
        return frontHealthTestMapper.deleteFrontHealthTestByIds(ids);
    }

    /**
     * 删除健康管理测试信息
     *
     * @param id 健康管理测试主键
     * @return 结果
     */
    @Override
    public int deleteFrontHealthTestById(Long id)
    {
        return frontHealthTestMapper.deleteFrontHealthTestById(id);
    }
}
