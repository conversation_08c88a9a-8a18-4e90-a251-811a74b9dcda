package com.ruoyi.operate.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.operate.mapper.FrontPublishMapper;
import com.ruoyi.operate.domain.FrontPublish;
import com.ruoyi.operate.service.IFrontPublishService;

/**
 * 推送消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class FrontPublishServiceImpl implements IFrontPublishService 
{
    @Autowired
    private FrontPublishMapper frontPublishMapper;

    /**
     * 查询推送消息
     * 
     * @param id 推送消息主键
     * @return 推送消息
     */
    @Override
    public FrontPublish selectFrontPublishById(Long id)
    {
        return frontPublishMapper.selectFrontPublishById(id);
    }

    /**
     * 查询推送消息列表
     * 
     * @param frontPublish 推送消息
     * @return 推送消息
     */
    @Override
    public List<FrontPublish> selectFrontPublishList(FrontPublish frontPublish)
    {
        return frontPublishMapper.selectFrontPublishList(frontPublish);
    }

    /**
     * 新增推送消息
     * 
     * @param frontPublish 推送消息
     * @return 结果
     */
    @Override
    public int insertFrontPublish(FrontPublish frontPublish)
    {
        frontPublish.setCreateTime(DateUtils.getNowDate());
        return frontPublishMapper.insertFrontPublish(frontPublish);
    }

    /**
     * 修改推送消息
     * 
     * @param frontPublish 推送消息
     * @return 结果
     */
    @Override
    public int updateFrontPublish(FrontPublish frontPublish)
    {
        frontPublish.setUpdateTime(DateUtils.getNowDate());
        return frontPublishMapper.updateFrontPublish(frontPublish);
    }

    /**
     * 批量删除推送消息
     * 
     * @param ids 需要删除的推送消息主键
     * @return 结果
     */
    @Override
    public int deleteFrontPublishByIds(Long[] ids)
    {
        return frontPublishMapper.deleteFrontPublishByIds(ids);
    }

    /**
     * 删除推送消息信息
     * 
     * @param id 推送消息主键
     * @return 结果
     */
    @Override
    public int deleteFrontPublishById(Long id)
    {
        return frontPublishMapper.deleteFrontPublishById(id);
    }
}
