package com.ruoyi.user.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.user.domain.entity.FrontHistoryPeriodEntity;
import com.ruoyi.user.domain.entity.FrontPeriodInfoEntity;
import com.ruoyi.user.mapper.FrontHistoryPeriodMapper;
import com.ruoyi.user.service.FrontPeriodInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * 经期记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Service
public class FrontPeriodInfoServiceImpl implements FrontPeriodInfoService {

    @Autowired
    private FrontUserMapper userMapper;

    @Autowired
    private FrontHistoryPeriodMapper frontHistoryPeriodMapper;

    /**
     * 新增经期记录 后端换算周期
     *
     * @return 结果
     */
    @Override
    public FrontPeriodInfoEntity selectFrontPeriodInfoByMonth(String inputMonth, Long userId) throws JsonProcessingException {
        FrontPeriodInfoEntity frontPeriodInfo = new FrontPeriodInfoEntity();
        frontPeriodInfo.setUserId(userId);

        FrontUser frontUser = userMapper.selectFrontUserById(userId);
        if (frontUser == null) {
            throw new IllegalArgumentException("用户信息未找到");
        }
        // 获取周期长度和经期天数
        Long periodCycle = null;
        Long periodLength = null;
        Date nearPeriodDate = null;
        //从当前用户历史经期记录中获取周期长度和经期天数 如果在之前就取历史最近的那一次记录
        FrontHistoryPeriodEntity frontHistoryPeriod = frontHistoryPeriodMapper.selectFrontHistoryPeriodByMonth(inputMonth , userId);
        if (frontHistoryPeriod != null) {
            periodLength = frontHistoryPeriod.getPeriodLength().longValue();
            periodCycle = frontHistoryPeriod.getPeriodCycle().longValue();
            nearPeriodDate = frontHistoryPeriod.getNearPeriodDate();
        } else {
            periodCycle = frontUser.getPeriodCycle();
            periodLength = frontUser.getPeriodLength();
            nearPeriodDate = frontUser.getNearPeriodDate();
        }
        LocalDate localNearPeriodDate = nearPeriodDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        // 目标月份
        YearMonth targetYM = YearMonth.parse(inputMonth , DateTimeFormatter.ofPattern("yyyy-MM"));
        // 构建周期链：从最近一次月经开始，逐步生成每个月的月经开始日，直到目标月份
        LocalDate currentPeriodStart = localNearPeriodDate;
        YearMonth currentYM = YearMonth.from(currentPeriodStart);
        while (currentYM.isBefore(targetYM)) {
            // 下一个月的月经开始日 = 本月月经开始日 + 周期长度
            currentPeriodStart = currentPeriodStart.plusDays(periodCycle);
            currentYM = YearMonth.from(currentPeriodStart);
        }
        // 现在 currentPeriodStart 是目标月份的月经开始日
        LocalDate periodStartInTargetMonth = currentPeriodStart;
        // 如果当前周期的开始月不是目标月，说明没有找到对应周期（比如用户输入了太远的月份）
        if (!YearMonth.from(periodStartInTargetMonth).equals(targetYM)) {
            throw new IllegalArgumentException("无法计算该月份的周期信息");
        }
        // 设置基础信息
        frontPeriodInfo.setCycleDays(periodCycle.intValue());
        frontPeriodInfo.setMenstrualPeriodDays(periodLength.intValue());

        // 1. 计算目标月份的经期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-M-d");

        List<String> periodDays = new ArrayList<>();
        for (int i = 0; i < periodLength; i++) {
            LocalDate day = periodStartInTargetMonth.plusDays(i);
            periodDays.add(day.format(formatter));
        }
        frontPeriodInfo.setPeriod(new ObjectMapper().writeValueAsString(periodDays));

        // 2. 计算排卵日 = 月经开始 - 14 天
        LocalDate ovulationDay = periodStartInTargetMonth.minusDays(14);
        frontPeriodInfo.setOvulaDay(ovulationDay.format(formatter));

        // 3. 计算排卵期 = 排卵日前5天 至 后4天
        List<String> ovulationPeriod = new ArrayList<>();
        for (int i = -5; i <= 4; i++) {
            LocalDate day = ovulationDay.plusDays(i);
            if (YearMonth.from(day).equals(targetYM)) {
                ovulationPeriod.add(day.format(formatter));
            }
        }
        frontPeriodInfo.setOvulation(new ObjectMapper().writeValueAsString(ovulationPeriod));

        // 4. 预测下次月经范围 = 本次月经 + 周期长度 开始，持续经期长度天
        LocalDate earliestNextPeriod = periodStartInTargetMonth.plusDays(periodCycle);
        LocalDate latestNextPeriod = earliestNextPeriod.plusDays(periodLength);

        List<String> predPeriodDays = new ArrayList<>();
        LocalDate currentDate = earliestNextPeriod;
        while (!currentDate.isAfter(latestNextPeriod)) {
            predPeriodDays.add(currentDate.format(formatter));
            currentDate = currentDate.plusDays(1);
        }
        frontPeriodInfo.setPredPeriod(new ObjectMapper().writeValueAsString(predPeriodDays));

        return frontPeriodInfo;
    }

    /**
     * 计算两个日期之间的天数差
     */
    private long daysBetween(Date start , Date end) {
        return (end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000);
    }

    // 辅助方法：计算两个日期之间相差几个月
    private int calculateMonthsBetween(Date startDate , Date endDate) {
        Calendar start = Calendar.getInstance();
        start.setTime(startDate);
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);
        int diffYear = end.get(Calendar.YEAR) - start.get(Calendar.YEAR);
        int diffMonth = end.get(Calendar.MONTH) - start.get(Calendar.MONTH);
        return diffYear * 12 + diffMonth + 1; // 包括当月
    }

}
