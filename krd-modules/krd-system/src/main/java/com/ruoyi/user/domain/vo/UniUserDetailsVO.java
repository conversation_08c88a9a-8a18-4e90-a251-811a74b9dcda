package com.ruoyi.user.domain.vo;

import com.ruoyi.user.domain.entity.FrontAddressEntity;
import com.ruoyi.user.domain.entity.FrontFamilyEntity;
import com.ruoyi.user.domain.entity.FrontPeriodInfoEntity;
import com.ruoyi.user.domain.entity.FrontUserEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 10:38
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UniUserDetailsVO {
    /**
     * 用户基本信息
     */
    private FrontUserEntity userBaseInfo;

    /**
     * 用户家庭信息
     */
    private List<FrontFamilyEntity> familyList;
    /**
     * 经期信息
     */
    private FrontPeriodInfoEntity periodInfo;
    /**
     * 用户统计信息
     */
    private UserStatisticsVO statisticsInfo;
    /**
     * 收货地址
     */
    private List<FrontAddressEntity> addressList;
}
