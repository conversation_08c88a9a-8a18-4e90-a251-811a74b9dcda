package com.ruoyi.user.mapper;

import com.ruoyi.user.domain.entity.FrontHistoryPeriodEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户历史经期记录表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-10 15:32:15
 */
@Mapper
public interface FrontHistoryPeriodMapper extends BaseMapper<FrontHistoryPeriodEntity> {

    FrontHistoryPeriodEntity selectFrontHistoryPeriodByMonth(@Param("inputMonth") String inputMonth, @Param("uid") Long uid);
}
