package com.ruoyi.user.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 订单总表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-10 18:38:06
 */
@Data
@TableName("front_orders")
public class FrontOrdersEntity implements Serializable {

	private static final long serialVersionUID = 1932386787019649026L;

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 订单编号
	 */
	private String orderNumber;
	/**
	 * 用户ID
	 */
	private Long userId;
	/**
	 * 总金额
	 */
	private BigDecimal totalPrice;
	/**
	 * 抵扣类型
	 */
	private String deductionType;
	/**
	 * 抵扣金额总金额
	 */
	private BigDecimal deductionPrice;
	/**
	 * 支付金额
	 */
	private BigDecimal payPrice;
	/**
	 * 发货单流水号
	 */
	private String pushGoodsNo;
	/**
	 * 下单时间
	 */
	private Date createTime;
	/**
	 * 完成时间
	 */
	private Date finishTime;
	/**
	 * 是否售后 0-false 1-true
	 */
	private Integer isAfter;
	/**
	 * 0-待支付 1-待发货 2-待收货 3-待评价 4-售后订单
	 */
	private Integer status;
	/**
	 * 用户备注
	 */
	private String userRemark;
	/**
	 * 平台备注
	 */
	private String platRemark;
	/**
	 * 是否关闭订单 0-false 1-true
	 */
	private Integer closeOrder;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;
	/**
	 * 是否删除
	 */
	private Long isDel;
	/**
	 * 是否支付
	 */
	private Long paid;
	/**
	 * 支付方式
	 */
	private String payType;
	/**
	 * 商户系统内部订单号
	 */
	private Integer outTradeNo;
	/**
	 * 地址id
	 */
	private Long addressId;
	/**
	 * 收货人
	 */
	private String receiveName;
	/**
	 * 收货人电话
	 */
	private String receivePhone;
	/**
	 * 收货人地址——省市区
	 */
	private String receiveAddress;
	/**
	 * 收货详情地址
	 */
	private String receiveAddressDetail;
	/**
	 * 积分抵扣
	 */
	private BigDecimal pointsDeduction;
	/**
	 * 礼品卡抵扣
	 */
	private BigDecimal giftDeduction;
	/**
	 * 优惠券抵扣
	 */
	private BigDecimal couponDeduction;

}
