package com.ruoyi.user.service;

import com.ruoyi.user.domain.dto.FreezeDTO;
import com.ruoyi.user.domain.dto.UserManagerQueryDTO;
import com.ruoyi.user.domain.dto.UserOrderQueryDTO;
import com.ruoyi.user.domain.dto.UserReportQueryDTO;
import com.ruoyi.user.domain.entity.FrontGoodsEntity;
import com.ruoyi.user.domain.vo.UniUserDetailsVO;
import com.ruoyi.user.domain.vo.UserBalanceVO;
import com.ruoyi.system.api.domain.vo.UserCouponVO;
import com.ruoyi.system.api.vo.UserGiftVO;
import com.ruoyi.user.domain.vo.UserListVO;
import com.ruoyi.user.domain.vo.UserOrderInfoVO;
import com.ruoyi.user.domain.vo.UserReportInfoVO;
import com.ruoyi.user.domain.vo.UserSourceVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9 11:44
 */
public interface FrontUserManagerService {

    List<UserListVO> getUserList(UserManagerQueryDTO queryDTO);

    void freezeUser(FreezeDTO dto);

    void unfreezeUser(FreezeDTO dto);

    List<UserBalanceVO> getUserBalanceList(Long uid);

    List<UserCouponVO> getUserCouponList(Long uid);

    List<UserGiftVO> getUserGiftList(Long uid);

    List<UserSourceVO> getUserSourceList(Long uid);

    UniUserDetailsVO getUniUserDetails(Long uid);

    List<UserOrderInfoVO> getOrderList(UserOrderQueryDTO queryDTO);

    List<UserReportInfoVO> getCheckListPage(UserReportQueryDTO queryDTO);

    List<FrontGoodsEntity> getUserSpcoList(Long uid);
}
