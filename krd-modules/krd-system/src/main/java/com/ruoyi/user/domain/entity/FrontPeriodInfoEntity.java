package com.ruoyi.user.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 经期记录表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-10 14:55:25
 */
@Data
@TableName("front_period_info")
public class FrontPeriodInfoEntity implements Serializable {

	private static final long serialVersionUID = 1932330746164736001L;

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 
	 */
	private Long userId;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;
	/**
	 * 经期日期数组
	 */
	private String period;
	/**
	 * 排卵期
	 */
	private String ovulation;
	/**
	 * 排卵日
	 */
	private String ovulaDay;
	/**
	 * 预测经期
	 */
	private String predPeriod;
	/**
	 * 每月经期开始时间
	 */
	private Date periodStart;
	/**
	 * 每月经期结束时间
	 */
	private Date periodEnd;
	/**
	 * 比上周期提前延期了多少天
	 */
	private Integer compareTime;
	/**
	 * 经期天数
	 */
	private Integer menstrualPeriodDays;
	/**
	 * 周期天数
	 */
	private Integer cycleDays;
	/**
	 * 经期月份
	 */
	private String month;
	/**
	 * 备注
	 */
	private String remark;

}
