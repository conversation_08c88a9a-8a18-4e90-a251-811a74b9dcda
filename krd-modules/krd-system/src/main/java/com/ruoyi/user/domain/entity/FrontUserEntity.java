package com.ruoyi.user.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-09 12:00:54
 */
@Data
@TableName("front_user")
public class FrontUserEntity implements Serializable {

	private static final long serialVersionUID = 1931924441625227266L;

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 用户头像
	 */
	private String userIcon;
	/**
	 * 用户名
	 */
	private String userName;
	/**
	 * 城市
	 */
	private String userCity;
	/**
	 * 用户标签，存id
	 */
	private String userTag;
	/**
	 * 手机号码
	 */
	private String userMobile;
	/**
	 * 用户生日(存时间戳)
	 */
	private String userBirthday;
	/**
	 * 年龄
	 */
	private Integer userAge;
	/**
	 * 0-女 1-男
	 */
	private Integer userSex;
	/**
	 * 体重
	 */
	private Double userHeight;
	/**
	 * 创建时间，注册时间
	 */
	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 状态 (是否冻结) 0-false 1-true
	 */
	private Integer status;
	/**
	 * 用户IP
	 */
	private String userIp;
	/**
	 * 是否进行问卷调查 0-false 1-true
	 */
	private Integer isDoQuest;
	/**
	 * 身高
	 */
	private Double userWeight;
	/**
	 * 周期长度
	 */
	private Integer periodCycle;
	/**
	 * 经期长度
	 */
	private Integer periodLength;
	/**
	 * 最近一次月经
	 */
	private Date nearPeriodDate;
	/**
	 * 是否答过健康档案的问卷
	 */
	private Integer isAnswerHealth;
	/**
	 * 健康档案那里是否月经已过
	 */
	private Integer isGoPeriod;
	/**
	 * 登录次数
	 */
	private Integer loginCount;
}
