package com.ruoyi.user.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 订单-商品信息表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-10 23:52:02
 */
@Data
@TableName("front_orders_goods")
public class FrontOrdersGoodsEntity implements Serializable {

	private static final long serialVersionUID = 1932465788824252417L;

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 0-检测套餐 1-商品

	 */
	private Integer type;
	/**
	 * 订单ID
	 */
	private Long orderId;
	/**
	 * 商品ID
	 */
	private Long goodsId;
	/**
	 * 商品规格id
	 */
	private Long goodsSpecId;
	/**
	 * 购买数量
	 */
	private Integer count;
	/**
	 * 商品金额
	 */
	private BigDecimal price;
	/**
	 * 抵扣金额
	 */
	private BigDecimal discount;
	/**
	 * 应付金额
	 */
	private BigDecimal meetPrice;
	/**
	 * 实付金额
	 */
	private BigDecimal payPrice;
	/**
	 * 处理备注
	 */
	private String handleRemark;
	/**
	 * 售后退款原因
	 */
	private String afterReason;
	/**
	 * 售后状态
	 */
	private String afterStatus;
	/**
	 * 退款说明
	 */
	private String afterDesc;
	/**
	 * 退款金额
	 */
	private BigDecimal afterPrice;
	/**
	 * 申请时间
	 */
	private Date applyTime;
	/**
	 * 处理时间
	 */
	private Date handleTime;
	/**
	 * 实际退款金额
	 */
	private BigDecimal ackAfterPrice;
	/**
	 * 售后地址
	 */
	private String afterAddress;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;

}
