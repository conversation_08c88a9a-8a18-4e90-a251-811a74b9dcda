package com.ruoyi.user.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 商品表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-11 17:10:38
 */
@Data
@TableName("front_goods")
public class FrontGoodsEntity implements Serializable {

	private static final long serialVersionUID = 1932727162348544001L;

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 商品类型 0-检测套餐 1-商城商品
	 */
	private Integer goodsType;
	/**
	 * 检测套餐才有的
	 */
	private Integer packageId;
	/**
	 * 商品分类id
	 */
	private Long gategoryId;
	/**
	 * 套餐名称
	 */
	private String name;
	/**
	 * 副标题
	 */
	private String subtitle;
	/**
	 * 原价
	 */
	private BigDecimal price;
	/**
	 * 会员价
	 */
	private BigDecimal vipPrice;
	/**
	 * 库存
	 */
	private Integer amount;
	/**
	 * 商品货号
	 */
	private String goodsNo;
	/**
	 * 上下架 0-下架 1-上架
	 */
	private Integer isStatus;
	/**
	 * 0-不推荐 1-首页大图展示 2-首页小图展示 3-商城大图
	 */
	private String featuredFirst;
	/**
	 * 首页图片，featured_first  != 0 暂时该图片
	 */
	private String indexImage;
	/**
	 * 套餐首图 / 商品首图 地址
	 */
	private String firstPic;
	/**
	 * banner
	 */
	private String banner;
	/**
	 * 套餐单位 / 商品单位
	 */
	private String unit;
	/**
	 * 搜索关键字
	 */
	private String keywords;
	/**
	 * 详细介绍
	 */
	private String details;
	/**
	 * 规格ID
	 */
	private Long specId;
	/**
	 * 报告解读
	 */
	private String reportInfo;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;
	/**
	 * 0-未删除  1-删除
	 */
	private Integer isDel;
	/**
	 * 排序默认0（越大越靠前）
	 */
	private Integer sort;

}
