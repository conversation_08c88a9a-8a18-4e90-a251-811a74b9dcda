package com.ruoyi.user.mapper;

import com.ruoyi.user.domain.entity.FrontGoodsEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商品表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-11 17:10:38
 */
@Repository("userFrontGoodsMapper")
public interface FrontGoodsMapper extends BaseMapper<FrontGoodsEntity> {

    List<FrontGoodsEntity> getUserSpcoList(Long uid);
}
