package com.ruoyi.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.user.FrontTag;
import com.ruoyi.system.api.mapper.FrontTagMapper;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.user.service.IFrontTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签管理Service业务层处理
 *
 * @date 2025-05-09
 */
@Service
public class FrontTagServiceImpl implements IFrontTagService
{
    @Autowired
    private FrontTagMapper frontTagMapper;

    @Autowired
    private FrontUserMapper userMapper;

    /**
     * 查询标签管理
     *
     * @param id 标签管理主键
     * @return 标签管理
     */
    @Override
    public FrontTag selectFrontTagById(Long id)
    {
        return frontTagMapper.selectFrontTagById(id);
    }

    /**
     * 查询标签管理列表
     *
     * @param frontTag 标签管理
     * @return 标签管理
     */
    @Override
    public List<FrontTag> selectFrontTagList(FrontTag frontTag)
    {
        return frontTagMapper.selectFrontTagList(frontTag);
    }

    /**
     * 新增标签管理
     *
     * @param frontTag 标签管理
     * @return 结果
     */
    @Override
    public int insertFrontTag(FrontTag frontTag)
    {
        frontTag.setCreateTime(DateUtils.getNowDate());
        List<Integer> frontUsers = calculateUserCount(frontTag);
        if (frontUsers != null && !frontUsers.isEmpty()) frontTag.setUserCount(frontUsers.size());
        return frontTagMapper.insertFrontTag(frontTag);
    }

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public List<Integer> calculateUserCount(FrontTag frontTag) {
        List<Integer> result = new ArrayList<>();

        LambdaQueryWrapper<FrontUser> queryWrapper = new LambdaQueryWrapper<>();

        // 性别筛选
        if (frontTag.getTagSex() != null && frontTag.getTagSex() != 0) {
            queryWrapper.eq(FrontUser::getUserSex, frontTag.getTagSex());
        }

        // 城市筛选
        if (!"0".equals(frontTag.getTagCity())) {
            queryWrapper.eq(FrontUser::getUserCity, frontTag.getTagCity());
        }

        // 注册时间筛选
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (!"0".equals(frontTag.getTagRegister())) {
            try {
                JsonNode registerNode = objectMapper.readTree(frontTag.getTagRegister());
                JsonNode timeRange = registerNode.get("timeRange");
                String start = timeRange.get("start").asText();
                String end = timeRange.has("end") ? timeRange.get("end").asText() : "";
                String fixedDays = registerNode.get("fixedDays").asText();

                LocalDateTime now = LocalDateTime.now();

                if (!fixedDays.isEmpty()) {
                    int days = Integer.parseInt(fixedDays);
                    LocalDateTime startTime = now.minusDays(days);
                    queryWrapper.ge(FrontUser::getCreateTime, startTime);
                } else {
                    LocalDateTime startTime = LocalDate.parse(start, formatter).atStartOfDay();
                    LocalDateTime endTime = "".equals(end)
                            ? now
                            : LocalDate.parse(end, formatter).atStartOfDay().plusDays(1);
                    queryWrapper.between(FrontUser::getCreateTime, startTime, endTime);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        List<Integer> baseUserIds = userMapper.selectObjs(queryWrapper.select(FrontUser::getId))
                .stream()
                .map(obj -> ((Number) obj).intValue())
                .collect(Collectors.toList());

        result.addAll(baseUserIds);
        if ("0".equals(frontTag.getTagDeal()) && "0".equals(frontTag.getTagAmount())) {
            return result;
        }
        // 交易次数筛选
        if (!"0".equals(frontTag.getTagDeal())) {
            try {
                JsonNode dealNode = objectMapper.readTree(frontTag.getTagDeal());
                JsonNode timeRange = dealNode.get("timeRange");
                String start = safeGetString(timeRange, "start");
                String end = safeGetString(timeRange, "end");
                String count = safeGetString(dealNode, "count");
                List<Integer> dealUserIds = userMapper.selectUserDealCount(start, end, count);

                result.retainAll(dealUserIds);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 消费金额筛选
        if (!"0".equals(frontTag.getTagAmount())) {
            try {
                JsonNode amountNode = objectMapper.readTree(frontTag.getTagAmount());
                JsonNode timeRange = amountNode.get("timeRange");
                String start = safeGetString(timeRange, "start");
                String end = safeGetString(timeRange, "end");
                String amount = safeGetString(amountNode, "amount");

                List<Integer> amountUserIds = userMapper.selectUserDealAmount(start, end, amount);

                result.retainAll(amountUserIds);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return result;
    }

    public static String safeGetString(JsonNode node, String field) {
        if (node == null || !node.has(field)) {
            return "";
        }
        JsonNode valueNode = node.get(field);
        return (valueNode != null && !valueNode.isNull()) ? valueNode.asText("") : "";
    }

    /**
     * 修改标签管理
     *
     * @param frontTag 标签管理
     * @return 结果
     */
    @Override
    public int updateFrontTag(FrontTag frontTag)
    {
        frontTag.setUpdateTime(DateUtils.getNowDate());
        List<Integer> frontUsers = calculateUserCount(frontTag);
        if (frontUsers != null && !frontUsers.isEmpty()) frontTag.setUserCount(frontUsers.size());
        return frontTagMapper.updateFrontTag(frontTag);
    }

    /**
     * 批量删除标签管理
     *
     * @param ids 需要删除的标签管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontTagByIds(Long[] ids)
    {
        return frontTagMapper.deleteFrontTagByIds(ids);
    }

    /**
     * 删除标签管理信息
     *
     * @param id 标签管理主键
     * @return 结果
     */
    @Override
    public int deleteFrontTagById(Long id)
    {
        return frontTagMapper.deleteFrontTagById(id);
    }
}
