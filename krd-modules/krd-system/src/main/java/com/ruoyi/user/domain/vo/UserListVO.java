package com.ruoyi.user.domain.vo;

import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/9 11:55
 */
@Data
public class UserListVO {

    @Excel(name = "用户ID")
    private Long uid;

    @Excel(name = "用户名")
    private String uname;

    @Excel(name = "用户标签")
    private String utags;

    @Excel(name = "手机号码")
    private String umobile;

    @Excel(name = "余额(元)")
    private BigDecimal balanceTotal = new BigDecimal("0.00");
    @Excel(name = "优惠券(张)")
    private Integer couponCount = 0;
    @Excel(name = "礼品卡(张)")
    private Integer giftCount = 0;
    @Excel(name = "积分")
    private Integer sourceTotal = 0;

    @Excel(name = "注册时间")
    private String registerTime;

    @Excel(name = "状态")
    private String ustatusDesc;

    private String ustatus;
}
