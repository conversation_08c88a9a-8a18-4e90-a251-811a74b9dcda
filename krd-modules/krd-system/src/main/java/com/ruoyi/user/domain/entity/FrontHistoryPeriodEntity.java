package com.ruoyi.user.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户历史经期记录表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-10 15:32:15
 */
@Data
@TableName("front_history_period")
public class FrontHistoryPeriodEntity implements Serializable {

	private static final long serialVersionUID = 1932340013789802498L;

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Integer id;
	/**
	 * 周期长度
	 */
	private Integer periodCycle;
	/**
	 * 经期长度
	 */
	private Integer periodLength;
	/**
	 * 最近一次月经开始日
	 */
	private Date nearPeriodDate;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 创建时间
	 */
	private Date createTime;

}
