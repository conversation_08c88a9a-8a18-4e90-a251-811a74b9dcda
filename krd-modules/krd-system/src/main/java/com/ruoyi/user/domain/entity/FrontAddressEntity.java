package com.ruoyi.user.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 地址管理
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-10 16:14:11
 */
@Data
@TableName("front_address")
public class FrontAddressEntity implements Serializable {

	private static final long serialVersionUID = 1932350570349654017L;

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 
	 */
	private Long userId;
	/**
	 * 收货人
	 */
	private String name;
	/**
	 * 手机号码
	 */
	private String mobile;
	/**
	 * 所在地区
	 */
	private String area;
	/**
	 * 详细地址
	 */
	private String address;
	/**
	 * 地址标签
	 */
	private String tag;
	/**
	 * 是否默认 0 - 否  1-是
	 */
	private Integer isDefault;
	/**
	 * 是否删除 0-false 1-true
	 */
	private Integer isDel;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private Date updateBy;

}
