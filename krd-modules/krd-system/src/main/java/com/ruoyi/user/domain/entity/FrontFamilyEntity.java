package com.ruoyi.user.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 家庭成员
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-10 11:39:26
 */
@Data
@TableName("front_family")
public class FrontFamilyEntity implements Serializable {

	private static final long serialVersionUID = 1932281426606485505L;

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 
	 */
	private Long userId;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 0-女 1-男
	 */
	private Integer sex;
	/**
	 * 年龄
	 */
	private Integer age;
	/**
	 * 出生日期 —— 时间戳
	 */
	private String date;
	/**
	 * 体重(单位：kg)
	 */
	private Double weight;
	/**
	 * 身高(单位：cm)
	 */
	private Double height;
	/**
	 * 0-false 1-true
	 */
	private Integer isSelf;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 0-false ,1-true
	 */
	private Integer isDel;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	private String updateBy;

}
