package com.ruoyi.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.bullboard.vo.*;
import com.ruoyi.user.domain.dto.BalanceTotalDTO;
import com.ruoyi.user.domain.dto.UserManagerQueryDTO;
import com.ruoyi.user.domain.entity.FrontUserEntity;
import com.ruoyi.user.domain.vo.UserListVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-09 12:00:54
 */
@Repository("userManagerMapper")
public interface FrontUserMapper extends BaseMapper<FrontUserEntity> {

    List<UserListVO> getUserList(UserManagerQueryDTO queryDTO);

    List<BalanceTotalDTO> getBalanceTotal(@Param("uidList") List<Long> uidList);

    //获取用户数据看板头部新增信息
    UserBoardTopVo getUserBoardTop();

    //获取用户数据折线图
    List<UserDataLineVo> getUserDataLine(@Param("startDate") String startDate, @Param("endDate") String endDate);

    //用户右侧用户数据统计信息
    UserAddNewInfo getUserAddNewInfo(@Param("startDate") String startDate, @Param("endDate") String endDate);

    //获取用户占比信息
    UserAccountVo getUserAccount(@Param("type") Integer type);

    //获取用户使用数据
    List<UserDataInfoVo> getUserDataInfo(@Param("startDate") String startDate, @Param("endDate") String endDate);

    //获取用户年龄分布
    List<UserAgeDistributionVo> getUserAgeDistribution(@Param("type") Integer type);
}
