package com.ruoyi.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.bullboard.vo.UserAccountVo;
import com.ruoyi.bullboard.vo.UserBoardTopVo;
import com.ruoyi.bullboard.vo.UserDataLineVo;
import com.ruoyi.user.domain.dto.BalanceTotalDTO;
import com.ruoyi.user.domain.dto.UserManagerQueryDTO;
import com.ruoyi.user.domain.entity.FrontUserEntity;
import com.ruoyi.user.domain.vo.UserListVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-09 12:00:54
 */
@Repository("userManagerMapper")
public interface FrontUserMapper extends BaseMapper<FrontUserEntity> {

    List<UserListVO> getUserList(UserManagerQueryDTO queryDTO);

    List<BalanceTotalDTO> getBalanceTotal(@Param("uidList") List<Long> uidList);

    //获取用户数据看板头部新增信息
    UserBoardTopVo getUserBoardTop();

    //获取用户数据折线图
    List<UserDataLineVo> getUserDataLine(@Param("startDate") String startDate, @Param("endDate") String endDate);

    //获取用户占比信息
    UserAccountVo getUserAccount(@Param("type") Integer type);
}
