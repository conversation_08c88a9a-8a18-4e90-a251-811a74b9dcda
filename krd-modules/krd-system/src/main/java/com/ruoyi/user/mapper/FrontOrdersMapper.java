package com.ruoyi.user.mapper;

import com.ruoyi.user.domain.dto.UserOrderQueryDTO;
import com.ruoyi.user.domain.entity.FrontOrdersEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.user.domain.vo.UserOrderInfoVO;
import com.ruoyi.user.domain.vo.UserStatisticsVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 订单总表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-10 18:38:06
 */
@Repository("systemFrontOrdersMapper")
public interface FrontOrdersMapper extends BaseMapper<FrontOrdersEntity> {

    List<UserOrderInfoVO> getOrderList(UserOrderQueryDTO queryDTO);

    UserStatisticsVO getOrderStatistics(Long uid);
}
