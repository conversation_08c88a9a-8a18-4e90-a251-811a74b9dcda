package com.ruoyi.user.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.user.domain.dto.FreezeDTO;
import com.ruoyi.user.domain.dto.UserManagerQueryDTO;
import com.ruoyi.user.domain.dto.UserOrderQueryDTO;
import com.ruoyi.user.domain.dto.UserReportQueryDTO;
import com.ruoyi.user.domain.entity.FrontGoodsEntity;
import com.ruoyi.user.domain.vo.UniUserDetailsVO;
import com.ruoyi.user.domain.vo.UserBalanceVO;
import com.ruoyi.system.api.domain.vo.UserCouponVO;
import com.ruoyi.system.api.vo.UserGiftVO;
import com.ruoyi.user.domain.vo.UserListVO;
import com.ruoyi.user.domain.vo.UserOrderInfoVO;
import com.ruoyi.user.domain.vo.UserReportInfoVO;
import com.ruoyi.user.domain.vo.UserSourceVO;
import com.ruoyi.user.service.FrontUserManagerService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9 11:42
 */
@RestController
@RequestMapping("/frontUserManager")
public class FrontUserManagerController extends BaseController {

    @Resource
    private FrontUserManagerService frontUserManagerService;

    /**
     * 查询用户列表
     * @param queryDTO
     * @return
     */
    @RequestMapping("/getUserList")
    public TableDataInfo getUserList(@RequestBody UserManagerQueryDTO queryDTO){
        startPage();
        List<UserListVO> list = frontUserManagerService.getUserList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 导出用户列表
     * @param response
     * @param queryDTO
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserManagerQueryDTO queryDTO)
    {
        List<UserListVO> list = frontUserManagerService.getUserList(queryDTO);
        ExcelUtil<UserListVO> util = new ExcelUtil<>(UserListVO.class);
        util.exportExcel(response, list, "用户管理列表");
    }

    /**
     * 冻结用户
     * @param dto
     */
    @PostMapping("/freezeUser")
    public void freezeUser(@RequestBody FreezeDTO dto)
    {
        frontUserManagerService.freezeUser(dto);
    }

    /**
     * 冻结用户
     * @param dto
     */
    @PostMapping("/unfreezeUser")
    public void unfreezeUser(@RequestBody FreezeDTO dto)
    {
        frontUserManagerService.unfreezeUser(dto);
    }

    /**
     * 查询用户余额
     * @param uid
     * @return
     */
    @GetMapping("/getUserBalanceList/{uid}")
    public TableDataInfo getUserBalanceList(@PathVariable("uid") Long uid){
        startPage();
        List<UserBalanceVO> list = frontUserManagerService.getUserBalanceList(uid);
        return getDataTable(list);
    }

    /**
     * 查询用户优惠券
     * @param uid
     * @return
     */
    @GetMapping("/getUserCouponList/{uid}")
    public TableDataInfo getUserCouponList(@PathVariable("uid") Long uid){
        startPage();
        List<UserCouponVO> list = frontUserManagerService.getUserCouponList(uid);
        return getDataTable(list);
    }

    /**
     * 查询用户礼品卡
     * @param uid
     * @return
     */
    @GetMapping("/getUserGiftList/{uid}")
    public TableDataInfo getUserGiftList(@PathVariable("uid") Long uid){
        startPage();
        List<UserGiftVO> list = frontUserManagerService.getUserGiftList(uid);
        return getDataTable(list);
    }

    /**
     * 查询用户积分
     * @param uid
     * @return
     */
    @GetMapping("/getUserSourceList/{uid}")
    public TableDataInfo getUserSourceList(@PathVariable("uid") Long uid){
        startPage();
        List<UserSourceVO> list = frontUserManagerService.getUserSourceList(uid);
        return getDataTable(list);
    }

    /**
     * 获取用户详情
     * @param uid
     * @return
     */
    @GetMapping("/getUniUserDetails/{uid}")
    public R<UniUserDetailsVO> getUniUserDetails(@PathVariable("uid") Long uid){
        UniUserDetailsVO vo = frontUserManagerService.getUniUserDetails(uid);
        return R.ok(vo);
    }

    /**
     * 获取用户订单列表
     * @param queryDTO
     * @return
     */
    @PostMapping("/getOrderListPage")
    public TableDataInfo getOrderListPage(@RequestBody UserOrderQueryDTO queryDTO){
        startPage();
        List<UserOrderInfoVO> list = frontUserManagerService.getOrderList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 查询检测报告列表
     * @param queryDTO
     * @return
     */
    @PostMapping("/getCheckListPage")
    public TableDataInfo getCheckListPage(@RequestBody UserReportQueryDTO queryDTO){
        startPage();
        List<UserReportInfoVO> list = frontUserManagerService.getCheckListPage(queryDTO);
        return getDataTable(list);
    }

    /**
     * 获取用户收藏商品列表
     * @param uid
     * @return
     */
    @GetMapping("/getUserSpcoList/{uid}")
    public R<List<FrontGoodsEntity>> getUserSpcoList(@PathVariable("uid") Long uid){
        List<FrontGoodsEntity> list = frontUserManagerService.getUserSpcoList(uid);
        return R.ok(list);
    }
}
