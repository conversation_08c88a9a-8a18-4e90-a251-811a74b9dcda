package com.ruoyi.user.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/11 00:00
 */
@Data
public class UserStatisticsVO {
    //消费金额
    private BigDecimal xfBalance;
    //订单数量
    private Integer orderCount;
    //售后退款数量
    private Integer ackAfterCount;
    //礼品卡数量
    private Integer giftCount;
    private Integer giftBalance;
    //优惠券数量
    private Integer couponCount;
    private Integer couponBalance;
    //积分余额
    private Integer sourceBalance;
    //收藏商品数量
    private Integer spcoCount;
    //评价数量
    private Integer evaCount;
    //检测数量
    private Integer chckCount;
    //邀请好友数量
    private Integer friendCount;
}
