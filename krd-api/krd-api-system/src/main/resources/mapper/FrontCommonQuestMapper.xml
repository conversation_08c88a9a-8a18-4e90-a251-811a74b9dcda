<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontCommonQuestMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontCommonQuest" id="FrontCommonQuestResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="answer"    column="answer"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="sort"    column="sort"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectFrontCommonQuestVo">
        select id, title, answer, status, create_time, update_time, create_by, update_by, sort, is_del from front_common_quest
    </sql>

    <select id="selectFrontCommonQuestList" parameterType="com.ruoyi.system.api.domain.FrontCommonQuest" resultMap="FrontCommonQuestResult">
        <include refid="selectFrontCommonQuestVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="answer != null  and answer != ''"> and answer = #{answer}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>

    <select id="selectFrontCommonQuestById" parameterType="Long" resultMap="FrontCommonQuestResult">
        <include refid="selectFrontCommonQuestVo"/>
        where id = #{id}
    </select>
    <select id="selectUniFrontCommonQuestList" resultType="com.ruoyi.system.api.domain.FrontCommonQuest">
        <include refid="selectFrontCommonQuestVo"/>
        where status = 1
    </select>

    <insert id="insertFrontCommonQuest" parameterType="com.ruoyi.system.api.domain.FrontCommonQuest">
        insert into front_common_quest
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="answer != null">answer,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="sort != null">sort,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="answer != null">#{answer},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="sort != null">#{sort},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateFrontCommonQuest" parameterType="com.ruoyi.system.api.domain.FrontCommonQuest">
        update front_common_quest
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="answer != null">answer = #{answer},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontCommonQuestById" parameterType="Long">
        delete from front_common_quest where id = #{id}
    </delete>

    <delete id="deleteFrontCommonQuestByIds" parameterType="String">
        delete from front_common_quest where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
