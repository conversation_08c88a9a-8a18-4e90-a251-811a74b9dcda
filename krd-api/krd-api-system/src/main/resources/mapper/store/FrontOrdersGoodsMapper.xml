<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontOrdersGoodsMapper">
    
    <resultMap type="com.ruoyi.system.api.domain.FrontOrdersGoods" id="FrontOrdersGoodsResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="count"    column="count"    />
        <result property="price"    column="price"    />
        <result property="discount"    column="discount"    />
        <result property="meetPrice"    column="meet_price"    />
        <result property="payPrice"    column="pay_price"    />
        <result property="handleRemark"    column="handle_remark"    />
        <result property="afterReason"    column="after_reason"    />
        <result property="afterStatus"    column="after_status"    />
        <result property="afterDesc"    column="after_desc"    />
        <result property="afterPrice"    column="after_price"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="ackAfterPrice"    column="ack_after_price"    />
        <result property="afterAddress"    column="after_address"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFrontOrdersGoodsVo">
        select id, order_id, goods_id, count, price, discount, meet_price, pay_price, handle_remark, after_reason, after_status, after_desc, after_price, apply_time, handle_time, ack_after_price, after_address, create_by, update_by, create_time, update_time from front_orders_goods
    </sql>

    <select id="selectFrontOrdersGoodsList" parameterType="com.ruoyi.system.api.domain.FrontOrdersGoods" resultMap="FrontOrdersGoodsResult">
        <include refid="selectFrontOrdersGoodsVo"/>
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="goodsId != null "> and goods_id = #{goodsId}</if>
            <if test="count != null "> and count = #{count}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="discount != null "> and discount = #{discount}</if>
            <if test="meetPrice != null "> and meet_price = #{meetPrice}</if>
            <if test="payPrice != null "> and pay_price = #{payPrice}</if>
            <if test="handleRemark != null  and handleRemark != ''"> and handle_remark = #{handleRemark}</if>
            <if test="afterReason != null  and afterReason != ''"> and after_reason = #{afterReason}</if>
            <if test="afterStatus != null  and afterStatus != ''"> and after_status = #{afterStatus}</if>
            <if test="afterDesc != null  and afterDesc != ''"> and after_desc = #{afterDesc}</if>
            <if test="afterPrice != null "> and after_price = #{afterPrice}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="handleTime != null "> and handle_time = #{handleTime}</if>
            <if test="ackAfterPrice != null "> and ack_after_price = #{ackAfterPrice}</if>
            <if test="afterAddress != null  and afterAddress != ''"> and after_address = #{afterAddress}</if>
        </where>
    </select>
    
    <select id="selectFrontOrdersGoodsById" parameterType="Long" resultMap="FrontOrdersGoodsResult">
        <include refid="selectFrontOrdersGoodsVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontOrdersGoods" parameterType="com.ruoyi.system.api.domain.FrontOrdersGoods">
        insert into front_orders_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="count != null">count,</if>
            <if test="price != null">price,</if>
            <if test="discount != null">discount,</if>
            <if test="meetPrice != null">meet_price,</if>
            <if test="payPrice != null">pay_price,</if>
            <if test="handleRemark != null">handle_remark,</if>
            <if test="afterReason != null">after_reason,</if>
            <if test="afterStatus != null">after_status,</if>
            <if test="afterDesc != null">after_desc,</if>
            <if test="afterPrice != null">after_price,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="ackAfterPrice != null">ack_after_price,</if>
            <if test="afterAddress != null">after_address,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="goodsId != null">#{goodsId},</if>
            <if test="count != null">#{count},</if>
            <if test="price != null">#{price},</if>
            <if test="discount != null">#{discount},</if>
            <if test="meetPrice != null">#{meetPrice},</if>
            <if test="payPrice != null">#{payPrice},</if>
            <if test="handleRemark != null">#{handleRemark},</if>
            <if test="afterReason != null">#{afterReason},</if>
            <if test="afterStatus != null">#{afterStatus},</if>
            <if test="afterDesc != null">#{afterDesc},</if>
            <if test="afterPrice != null">#{afterPrice},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="ackAfterPrice != null">#{ackAfterPrice},</if>
            <if test="afterAddress != null">#{afterAddress},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateFrontOrdersGoods" parameterType="com.ruoyi.system.api.domain.FrontOrdersGoods">
        update front_orders_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="goodsId != null">goods_id = #{goodsId},</if>
            <if test="count != null">count = #{count},</if>
            <if test="price != null">price = #{price},</if>
            <if test="discount != null">discount = #{discount},</if>
            <if test="meetPrice != null">meet_price = #{meetPrice},</if>
            <if test="payPrice != null">pay_price = #{payPrice},</if>
            <if test="handleRemark != null">handle_remark = #{handleRemark},</if>
            <if test="afterReason != null">after_reason = #{afterReason},</if>
            <if test="afterStatus != null">after_status = #{afterStatus},</if>
            <if test="afterDesc != null">after_desc = #{afterDesc},</if>
            <if test="afterPrice != null">after_price = #{afterPrice},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="ackAfterPrice != null">ack_after_price = #{ackAfterPrice},</if>
            <if test="afterAddress != null">after_address = #{afterAddress},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontOrdersGoodsById" parameterType="Long">
        delete from front_orders_goods where id = #{id}
    </delete>

    <delete id="deleteFrontOrdersGoodsByIds" parameterType="String">
        delete from front_orders_goods where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>