<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.api.mapper.FrontGiftInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.system.api.domain.FrontGiftInfo" id="frontGiftInfoMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="useTime" column="use_time"/>
        <result property="balance" column="balance"/>
        <result property="orderNumber" column="order_number"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="giftId" column="gift_id"/>
        <result property="type" column="type"/>
        <result property="sort" column="sort"/>
    </resultMap>
    <select id="getUserGiftList" resultType="com.ruoyi.system.api.vo.UserGiftVO">
        SELECT
            ui.id AS giftId,
            ui.type AS giftType,
            ui.balance AS giftBalance,
            GREATEST(gi.balance - COALESCE(used.total_used, 0), 0) AS giftBalanceAfter,
            ui.create_time AS giftUseTime,
            ui.order_no AS orderNumber
        FROM
            front_gift_user_info ui
                LEFT JOIN front_gift_info gi ON ui.gift_info_id = gi.id
                LEFT JOIN (
                SELECT
                    gift_info_id,
                    SUM(balance) AS total_used
                FROM
                    front_gift_user_info
                WHERE
                    type = 0
                GROUP BY
                    gift_info_id
            ) used ON ui.gift_info_id = used.gift_info_id
        WHERE gi.user_id = #{uid}
    </select>
    <select id="getUserGiftCountByUids" resultType="com.ruoyi.system.api.domain.dto.UserCountDTO">
        SELECT user_id AS uid,COUNT(1) AS count
        FROM front_gift_info
        WHERE user_id IN <foreach item="item" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY user_id
    </select>

    <!-- 用户礼品卡列表 -->
    <select id="listGiftCardList" resultType="com.ruoyi.system.api.domain.vo.UniUserVo$UserGiftCardList">
        SELECT
            gi.id,
            g.`name`,
            g.balance AS faceValue,
            gi.balance,
            gi.use_time as expireTime
        FROM
            front_gift_info gi
                LEFT JOIN front_gift g ON gi.gift_id = g.id
        where
            gi.user_id = #{uid}
        <if test="type == 1">
            and gi.type in (0,3)
        </if>
        <if test="type == 2">
            and gi.type in (1,2)
        </if>
    </select>


</mapper>