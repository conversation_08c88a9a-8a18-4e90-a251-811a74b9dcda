<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontGoodsSpecMapper">
    
    <resultMap type="com.ruoyi.system.api.domain.FrontGoodsSpec" id="FrontGoodsSpecResult">
        <result property="id"    column="id"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="title"    column="title"    />
        <result property="price"    column="price"    />
        <result property="vipPrice"    column="vip_price"    />
        <result property="amount"    column="amount"    />
        <result property="pic"    column="pic"    />
        <result property="isDel"    column="is_del"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectFrontGoodsSpecVo">
        select id, goods_id, title, price, vip_price, amount, pic, is_del, create_time, update_time, create_by, update_by from front_goods_spec
    </sql>

    <select id="selectFrontGoodsSpecList" parameterType="com.ruoyi.system.api.domain.FrontGoodsSpec" resultMap="FrontGoodsSpecResult">
        <include refid="selectFrontGoodsSpecVo"/>
        <where>  
            <if test="goodsId != null "> and goods_id = #{goodsId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="vipPrice != null "> and vip_price = #{vipPrice}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="pic != null  and pic != ''"> and pic = #{pic}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectFrontGoodsSpecById" parameterType="Long" resultMap="FrontGoodsSpecResult">
        <include refid="selectFrontGoodsSpecVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontGoodsSpec" parameterType="com.ruoyi.system.api.domain.FrontGoodsSpec">
        insert into front_goods_spec
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="title != null">title,</if>
            <if test="price != null">price,</if>
            <if test="vipPrice != null">vip_price,</if>
            <if test="amount != null">amount,</if>
            <if test="pic != null">pic,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="goodsId != null">#{goodsId},</if>
            <if test="title != null">#{title},</if>
            <if test="price != null">#{price},</if>
            <if test="vipPrice != null">#{vipPrice},</if>
            <if test="amount != null">#{amount},</if>
            <if test="pic != null">#{pic},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateFrontGoodsSpec" parameterType="com.ruoyi.system.api.domain.FrontGoodsSpec">
        update front_goods_spec
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodsId != null">goods_id = #{goodsId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="price != null">price = #{price},</if>
            <if test="vipPrice != null">vip_price = #{vipPrice},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="pic != null">pic = #{pic},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontGoodsSpecById" parameterType="Long">
        delete from front_goods_spec where id = #{id}
    </delete>

    <delete id="deleteFrontGoodsSpecByIds" parameterType="String">
        delete from front_goods_spec where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>