<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.api.mapper.FrontCouponInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.system.api.domain.FrontCouponInfo" id="frontCouponInfoMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="useTime" column="use_time"/>
        <result property="orderNumber" column="order_number"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="couponId" column="coupon_id"/>
        <result property="balance" column="balance"/>
        <result property="sort" column="sort"/>
    </resultMap>
    <select id="getUserCouponList" resultType="com.ruoyi.system.api.domain.vo.UserCouponVO">
        SELECT
            c.coupon_id AS couponId,
            c.type AS couponType,
            COUNT(1) AS couponCount,
            COUNT(1) AS couponCountAfter,
            SUM(c.balance) AS couponBalance,
            SUM(c.balance) AS couponBalanceAfter,
            c.create_time AS couponUseTime,
            c.order_number AS orderNumber,
            c.`status` AS couponStatus
        FROM
            front_coupon_info c
        WHERE
            c.user_id = #{uid}
        GROUP BY c.coupon_id
    </select>
    <select id="getUserCouponCountByUids" resultType="com.ruoyi.system.api.domain.dto.UserCountDTO">
        SELECT user_id AS uid,COUNT(1) AS count
        FROM front_coupon_info
        WHERE user_id IN <foreach item="item" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY user_id
    </select>


</mapper>