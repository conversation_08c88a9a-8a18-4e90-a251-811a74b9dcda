<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontAdvertMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontAdvert" id="FrontAdvertResult">
        <result property="id"    column="id"    />
        <result property="adTitle"    column="ad_title"    />
        <result property="adType"    column="ad_type"    />
        <result property="adUrl"    column="ad_url"    />
        <result property="adAddress"    column="ad_address"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="material"    column="material"    />
    </resultMap>

    <sql id="selectFrontAdvertVo">
        select id, ad_title, ad_type, ad_url, material,ad_address, status, create_time, update_time, is_del, create_by, update_by from front_advert
    </sql>

    <select id="selectFrontAdvertList" parameterType="com.ruoyi.system.api.domain.FrontAdvert" resultMap="FrontAdvertResult">
        select fa.*, fat.name as ad_type_name from front_advert fa left join front_advert_type fat on fa.ad_type = fat.id
        <where>
            <if test="adTitle != null  and adTitle != ''"> and ad_title like concat('%',#{adTitle},'%')</if>
            <if test="adType != null "> and ad_type = #{adType}</if>
            <if test="adUrl != null  and adUrl != ''"> and ad_url = #{adUrl}</if>
            <if test="adAddress != null "> and ad_address = #{adAddress}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectFrontAdvertById" parameterType="Long" resultMap="FrontAdvertResult">
        <include refid="selectFrontAdvertVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontAdvert" parameterType="com.ruoyi.system.api.domain.FrontAdvert">
        insert into front_advert
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="adTitle != null">ad_title,</if>
            <if test="adType != null">ad_type,</if>
            <if test="adUrl != null">ad_url,</if>
            <if test="adAddress != null">ad_address,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="material != null">material,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="adTitle != null">#{adTitle},</if>
            <if test="adType != null">#{adType},</if>
            <if test="adUrl != null">#{adUrl},</if>
            <if test="adAddress != null">#{adAddress},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="material != null">#{material},</if>
        </trim>
    </insert>

    <update id="updateFrontAdvert" parameterType="com.ruoyi.system.api.domain.FrontAdvert">
        update front_advert
        <trim prefix="SET" suffixOverrides=",">
            <if test="adTitle != null">ad_title = #{adTitle},</if>
            <if test="adType != null">ad_type = #{adType},</if>
            <if test="adUrl != null">ad_url = #{adUrl},</if>
            <if test="adAddress != null">ad_address = #{adAddress},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="material != null">material = #{material},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontAdvertById" parameterType="Long">
        delete from front_advert where id = #{id}
    </delete>

    <delete id="deleteFrontAdvertByIds" parameterType="String">
        delete from front_advert where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
