<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontChoiceMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontChoice" id="FrontChoiceResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="info"    column="info"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="url"    column="url"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="intro"    column="intro"    />
        <result property="cover"    column="cover"    />
    </resultMap>

    <sql id="selectFrontChoiceVo">
        select id, title, info, create_time,cover, update_time, is_del, url, sort, create_by, update_by, intro from front_choice
    </sql>

    <select id="selectFrontChoiceList" parameterType="com.ruoyi.system.api.domain.FrontChoice" resultMap="FrontChoiceResult">
        <include refid="selectFrontChoiceVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%',#{title},'%')</if>
            <if test="info != null  and info != ''"> and info = #{info}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="intro != null  and intro != ''"> and intro = #{intro}</if>
        </where>
    </select>

    <select id="selectFrontChoiceById" parameterType="Long" resultMap="FrontChoiceResult">
        <include refid="selectFrontChoiceVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontChoice" parameterType="com.ruoyi.system.api.domain.FrontChoice">
        insert into front_choice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="info != null">info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="url != null">url,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="intro != null">intro,</if>
            <if test="cover != null">cover,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="info != null">#{info},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="url != null">#{url},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="intro != null">#{intro},</if>
            <if test="cover != null">#{cover},</if>
        </trim>
    </insert>

    <update id="updateFrontChoice" parameterType="com.ruoyi.system.api.domain.FrontChoice">
        update front_choice
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="info != null">info = #{info},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="url != null">url = #{url},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="intro != null">intro = #{intro},</if>
            <if test="cover != null">cover = #{cover},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontChoiceById" parameterType="Long">
        delete from front_choice where id = #{id}
    </delete>

    <delete id="deleteFrontChoiceByIds" parameterType="String">
        delete from front_choice where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
