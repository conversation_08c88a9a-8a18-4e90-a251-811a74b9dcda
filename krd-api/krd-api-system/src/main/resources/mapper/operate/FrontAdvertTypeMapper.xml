<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontAdvertTypeMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontAdvertType" id="FrontAdvertTypeResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectFrontAdvertTypeVo">
        select id, name, is_del from front_advert_type
    </sql>

    <select id="selectFrontAdvertTypeList" parameterType="com.ruoyi.system.api.domain.FrontAdvertType" resultMap="FrontAdvertTypeResult">
        <include refid="selectFrontAdvertTypeVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>

    <select id="selectFrontAdvertTypeById" parameterType="Long" resultMap="FrontAdvertTypeResult">
        <include refid="selectFrontAdvertTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontAdvertType" parameterType="com.ruoyi.system.api.domain.FrontAdvertType" useGeneratedKeys="true" keyProperty="id">
        insert into front_advert_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateFrontAdvertType" parameterType="com.ruoyi.system.api.domain.FrontAdvertType">
        update front_advert_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontAdvertTypeById" parameterType="Long">
        delete from front_advert_type where id = #{id}
    </delete>

    <delete id="deleteFrontAdvertTypeByIds" parameterType="String">
        delete from front_advert_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
