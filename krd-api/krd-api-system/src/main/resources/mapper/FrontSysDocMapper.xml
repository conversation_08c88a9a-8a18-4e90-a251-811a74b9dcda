<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontSysDocMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontSysDoc" id="FrontSysDocResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="docType"    column="doc_type"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectFrontSysDocVo">
        select id, title, doc_type, content, create_time, update_time, create_by, update_by, is_del from front_sys_doc
    </sql>

    <select id="selectFrontSysDocList" parameterType="com.ruoyi.system.api.domain.FrontSysDoc" resultMap="FrontSysDocResult">
        <include refid="selectFrontSysDocVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>

    <select id="selectFrontSysDocById" parameterType="Long" resultMap="FrontSysDocResult">
        <include refid="selectFrontSysDocVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontSysDoc" parameterType="com.ruoyi.system.api.domain.FrontSysDoc">
        insert into front_sys_doc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="docType != null">doc_type,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="docType != null">#{docType},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateFrontSysDoc" parameterType="com.ruoyi.system.api.domain.FrontSysDoc">
        update front_sys_doc
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="docType != null">doc_type = #{docType},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontSysDocById" parameterType="Long">
        delete from front_sys_doc where id = #{id}
    </delete>

    <delete id="deleteFrontSysDocByIds" parameterType="String">
        delete from front_sys_doc where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
