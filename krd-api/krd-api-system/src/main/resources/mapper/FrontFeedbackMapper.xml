<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontFeedbackMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontFeedback" id="FrontFeedbackResult">
        <result property="id"    column="id"    />
        <result property="content"    column="content"    />
        <result property="pic"    column="pic"    />
        <result property="createTime"    column="create_time"    />
        <result property="userId"    column="user_id"    />
        <result property="mobile"    column="mobile"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectFrontFeedbackVo">
        select id, content, pic, create_time, user_id, mobile, is_del from front_feedback
    </sql>

    <select id="selectFrontFeedbackList" parameterType="com.ruoyi.system.api.domain.FrontFeedback" resultMap="FrontFeedbackResult">
        select fe.*,fu.user_name as userName  from front_feedback fe LEFT JOIN front_user fu
        ON fe.user_id = fu.id
        <where>
            <if test="startTime != null  and endTime != ''"> and create_time between #{startTime} and #{endTime}</if>
        </where>
    </select>

    <select id="selectFrontFeedbackById" parameterType="Long" resultMap="FrontFeedbackResult">
        <include refid="selectFrontFeedbackVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontFeedback" parameterType="com.ruoyi.system.api.domain.FrontFeedback">
        insert into front_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="content != null">content,</if>
            <if test="pic != null">pic,</if>
            <if test="createTime != null">create_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="mobile != null">mobile,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="content != null">#{content},</if>
            <if test="pic != null">#{pic},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateFrontFeedback" parameterType="com.ruoyi.system.api.domain.FrontFeedback">
        update front_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null">content = #{content},</if>
            <if test="pic != null">pic = #{pic},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontFeedbackById" parameterType="Long">
        delete from front_feedback where id = #{id}
    </delete>

    <delete id="deleteFrontFeedbackByIds" parameterType="String">
        delete from front_feedback where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
