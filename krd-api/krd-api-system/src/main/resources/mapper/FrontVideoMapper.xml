<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontVideoMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontVideo" id="FrontVideoResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="url"    column="url"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="size"    column="size"    />
        <result property="cover"    column="cover"    />
        <result property="step"    column="step"    />
    </resultMap>

    <sql id="selectFrontVideoVo">
        select * from front_video
    </sql>

    <select id="selectFrontVideoList" parameterType="com.ruoyi.system.api.domain.FrontVideo" resultMap="FrontVideoResult">
        <include refid="selectFrontVideoVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectFrontVideoById" parameterType="Long" resultMap="FrontVideoResult">
        <include refid="selectFrontVideoVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontVideo" parameterType="com.ruoyi.system.api.domain.FrontVideo">
        insert into front_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="url != null">url,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="sort != null">sort,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="size != null">size,</if>
            <if test="cover != null">cover,</if>
            <if test="step != null">step,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="url != null">#{url},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="size != null">#{size},</if>
            <if test="cover != null">#{cover},</if>
            <if test="step != null">#{step},</if>
        </trim>
    </insert>

    <update id="updateFrontVideo" parameterType="com.ruoyi.system.api.domain.FrontVideo">
        update front_video
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="url != null">url = #{url},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="size != null">size = #{size},</if>
            <if test="cover != null">cover = #{cover},</if>
            <if test="step != null">step = #{step},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontVideoById" parameterType="Long">
        delete from front_video where id = #{id}
    </delete>

    <delete id="deleteFrontVideoByIds" parameterType="String">
        delete from front_video where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
