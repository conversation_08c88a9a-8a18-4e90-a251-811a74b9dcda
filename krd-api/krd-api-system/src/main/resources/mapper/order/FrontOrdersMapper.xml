<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontOrdersMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontOrders" id="FrontOrdersResult">
        <result property="id"    column="id"    />
<!--        <result property="type"    column="type"    />-->
        <result property="orderNumber"    column="order_number"    />
        <result property="userId"    column="user_id"    />
        <result property="totalPrice"    column="total_price"    />
        <result property="deductionType"    column="deduction_type"    />
        <result property="deductionPrice"    column="deduction_price"    />
        <result property="payPrice"    column="pay_price"    />
        <result property="pushGoodsNo"    column="push_goods_no"    />
        <result property="createTime"    column="create_time"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="isAfter"    column="is_after"    />
        <result property="status"    column="status"    />
        <result property="userRemark"    column="user_remark"    />
        <result property="platRemark"    column="plat_remark"    />
        <result property="closeOrder"    column="close_order"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="useBalance"    column="use_balance"    />
        <result property="usePoint"    column="use_point"    />
    </resultMap>

    <sql id="selectFrontOrdersVo">
        select id, type, order_number, user_id, total_price, deduction_type, use_balance, use_point,deduction_price, pay_price, push_goods_no, create_time, finish_time, is_after, status, user_remark, plat_remark, close_order, update_time, create_by, update_by from front_orders
    </sql>

    <select id="selectFrontOrdersList" resultType="com.ruoyi.system.api.domain.FrontOrders">
        select
            o.id,
--             o.type,
            o.order_number,
            o.user_id,
            o.total_price,
            o.deduction_type,
            o.deduction_price,
            o.pay_price,
            o.push_goods_no,
            o.create_time,
            o.finish_time,
            o.is_after,
            o.status,
            o.user_remark,
            o.plat_remark,
            o.close_order,
            o.update_time,
            o.create_by,
            o.update_by,
            g.name as goodsName
        from
            front_orders as o
        left join front_orders_goods s on o.id = s.order_id
        left join front_goods g on s.goods_id = g.id
        <where>
<!--            <if test="query.type != null "> and type = #{query.type}</if>-->
            <if test="query.keyword != null  and query.keyword != ''">
            and (o.user_id = #{query.keyword}
            or o.order_number like concat('%',#{query.keyword},'%')
            or g.name like concat('%',#{query.keyword},'%'))
            </if>
            <if test="query.status != null "> and status = #{query.status}</if>
        </where>
        order by o.create_time desc
    </select>

    <select id="selectFrontOrdersById" parameterType="Long" resultMap="FrontOrdersResult">
        <include refid="selectFrontOrdersVo"/>
        where o.id = #{id}
    </select>
    <select id="selectUniOrdersList" resultType="com.ruoyi.system.api.domain.FrontOrders">
        select
        o.id,
        o.order_number,
        o.user_id,
        o.total_price,
        o.deduction_type,
        o.deduction_price,
        o.pay_price,
        o.push_goods_no,
        o.create_time,
        o.finish_time,
        o.is_after,
        o.status,
        o.user_remark,
        o.plat_remark,
        o.close_order,
        o.update_time,
        o.create_by,
        o.update_by,
        g.name as goodsName
        from
        front_orders as o
        left join front_orders_goods s on o.id = s.order_id
        left join front_goods g on s.goods_id = g.id
        <where>
            <if test="query.keyword != null  and query.keyword != ''">
                and ( o.order_number like concat('%',#{query.keyword},'%')
                or g.name like concat('%',#{query.keyword},'%'))
            </if>
            <if test="query.status != null and query.status != '' and query.status != '-1'"> and status = #{query.status}</if>
            <if test="query.uid != null and query.uid != ''">
                and user_id = #{query.uid}
            </if>
        </where>
    </select>

    <insert id="insertFrontOrders" parameterType="com.ruoyi.system.api.domain.FrontOrders" useGeneratedKeys="true" keyProperty="id">
        insert into front_orders
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="orderNumber != null">order_number,</if>
            <if test="userId != null">user_id,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="deductionType != null">deduction_type,</if>
            <if test="deductionPrice != null">deduction_price,</if>
            <if test="payPrice != null">pay_price,</if>
            <if test="pushGoodsNo != null">push_goods_no,</if>
            <if test="createTime != null">create_time,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="isAfter != null">is_after,</if>
            <if test="status != null">status,</if>
            <if test="userRemark != null">user_remark,</if>
            <if test="platRemark != null">plat_remark,</if>
            <if test="closeOrder != null">close_order,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="useBalance != null">use_balance,</if>
            <if test="usePoint != null">use_point,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="userId != null">#{userId},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="deductionType != null">#{deductionType},</if>
            <if test="deductionPrice != null">#{deductionPrice},</if>
            <if test="payPrice != null">#{payPrice},</if>
            <if test="pushGoodsNo != null">#{pushGoodsNo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="isAfter != null">#{isAfter},</if>
            <if test="status != null">#{status},</if>
            <if test="userRemark != null">#{userRemark},</if>
            <if test="platRemark != null">#{platRemark},</if>
            <if test="closeOrder != null">#{closeOrder},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="useBalance != null">#{useBalance},</if>
            <if test="usePoints != null">#{usePoints},</if>
         </trim>
    </insert>

    <update id="updateFrontOrders" parameterType="com.ruoyi.system.api.domain.FrontOrders">
        update front_orders
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNumber != null">order_number = #{orderNumber},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="deductionType != null">deduction_type = #{deductionType},</if>
            <if test="deductionPrice != null">deduction_price = #{deductionPrice},</if>
            <if test="payPrice != null">pay_price = #{payPrice},</if>
            <if test="pushGoodsNo != null">push_goods_no = #{pushGoodsNo},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="isAfter != null">is_after = #{isAfter},</if>
            <if test="status != null">status = #{status},</if>
            <if test="userRemark != null">user_remark = #{userRemark},</if>
            <if test="platRemark != null">plat_remark = #{platRemark},</if>
            <if test="closeOrder != null">close_order = #{closeOrder},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="useBalance != null">use_balance = #{useBalance},</if>
            <if test="usePoint != null">use_point = #{usePoint},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontOrdersById" parameterType="Long">
        delete from front_orders where id = #{id}
    </delete>

    <delete id="deleteFrontOrdersByIds" parameterType="String">
        delete from front_orders where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
