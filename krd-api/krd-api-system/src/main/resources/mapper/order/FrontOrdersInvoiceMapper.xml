<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontOrdersInvoiceMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontOrdersInvoice" id="FrontOrdersInvoiceResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="status"    column="status"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="name"    column="name"    />
        <result property="hearder"    column="hearder"    />
        <result property="number"    column="number"    />
        <result property="address"    column="address"    />
        <result property="phone"    column="phone"    />
        <result property="bank"    column="bank"    />
        <result property="account"    column="account"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="url"    column="url"    />
    </resultMap>

    <sql id="selectFrontOrdersInvoiceVo">
        select id, order_id, status, apply_time, name, hearder, number, address, phone, bank, account, content, create_time, update_time, create_by, update_by,url from front_orders_invoice
    </sql>

    <select id="selectFrontOrdersInvoiceList" parameterType="com.ruoyi.system.api.domain.FrontOrdersInvoice" resultMap="FrontOrdersInvoiceResult">
        <include refid="selectFrontOrdersInvoiceVo"/>
        <where>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="hearder != null  and hearder != ''"> and hearder = #{hearder}</if>
            <if test="number != null  and number != ''"> and number = #{number}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="bank != null  and bank != ''"> and bank = #{bank}</if>
            <if test="account != null  and account != ''"> and account = #{account}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>

    <select id="selectFrontOrdersInvoiceById" parameterType="Long" resultMap="FrontOrdersInvoiceResult">
        <include refid="selectFrontOrdersInvoiceVo"/>
        where order_id = #{orderId}
    </select>

    <insert id="insertFrontOrdersInvoice" parameterType="com.ruoyi.system.api.domain.FrontOrdersInvoice">
        insert into front_orders_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="status != null">status,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="name != null">name,</if>
            <if test="hearder != null">hearder,</if>
            <if test="number != null">number,</if>
            <if test="address != null">address,</if>
            <if test="phone != null">phone,</if>
            <if test="bank != null">bank,</if>
            <if test="account != null">account,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="status != null">#{status},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="name != null">#{name},</if>
            <if test="hearder != null">#{hearder},</if>
            <if test="number != null">#{number},</if>
            <if test="address != null">#{address},</if>
            <if test="phone != null">#{phone},</if>
            <if test="bank != null">#{bank},</if>
            <if test="account != null">#{account},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateFrontOrdersInvoice" parameterType="com.ruoyi.system.api.domain.FrontOrdersInvoice">
        update front_orders_invoice
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="name != null">name = #{name},</if>
            <if test="hearder != null">hearder = #{hearder},</if>
            <if test="number != null">number = #{number},</if>
            <if test="address != null">address = #{address},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="bank != null">bank = #{bank},</if>
            <if test="account != null">account = #{account},</if>
            <if test="content != null">content = #{content},</if>
            <if test="url != null">url = #{url},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontOrdersInvoiceById" parameterType="Long">
        delete from front_orders_invoice where id = #{id}
    </delete>

    <delete id="deleteFrontOrdersInvoiceByIds" parameterType="String">
        delete from front_orders_invoice where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>