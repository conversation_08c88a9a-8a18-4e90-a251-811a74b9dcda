<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontHealthTestMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontHealthTest" id="FrontHealthTestResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectFrontHealthTestVo">
        select id, type, content, create_time, update_time, is_del, create_by, update_by from front_health_test
    </sql>

    <select id="selectFrontHealthTestList" parameterType="com.ruoyi.system.api.domain.FrontHealthTest" resultMap="FrontHealthTestResult">
        <include refid="selectFrontHealthTestVo"/>
        <where>
            <if test="type != null "> and type = #{type}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>

    <select id="selectFrontHealthTestById" parameterType="Long" resultMap="FrontHealthTestResult">
        <include refid="selectFrontHealthTestVo"/>
        where type = #{id}
    </select>

    <insert id="insertFrontHealthTest" parameterType="com.ruoyi.system.api.domain.FrontHealthTest">
        insert into front_health_test
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null">type,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateFrontHealthTest" parameterType="com.ruoyi.system.api.domain.FrontHealthTest">
        update front_health_test
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontHealthTestById" parameterType="Long">
        delete from front_health_test where id = #{id}
    </delete>

    <delete id="deleteFrontHealthTestByIds" parameterType="String">
        delete from front_health_test where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
