<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontSourceMapper">

    <!--  查询用户积分增加情况  -->
    <select id="selectFrontUserPoint" resultType="integer">
        SELECT
            SUM(s.POINT)
        FROM
            front_source s
        WHERE
            s.type = 0
          AND s.user_id = #{userId}
    </select>

</mapper>