<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontHealthAnswerMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontHealthAnswer" id="FrontHealthAnswerResult">
        <result property="id"    column="id"    />
        <result property="uid"    column="uid"    />
        <result property="content"    column="content"    />
        <result property="type"    column="type"    />
        <result property="questId"    column="quest_id"    />
    </resultMap>

    <sql id="selectFrontHealthAnswerVo">
        select id, uid, content, type, quest_id from front_health_answer
    </sql>

    <select id="selectFrontHealthAnswerList" parameterType="com.ruoyi.system.api.domain.FrontHealthAnswer" resultMap="FrontHealthAnswerResult">
        <include refid="selectFrontHealthAnswerVo"/>
        <where>
            <if test="uid != null "> and uid = #{uid}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="questId != null "> and quest_id = #{questId}</if>
        </where>
    </select>

    <select id="selectFrontHealthAnswerById" parameterType="Long" resultMap="FrontHealthAnswerResult">
        <include refid="selectFrontHealthAnswerVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontHealthAnswer" parameterType="com.ruoyi.system.api.domain.FrontHealthAnswer" useGeneratedKeys="true" keyProperty="id">
        insert into front_health_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uid != null">uid,</if>
            <if test="content != null">content,</if>
            <if test="type != null">type,</if>
            <if test="questId != null">quest_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uid != null">#{uid},</if>
            <if test="content != null">#{content},</if>
            <if test="type != null">#{type},</if>
            <if test="questId != null">#{questId},</if>
        </trim>
    </insert>

    <update id="updateFrontHealthAnswer" parameterType="com.ruoyi.system.api.domain.FrontHealthAnswer">
        update front_health_answer
        <trim prefix="SET" suffixOverrides=",">
            <if test="uid != null">uid = #{uid},</if>
            <if test="content != null">content = #{content},</if>
            <if test="type != null">type = #{type},</if>
            <if test="questId != null">quest_id = #{questId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontHealthAnswerById" parameterType="Long">
        delete from front_health_answer where id = #{id}
    </delete>

    <delete id="deleteFrontHealthAnswerByIds" parameterType="String">
        delete from front_health_answer where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
