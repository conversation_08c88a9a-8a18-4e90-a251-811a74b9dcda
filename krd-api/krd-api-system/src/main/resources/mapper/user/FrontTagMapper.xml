<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontTagMapper">

    <resultMap type="com.ruoyi.system.api.domain.user.FrontTag" id="FrontTagResult">
        <result property="id"    column="id"    />
        <result property="tagType"    column="tag_type"    />
        <result property="tagName"    column="tag_name"    />
        <result property="tagSex"    column="tag_sex"    />
        <result property="tagCity"    column="tag_city"    />
        <result property="tagRegister"    column="tag_register"    />
        <result property="tagDeal"    column="tag_deal"    />
        <result property="tagAmount"    column="tag_amount"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="userCount"    column="user_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectFrontTagVo">
        select id, tag_type, tag_name, tag_sex, tag_city, tag_register, tag_deal, tag_amount, create_time, update_time, is_del, user_count, create_by, update_by from front_tag
    </sql>

    <select id="selectFrontTagList" parameterType="com.ruoyi.system.api.domain.user.FrontTag" resultMap="FrontTagResult">
        <include refid="selectFrontTagVo"/>
        <where>
            <if test="tagType != null "> and tag_type = #{tagType}</if>
            <if test="tagName != null  and tagName != ''"> and tag_name like concat('%', #{tagName}, '%')</if>
            <if test="tagSex != null "> and tag_sex = #{tagSex}</if>
            <if test="tagCity != null  and tagCity != ''"> and tag_city = #{tagCity}</if>
            <if test="tagRegister != null  and tagRegister != ''"> and tag_register = #{tagRegister}</if>
            <if test="tagDeal != null  and tagDeal != ''"> and tag_deal = #{tagDeal}</if>
            <if test="tagAmount != null  and tagAmount != ''"> and tag_amount = #{tagAmount}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="userCount != null "> and user_count = #{userCount}</if>
        </where>
    </select>

    <select id="selectFrontTagById" parameterType="Long" resultMap="FrontTagResult">
        <include refid="selectFrontTagVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontTag" parameterType="com.ruoyi.system.api.domain.user.FrontTag" useGeneratedKeys="true" keyProperty="id">
        insert into front_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagType != null">tag_type,</if>
            <if test="tagName != null">tag_name,</if>
            <if test="tagSex != null">tag_sex,</if>
            <if test="tagCity != null">tag_city,</if>
            <if test="tagRegister != null">tag_register,</if>
            <if test="tagDeal != null">tag_deal,</if>
            <if test="tagAmount != null">tag_amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="userCount != null">user_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagType != null">#{tagType},</if>
            <if test="tagName != null">#{tagName},</if>
            <if test="tagSex != null">#{tagSex},</if>
            <if test="tagCity != null">#{tagCity},</if>
            <if test="tagRegister != null">#{tagRegister},</if>
            <if test="tagDeal != null">#{tagDeal},</if>
            <if test="tagAmount != null">#{tagAmount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="userCount != null">#{userCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateFrontTag" parameterType="com.ruoyi.system.api.domain.user.FrontTag">
        update front_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagType != null">tag_type = #{tagType},</if>
            <if test="tagName != null">tag_name = #{tagName},</if>
            <if test="tagSex != null">tag_sex = #{tagSex},</if>
            <if test="tagCity != null">tag_city = #{tagCity},</if>
            <if test="tagRegister != null">tag_register = #{tagRegister},</if>
            <if test="tagDeal != null">tag_deal = #{tagDeal},</if>
            <if test="tagAmount != null">tag_amount = #{tagAmount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="userCount != null">user_count = #{userCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontTagById" parameterType="Long">
        delete from front_tag where id = #{id}
    </delete>

    <delete id="deleteFrontTagByIds" parameterType="String">
        delete from front_tag where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
