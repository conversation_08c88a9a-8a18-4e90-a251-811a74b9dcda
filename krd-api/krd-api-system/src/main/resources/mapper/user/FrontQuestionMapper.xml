<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontQuestionMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontQuestion" id="FrontQuestionResult">
        <result property="id"    column="id"    />
        <result property="packageId"    column="package_id"    />
        <result property="questName"    column="quest_name"    />
        <result property="isPublish"    column="is_publish"    />
        <result property="content"    column="content"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="answer"    column="answer"    />
        <result property="point"    column="point"    />
        <result property="intro"    column="intro"    />
    </resultMap>

    <sql id="selectFrontQuestionVo">
        select id, package_id, quest_name, is_publish, content, create_user_id, create_time, update_time, is_del, create_by, update_by, answer, point, intro from front_question
    </sql>

    <select id="selectFrontQuestionList" parameterType="com.ruoyi.system.api.domain.FrontQuestion" resultMap="FrontQuestionResult">
        <include refid="selectFrontQuestionVo"/>
        <where>
            <if test="packageId != null "> and package_id = #{packageId}</if>
            <if test="questName != null  and questName != ''"> and quest_name like concat('%', #{questName}, '%')</if>
            <if test="isPublish != null "> and is_publish = #{isPublish}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="createUserId != null "> and create_user_id = #{createUserId}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="answer != null  and answer != ''"> and answer = #{answer}</if>
            <if test="point != null "> and point = #{point}</if>
            <if test="intro != null  and intro != ''"> and intro = #{intro}</if>
        </where>
    </select>

    <select id="selectFrontQuestionById" parameterType="Long" resultMap="FrontQuestionResult">
        <include refid="selectFrontQuestionVo"/>
        where id = #{id}
    </select>
    <select id="selectUniQuestionList" resultType="com.ruoyi.system.api.domain.FrontQuestion">

        <include refid="selectFrontQuestionVo"/>
        where is_publish = 1
    </select>

    <insert id="insertFrontQuestion" parameterType="com.ruoyi.system.api.domain.FrontQuestion" useGeneratedKeys="true" keyProperty="id">
        insert into front_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packageId != null">package_id,</if>
            <if test="questName != null">quest_name,</if>
            <if test="isPublish != null">is_publish,</if>
            <if test="content != null">content,</if>
            <if test="createUserId != null">create_user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="answer != null">answer,</if>
            <if test="point != null">point,</if>
            <if test="intro != null">intro,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packageId != null">#{packageId},</if>
            <if test="questName != null">#{questName},</if>
            <if test="isPublish != null">#{isPublish},</if>
            <if test="content != null">#{content},</if>
            <if test="createUserId != null">#{createUserId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="answer != null">#{answer},</if>
            <if test="point != null">#{point},</if>
            <if test="intro != null">#{intro},</if>
        </trim>
    </insert>

    <update id="updateFrontQuestion" parameterType="com.ruoyi.system.api.domain.FrontQuestion">
        update front_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="questName != null">quest_name = #{questName},</if>
            <if test="isPublish != null">is_publish = #{isPublish},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createUserId != null">create_user_id = #{createUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="answer != null">answer = #{answer},</if>
            <if test="point != null">point = #{point},</if>
            <if test="intro != null">intro = #{intro},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontQuestionById" parameterType="Long">
        delete from front_question where id = #{id}
    </delete>

    <delete id="deleteFrontQuestionByIds" parameterType="String">
        delete from front_question where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
