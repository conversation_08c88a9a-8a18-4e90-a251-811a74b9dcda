<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontCouponTypeMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontCouponType" id="FrontCouponTypeResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectFrontCouponTypeVo">
        select id, name, create_time from front_coupon_type
    </sql>

    <select id="selectFrontCouponTypeList" parameterType="com.ruoyi.system.api.domain.FrontCouponType" resultMap="FrontCouponTypeResult">
        <include refid="selectFrontCouponTypeVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
    </select>

    <select id="selectFrontCouponTypeById" parameterType="Long" resultMap="FrontCouponTypeResult">
        <include refid="selectFrontCouponTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrontCouponType" parameterType="com.ruoyi.system.api.domain.FrontCouponType" useGeneratedKeys="true" keyProperty="id">
        insert into front_coupon_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateFrontCouponType" parameterType="com.ruoyi.system.api.domain.FrontCouponType">
        update front_coupon_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontCouponTypeById" parameterType="Long">
        delete from front_coupon_type where id = #{id}
    </delete>

    <delete id="deleteFrontCouponTypeByIds" parameterType="String">
        delete from front_coupon_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>