<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontFastmailMapper">
    
    <resultMap type="com.ruoyi.system.api.domain.FrontFastmail" id="FrontFastmailResult">
        <result property="id"    column="id"    />
        <result property="fastType"    column="fast_type"    />
        <result property="number"    column="number"    />
        <result property="company"    column="company"    />
        <result property="orderNumber"    column="order_number"    />
        <result property="userMobile"    column="user_mobile"    />
        <result property="delivery"    column="delivery"    />
        <result property="deliveryMobile"    column="delivery_mobile"    />
        <result property="status"    column="status"    />
        <result property="orderTime"    column="order_time"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectFrontFastmailVo">
        select id, fast_type, number, company, order_number, user_mobile, delivery, delivery_mobile, status, order_time, finish_time, is_del, create_time, update_time, create_by, update_by from front_fastmail
    </sql>

    <select id="selectFrontFastmailList" parameterType="com.ruoyi.system.api.domain.FrontFastmail" resultMap="FrontFastmailResult">
        <include refid="selectFrontFastmailVo"/>
        <where>  
            <if test="fastType != null "> and fast_type = #{fastType}</if>
            <if test="number != null  and number != ''"> and number = #{number}</if>
            <if test="company != null  and company != ''"> and company = #{company}</if>
            <if test="orderNumber != null  and orderNumber != ''"> and order_number like concat('%',#{orderNumber},'%') </if>
            <if test="userMobile != null  and userMobile != ''"> and user_mobile = #{userMobile}</if>
            <if test="delivery != null  and delivery != ''"> and delivery = #{delivery}</if>
            <if test="deliveryMobile != null  and deliveryMobile != ''"> and delivery_mobile = #{deliveryMobile}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="orderTime != null "> and order_time = #{orderTime}</if>
            <if test="finishTime != null "> and finish_time = #{finishTime}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectFrontFastmailById" parameterType="Long" resultMap="FrontFastmailResult">
        <include refid="selectFrontFastmailVo"/>
        where id = #{id}
    </select>

    <select id="selectFrontFastmailByOrderNum" parameterType="String" resultMap="FrontFastmailResult">
        <include refid="selectFrontFastmailVo"/>
        where order_number = #{num}
    </select>

    <insert id="insertFrontFastmail" parameterType="com.ruoyi.system.api.domain.FrontFastmail">
        insert into front_fastmail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fastType != null">fast_type,</if>
            <if test="number != null">number,</if>
            <if test="company != null">company,</if>
            <if test="orderNumber != null">order_number,</if>
            <if test="userMobile != null">user_mobile,</if>
            <if test="delivery != null">delivery,</if>
            <if test="deliveryMobile != null">delivery_mobile,</if>
            <if test="status != null">status,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fastType != null">#{fastType},</if>
            <if test="number != null">#{number},</if>
            <if test="company != null">#{company},</if>
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="userMobile != null">#{userMobile},</if>
            <if test="delivery != null">#{delivery},</if>
            <if test="deliveryMobile != null">#{deliveryMobile},</if>
            <if test="status != null">#{status},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateFrontFastmail" parameterType="com.ruoyi.system.api.domain.FrontFastmail">
        update front_fastmail
        <trim prefix="SET" suffixOverrides=",">
            <if test="fastType != null">fast_type = #{fastType},</if>
            <if test="number != null">number = #{number},</if>
            <if test="company != null">company = #{company},</if>
            <if test="orderNumber != null">order_number = #{orderNumber},</if>
            <if test="userMobile != null">user_mobile = #{userMobile},</if>
            <if test="delivery != null">delivery = #{delivery},</if>
            <if test="deliveryMobile != null">delivery_mobile = #{deliveryMobile},</if>
            <if test="status != null">status = #{status},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontFastmailById" parameterType="Long">
        delete from front_fastmail where id = #{id}
    </delete>

    <delete id="deleteFrontFastmailByIds" parameterType="String">
        delete from front_fastmail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>