<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontUserMapper">

    <resultMap type="com.ruoyi.system.api.domain.FrontUser" id="FrontUserResult">
        <result property="id"    column="id"    />
        <result property="userIcon"    column="user_icon"    />
        <result property="userName"    column="user_name"    />
        <result property="userCity"    column="user_city"    />
        <result property="userTag"    column="user_tag"    />
        <result property="userMobile"    column="user_mobile"    />
        <result property="userBirthday"    column="user_birthday"    />
        <result property="userAge"    column="user_age"    />
        <result property="userSex"    column="user_sex"    />
        <result property="userHeight"    column="user_height"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="userIp"    column="user_ip"    />
        <result property="isDoQuest"    column="is_do_quest"    />
        <result property="userWeight"    column="user_weight"    />
        <result property="periodCycle"    column="period_cycle"    />
        <result property="periodLength"    column="period_length"    />
        <result property="nearPeriodDate"    column="near_period_date"    />
        <result property="isAnswerHealth"    column="is_answer_health"    />
        <result property="isGoPeriod"    column="is_go_period"    />
        <result property="nowMoney"    column="now_money"    />
        <result property="integral"    column="integral"    />
        <result property="payCount"    column="pay_count"    />
    </resultMap>

    <sql id="selectFrontUserVo">
        select id, user_icon, user_name,is_answer_health,pay_count,now_money,integral, is_go_period,near_period_date,user_weight,user_city, user_tag, user_mobile, user_age, user_sex, user_height, create_time, update_time, status, user_ip, is_do_quest from front_user
    </sql>

    <select id="selectFrontUserList"  resultType="com.ruoyi.system.api.domain.FrontUser">
        SELECT
        a.id,
        a.user_name userName,
        a.user_mobile userMobile,
        a.status,
        GROUP_CONCAT(DISTINCT g.tag_name ORDER BY g.id) AS tagName,
        c.balance AS balance,
        COUNT(DISTINCT d.id) AS couponCount,
        COUNT(DISTINCT e.id) AS giftCount,
        f.balance AS points,
        a.create_time createTime
        FROM front_user a
        LEFT JOIN front_user_tag b ON b.user_id = a.id
        LEFT JOIN front_tag g ON b.tag_id = g.id
        -- 最新 balance 信息（按时间最大）
        LEFT JOIN (
        SELECT t1.*
        FROM front_balance_info t1
        JOIN (
        SELECT user_id, MAX(create_time) AS max_time
        FROM front_balance_info
        GROUP BY user_id
        ) t2 ON t1.user_id = t2.user_id AND t1.create_time = t2.max_time
        ) c ON c.user_id = a.id
        -- 最新 source 信息（按时间最大）
        LEFT JOIN (
        SELECT t1.*
        FROM front_source t1
        JOIN (
        SELECT user_id, MAX(create_time) AS max_time
        FROM front_source
        GROUP BY user_id
        ) t2 ON t1.user_id = t2.user_id AND t1.create_time = t2.max_time
        ) f ON f.user_id = a.id
        LEFT JOIN front_coupon_info d ON d.user_id = a.id AND d.status = '0'
        LEFT JOIN front_gift_info e ON e.user_id = a.id AND e.status = '0'
        <where>
            <if test="searchText != null and searchText != ''">
                and
                (
                user_name like concat('%', #{searchText},'%')or user_mobile like concat('%', #{searchText},'%')
                )
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
        GROUP BY a.id
    </select>

    <!-- 用户详情 -->
    <select id="selectFrontUserById" parameterType="Long" resultType="com.ruoyi.system.api.domain.FrontUser">
        select
            *
        from front_user where id = #{id}
    </select>

    <insert id="insertFrontUser" parameterType="com.ruoyi.system.api.domain.FrontUser" useGeneratedKeys="true" keyProperty="id">
        insert into front_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userIcon != null">user_icon,</if>
            <if test="userName != null">user_name,</if>
            <if test="userCity != null">user_city,</if>
            <if test="userTag != null">user_tag,</if>
            <if test="userMobile != null">user_mobile,</if>
            <if test="userBirthday != null">user_birthday,</if>
            <if test="userAge != null">user_age,</if>
            <if test="userSex != null">user_sex,</if>
            <if test="userHeight != null">user_height,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="userIp != null">user_ip,</if>
            <if test="isDoQuest != null">is_do_quest,</if>
            <if test="userWeight != null">user_weight,</if>
            <if test="periodCycle != null">period_cycle,</if>
            <if test="periodLength != null">period_length,</if>
            <if test="nearPeriodDate != null">near_period_date,</if>
            <if test="isGoPeriod != null">is_go_period,</if>
            <if test="nowMoney != null"> now_money,</if>
            <if test="integral != null">integral,</if>
            <if test="payCount != null">pay_count,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userIcon != null">#{userIcon},</if>
            <if test="userName != null">#{userName},</if>
            <if test="userCity != null">#{userCity},</if>
            <if test="userTag != null">#{userTag},</if>
            <if test="userMobile != null">#{userMobile},</if>
            <if test="userBirthday != null">#{userBirthday},</if>
            <if test="userAge != null">#{userAge},</if>
            <if test="userSex != null">#{userSex},</if>
            <if test="userHeight != null">#{userHeight},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="userIp != null">#{userIp},</if>
            <if test="isDoQuest != null">#{isDoQuest},</if>
            <if test="userWeight != null">#{userWeight},</if>
            <if test="periodCycle != null">#{periodCycle},</if>
            <if test="periodLength != null">#{periodLength},</if>
            <if test="nearPeriodDate != null">#{nearPeriodDate},</if>
            <if test="isGoPeriod != null">#{isGoPeriod},</if>
            <if test="nowMoney != null">#{nowMoney},</if>
            <if test="integral != null">#{integral},</if>
            <if test="payCount != null">#{payCount},</if>
        </trim>
    </insert>

    <update id="updateFrontUser" parameterType="com.ruoyi.system.api.domain.FrontUser">
        update front_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userIcon != null">user_icon = #{userIcon},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="userCity != null">user_city = #{userCity},</if>
            <if test="userTag != null">user_tag = #{userTag},</if>
            <if test="userMobile != null">user_mobile = #{userMobile},</if>
            <if test="userBirthday != null">user_birthday = #{userBirthday},</if>
            <if test="userAge != null">user_age = #{userAge},</if>
            <if test="userSex != null">user_sex = #{userSex},</if>
            <if test="userHeight != null">user_height = #{userHeight},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="userIp != null">user_ip = #{userIp},</if>
            <if test="isDoQuest != null">is_do_quest = #{isDoQuest},</if>
            <if test="userWeight != null">user_weight = #{userWeight},</if>
            <if test="periodCycle != null">period_cycle = #{periodCycle},</if>
            <if test="periodLength != null">period_length = #{periodLength},</if>
            <if test="nearPeriodDate != null">near_period_date = #{nearPeriodDate},</if>
            <if test="isAnswerHealth != null">is_answer_health = #{isAnswerHealth},</if>
            <if test="isGoPeriod != null">is_go_period = #{isGoPeriod},</if>
            <if test="nowMoney != null"> now_money = #{nowMoney},</if>
            <if test="integral != null">integral = #{integral},</if>
            <if test="payCount != null">pay_count = #{payCount},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteFrontUserById" parameterType="Long">
        delete from front_user where id = #{id}
    </delete>

    <delete id="deleteFrontUserByIds" parameterType="String">
        delete from front_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 0-余额 1-礼品卡 2-优惠券 3-积分 -->
    <select id="listMoneyPackageInfo" resultType="com.ruoyi.system.api.domain.resp.FrontUserMoneyResp">
        <if test="type == 0">
            select
            id,type,moveaccount,balance,create_time createTime,order_number orderNumber
            from front_balance_info
        </if>
        <if test="type == 1">
            select
            id,type,balance,create_time createTime,order_number orderNumber,status,use_time useTime,count
            from front_gift_info
        </if>
        <if test="type == 2">
            select
            id,type,balance,create_time createTime,order_number orderNumber,status,use_time useTime,count
            from front_coupon_info
        </if>
        <if test="type == 3">
            select
            id,type,balance,create_time createTime,order_number orderNumber,source
            from front_coupon_info
        </if>
        where user_id = #{userId}
        order by create_time desc
    </select>


    <select id="selectStatisticsInfoByUserId" resultType="com.ruoyi.system.api.domain.user.StatisticsInfo">
        with totalAmount as (
            select sum(total_price) consumptionAmount,count(order_number) orderCount,sum(status=4) afterSaleRefundCount,
                   sum(type = 0) checkCount
            from front_order_info where user_id = #{userId}
        ),
             available as (
                 select sum(balance) available from front_source where user_id = #{userId}
             ),
             giftCardAmount as (
                 select sum(balance) giftCardAmount from front_gift_info where user_id = #{userId}
             ),
             couponAmount as (
                 select concat(sum(balance),count(1)) availableCouponAmount from front_coupon_info where user_id = #{userId} and status = 0
             ),
             inviteCount as (
                 select count(1) inviteFriendsCount from front_invite where user_id = #{userId}
             ),
             collect as (
                 select count(1) collectGoodsCount from front_shop_collect where user_id = #{userId}
             ),
             evaluation as (
                 select count(1) goodsEvaluation from front_evaluate where user_id = #{userId}
             )
        select * from totalAmount,available,giftCardAmount,couponAmount,inviteCount,collect,evaluation
    </select>

    <select id="selectFrontAddressByUserId" resultType="com.ruoyi.system.api.domain.user.FrontAddress">
        select id,is_default isDefault,name,mobile,area,address from front_address where user_id = #{userId} and is_del = 0 order by is_default asc
    </select>

    <select id="selectCheckPackage" resultType="com.ruoyi.system.api.domain.user.CheckPackage">
        select a.id,c.name name,
               case when c.is_self = 0 then '家庭成员' else '本人' end as identity,
               c.age as age,
            d.pakeage_name packageName ,
            GROUP_CONCAT(case when e.type = '检验' then e.handle_user else '' end) checkPerson,
            GROUP_CONCAT(case when e.type = '审核' then e.handle_user else '' end) auditPerson,
			GROUP_CONCAT(case when e.type = '检验' then e.handle_time else '' end) checkTime
        from front_number a
            left join front_family c on a.order_family_id = c.id
            left join front_package d on d.id = a.package_id
            left join front_number_info e on e.number_id = a.id
        where a.order_user_id = #{id} and d.is_del = 0
        GROUP BY c.name
    </select>

    <select id="selectPhoneOne" resultType="com.ruoyi.system.api.domain.FrontUser">
        <include refid="selectFrontUserVo"/>
        where user_mobile = #{phone}
    </select>
    <select id="selectUserDealCount" resultType="java.lang.Integer">
        SELECT user_id
        FROM front_orders
        WHERE paid = 1
        <choose>
            <when test="start != null and start != '' and end != null and end != ''">
                AND pay_time BETWEEN STR_TO_DATE(#{start}, '%Y-%m-%d')
                AND STR_TO_DATE(#{end}, '%Y-%m-%d') + INTERVAL 1 DAY - INTERVAL 1 SECOND
            </when>

            <when test="start != null and start != ''">
                AND pay_time >= STR_TO_DATE(#{start}, '%Y-%m-%d')
            </when>

            <when test="end != null and end != ''">
                AND pay_time &lt;= STR_TO_DATE(#{end}, '%Y-%m-%d') + INTERVAL 1 DAY - INTERVAL 1 SECOND
            </when>
        </choose>
        GROUP BY user_id
        <if test="amount != null and amount != ''">
            HAVING COUNT(*) >= #{amount}
        </if>
    </select>
    <select id="selectUserDealAmount" resultType="java.lang.Integer">
        SELECT
        user_id
        FROM
        front_orders
        WHERE
        paid = 1

        <choose>
            <when test="start != null and start != '' and end != null and end != ''">
                AND pay_time BETWEEN STR_TO_DATE(#{start}, '%Y-%m-%d')
                AND STR_TO_DATE(#{end}, '%Y-%m-%d') + INTERVAL 1 DAY - INTERVAL 1 SECOND
            </when>

            <when test="start != null and start != ''">
                AND pay_time >= STR_TO_DATE(#{start}, '%Y-%m-%d')
            </when>

            <when test="end != null and end != ''">
                AND pay_time &lt;= STR_TO_DATE(#{end}, '%Y-%m-%d') + INTERVAL 1 DAY - INTERVAL 1 SECOND
            </when>
        </choose>

        GROUP BY
        user_id

        <if test="amount != null and amount != ''">
            HAVING SUM(total_price) >= #{amount}
        </if>
    </select>

</mapper>
