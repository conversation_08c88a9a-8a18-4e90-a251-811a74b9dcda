package com.ruoyi.system.api.vo;

import lombok.Data;

/**
 * 用户礼品卡
 * <AUTHOR>
 * @date 2025/6/9 18:29
 */
@Data
public class UserGiftVO {

    /**
     * 礼品卡ID
     */
    private Long giftId;
    /**
     * 礼品卡类型：0-购买 1-好友赠送 2-购物抵扣
     */
    private String giftType;
    /**
     * 礼品卡数量
     */
    private Integer giftCount;
    /**
     * 礼品卡余额
     */
    private String giftBalance;
    /**
     * 礼品卡余额(变动后)
     */
    private String giftBalanceAfter;
    /**
     * 礼品卡使用/过期时间
     */
    private String giftUseTime;
    /**
     * 订单编号
     */
    private String orderNumber;
    /**
     * 礼品卡状态：0-待使用 1-已使用 2-已过期 3-使用中
     */
    private String giftStatus;
}
