package com.ruoyi.system.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontCouponType;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-30
 */
public interface FrontCouponTypeMapper extends BaseMapper<FrontCouponType>
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public FrontCouponType selectFrontCouponTypeById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param frontCouponType 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<FrontCouponType> selectFrontCouponTypeList(FrontCouponType frontCouponType);

    /**
     * 新增【请填写功能名称】
     *
     * @param frontCouponType 【请填写功能名称】
     * @return 结果
     */
    public int insertFrontCouponType(FrontCouponType frontCouponType);

    /**
     * 修改【请填写功能名称】
     *
     * @param frontCouponType 【请填写功能名称】
     * @return 结果
     */
    public int updateFrontCouponType(FrontCouponType frontCouponType);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteFrontCouponTypeById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontCouponTypeByIds(Long[] ids);
}
