package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告管理对象 front_advert
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Getter
@Setter
@Data
@TableName("front_advert")
public class FrontAdvert implements Serializable
{

    /** $column.columnComment */
    private Long id;

    /** 广告标题 */
    @Excel(name = "广告标题")
    private String adTitle;

    /** 广告类型id */
    @Excel(name = "广告类型 0-首页广告 1-商城广告 2-经期记录广告")
    private Long adType;

    @TableField(exist = false)
    private String adTypeName;

    /** 链接地址 */
    @Excel(name = "链接地址")
    private String adUrl;

    /** 广告位置 */
    @Excel(name = "广告位置")
    private Long adAddress;

    @Excel(name = "广告图片")
    private String material;

    /** 状态 0-false 1-true */
    @Excel(name = "状态 0-false 1-true")
    private Long status;

    /** 是否删除 0-false 1-true */
    @Excel(name = "是否删除 0-false 1-true")
    private Long isDel;

    private String createBy;
    private String updateBy;
    private Date updateTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
