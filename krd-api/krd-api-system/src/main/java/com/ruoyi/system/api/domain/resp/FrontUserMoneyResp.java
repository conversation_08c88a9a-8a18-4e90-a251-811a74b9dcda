package com.ruoyi.system.api.domain.resp;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "钱包明细返回数据")
public class FrontUserMoneyResp {
    private Long id;
    @Schema(title = "来源 0-签到 1-答问卷 2-注册 3-邀请好友 4-分享 5-购物抵扣")
    private int source;
    @Schema(title = "类型 0-充值 1-购物 2-退款")
    private int type;
    @Schema(title = "动账 元 0为+ ，1、2 为-")
    private String moveaccount;
    @Schema(title = "金额")
    private String balance;
    @Schema(title = "变动后金额")
    private String changeBalance;
    @Schema(title = "订单编号")
    private String orderNumber;
    @Schema(title = "创建时间")
    private String createTime;
    @Schema(title = "状态")
    private String status;
    @Schema(title = "使用时间")
    private String useTime;
    @Schema(title = "购买张数")
    private String count;
    @Schema(title = "变动后数量")
    private String changeCount;


}
