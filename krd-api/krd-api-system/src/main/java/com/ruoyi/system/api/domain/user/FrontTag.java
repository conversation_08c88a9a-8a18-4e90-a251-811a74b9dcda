package com.ruoyi.system.api.domain.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标签管理对象 front_tag
 *
 * @date 2025-05-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("front_tag")
public class FrontTag extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 0-自动标签 1-手动标签（没有后续填写项目） */
    @Excel(name = "0-自动标签 1-手动标签", readConverterExp = "没=有后续填写项目")
    private Long tagType;

    /** 标签名称 */
    @Excel(name = "标签名称")
    private String tagName;

    /** 0-全部，1-男 ，2-女 */
    @Excel(name = "0-全部，1-男 ，2-女")
    private Long tagSex;

    /** 无限制时保存为0，其余按实际保存 */
    @Excel(name = "无限制时保存为0，其余按实际保存")
    private String tagCity;

    /** 无限制时保存为0，其余按实际保存 */
    @Excel(name = "无限制时保存为0，其余按实际保存")
    private String tagRegister;

    /** 无限制时保存为0，其余按实际保存 */
    @Excel(name = "无限制时保存为0，其余按实际保存")
    private String tagDeal;

    /** 无限制时保存为0，其余按实际保存 */
    @Excel(name = "无限制时保存为0，其余按实际保存")
    private String tagAmount;

    /** 0-false,1-true */
    @Excel(name = "0-false,1-true")
    private Long isDel;

    /** 人数 */
    @Excel(name = "人数")
    private Integer userCount;

}
