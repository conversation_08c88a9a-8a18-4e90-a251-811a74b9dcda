package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采集视频对象 front_video
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "采集视频对象")
@TableName("front_video")
public class FrontVideo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 视频标题 */
    @Excel(name = "视频标题")
    private String title;

    /** 视频链接 */
    @Excel(name = "视频链接")
    private String url;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 0-false 1-true */
    @Excel(name = "0-false 1-true")
    private Long status;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;


    /** 时长 */
    @Excel(name = "时长")
    private String size;


    /** 封面 */
    @Excel(name = "封面")
    private String cover;

    private String step;

}
