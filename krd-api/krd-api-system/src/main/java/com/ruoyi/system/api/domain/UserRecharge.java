package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户充值表

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("front_recharge")
public class UserRecharge implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(name = "充值用户UID")
    private Integer uid;

    @Schema(name = "订单号")
    private String orderId;

    @Schema(name = "充值金额")
    private BigDecimal price;

    @Schema(name = "购买赠送金额")
    private BigDecimal givePrice;

    @Schema(name = "充值类型")
    private String rechargeType;

    @Schema(name = "是否充值")
    private Boolean paid;

    @Schema(name = "充值支付时间")
    private Date payTime;

    @Schema(name = "充值时间")
    private Date createTime;

    @Schema(name = "退款金额")
    private BigDecimal refundPrice;

    @Schema(name = "礼品卡id(礼品卡购买专用)")
    private Long giftId;


}
