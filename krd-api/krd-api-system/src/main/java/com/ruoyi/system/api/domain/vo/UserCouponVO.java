package com.ruoyi.system.api.domain.vo;

import lombok.Data;

/**
 * 用户优惠券
 * <AUTHOR>
 * @date 2025/6/9 18:29
 */
@Data
public class UserCouponVO {

    /**
     * 优惠券id
     */
    private Long couponId;
    /**
     * 领取方式 0-主动领取 1-好友赠送 2-购物抵扣 3-已过期
     */
    private String couponType;
    /**
     * 数量（张）
     */
    private Integer couponCount;
    /**
     * 金额（元）
     */
    private String couponBalance;
    /**
     * 变动后数量（张）
     */
    private Integer couponCountAfter;
    /**
     * 变动后金额（元）
     */
    private String couponBalanceAfter;
    /**
     * 使用/过期时间
     */
    private String couponUseTime;
    /**
     * 订单编号
     */
    private String orderNumber;
    /**
     * 0-待使用 1-已使用 2-已过期
     */
    private String couponStatus;
}
