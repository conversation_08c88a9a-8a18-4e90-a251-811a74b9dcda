package com.ruoyi.system.api.mapper;

import com.ruoyi.system.api.domain.FrontOrdersGoods;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @ClassName FrontOrdersGoodsMapper
 * @Description 订单商品mapper
 * <AUTHOR>
 * @Date 2025/5/20 下午6:17
 */
@Mapper
public interface FrontOrdersGoodsMapper extends BaseMapper<FrontOrdersGoods>{
    /**
     * 查询订单-商品信息
     *
     * @param id 订单-商品信息主键
     * @return 订单-商品信息
     */
    public FrontOrdersGoods selectFrontOrdersGoodsById(Long id);

    /**
     * 查询订单-商品信息列表
     *
     * @param frontOrdersGoods 订单-商品信息
     * @return 订单-商品信息集合
     */
    public List<FrontOrdersGoods> selectFrontOrdersGoodsList(FrontOrdersGoods frontOrdersGoods);

    /**
     * 新增订单-商品信息
     *
     * @param frontOrdersGoods 订单-商品信息
     * @return 结果
     */
    public int insertFrontOrdersGoods(FrontOrdersGoods frontOrdersGoods);

    /**
     * 修改订单-商品信息
     *
     * @param frontOrdersGoods 订单-商品信息
     * @return 结果
     */
    public int updateFrontOrdersGoods(FrontOrdersGoods frontOrdersGoods);

    /**
     * 删除订单-商品信息
     *
     * @param id 订单-商品信息主键
     * @return 结果
     */
    public int deleteFrontOrdersGoodsById(Long id);

    /**
     * 批量删除订单-商品信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontOrdersGoodsByIds(Long[] ids);
}
