package com.ruoyi.system.api.mapper;

import com.ruoyi.system.api.domain.user.FrontTag;

import java.util.List;

/**
 * 标签管理Mapper接口
 *
 * @date 2025-05-09
 */
public interface FrontTagMapper
{
    /**
     * 查询标签管理
     *
     * @param id 标签管理主键
     * @return 标签管理
     */
    public FrontTag selectFrontTagById(Long id);

    /**
     * 查询标签管理列表
     *
     * @param frontTag 标签管理
     * @return 标签管理集合
     */
    public List<FrontTag> selectFrontTagList(FrontTag frontTag);

    /**
     * 新增标签管理
     *
     * @param frontTag 标签管理
     * @return 结果
     */
    public int insertFrontTag(FrontTag frontTag);

    /**
     * 修改标签管理
     *
     * @param frontTag 标签管理
     * @return 结果
     */
    public int updateFrontTag(FrontTag frontTag);

    /**
     * 删除标签管理
     *
     * @param id 标签管理主键
     * @return 结果
     */
    public int deleteFrontTagById(Long id);

    /**
     * 批量删除标签管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontTagByIds(Long[] ids);
}
