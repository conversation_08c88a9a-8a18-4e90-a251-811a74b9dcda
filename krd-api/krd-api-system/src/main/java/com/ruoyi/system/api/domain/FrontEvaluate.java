package com.ruoyi.system.api.domain;


import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName FrontEvaluate
 * @TableName front_evaluate
 * @Description 评价表
 * <AUTHOR>
 * @Date 2025/5/20 下午4:47
 */
@Data
@Schema(description = "评价表")
@TableName("front_evaluate")
public class FrontEvaluate implements Serializable {
    @Schema(description="id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @Schema(description="用户id")
    private Long userId;
    @Schema(description="订单ID")
    private Long orderId;
    @Schema(description="商品id / 套餐id")
    private Long goodsId;
    @Schema(description="规格ID")
    private Long specId;
    @Schema(description="评价星级")
    private Integer starRating;
    @Schema(description="0-好评 1-中评 2-差评")
    private Integer type;
    @Schema(description="评价内容")
    private String content;
    @Schema(description="评价图片")
    private String pic;
    @Schema(description="评价时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime tickTime;
    @Schema(description="回复内容")
    private String replyContent;
    @Schema(description="回复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime replyTime;
    @Schema(description="0-待回复 1-已回复 ")
    private Integer isReply;
    @Schema(description="0-未隐藏 1-显示")
    private Integer isShow;
    @Schema(description="0-未加精 1-加精")
    private Integer isRefine;
    @Schema(description="0-未删除 1-删除")
    private String isDel;
    @Schema(description="")
    private LocalDateTime createTime;
    @Schema(description="")
    private LocalDateTime updateTime;
    @Schema(description="")
    private String createBy;
    @Schema(description="")
    private String updateBy;
}
