package com.ruoyi.system.api.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.ruoyi.common.core.annotation.Excel;

/**
 * 商品规格对象 front_goods_spec
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Data
@Schema(description = "商品规格对象")
@TableName("front_goods_spec")
public class FrontGoodsSpec implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Schema(name = "商品规格id")
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 商品类型 0-检测套餐 1-商城商品 */
    @Schema(name = "商品类型 0-检测套餐 1-商城商品")
    private Integer goodsType;

    /** 商品id */
    @Excel(name = "商品id")
    @Schema(name = "商品id")
    private Long goodsId;

    /** 规格名称 */
    @Excel(name = "规格名称")
    @Schema(name = "规格名称")
    private String title;

    /** 原价 */
    @Excel(name = "原价")
    @Schema(name = "原价")
    private BigDecimal price;

    /** 库存 */
    @Excel(name = "库存（默认0不限数量 其他为限量）")
    @Schema(name = "库存（默认0不限数量 其他为限量）")
    private Long amount;

    /** 图片地址 */
    @Excel(name = "图片地址")
    @Schema(name = "图片地址")
    private String pic;

    /** 0-未删除 1-删除 */
    @Excel(name = "0-未删除 1-删除")
    @Schema(name = "0-未删除 1-删除")
    private Long isDel;

    /** 创建时间 */
    private LocalDateTime createTime;
    /** 修改时间 */
    private LocalDateTime updateTime;
    /** 创建人 */
    private String createBy;
    /** 修改人 */
    private String updateBy;

    /** b补充字段 **/
    @Schema(name = "会员价")
    @TableField(exist = false)
    private BigDecimal vipPrice;
}
