package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订单-发票信息对象 front_orders_invoice
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@Schema(description = "订单-发票信息对象")
@TableName("front_orders_invoice")
public class FrontOrdersInvoice implements Serializable
{

    /** $column.columnComment */
    @Schema(title = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    @Schema(title = "订单ID")
    private Long orderId;

    /** 0-未开具 1-已开具 */
    @Excel(name = "0-未开具 1-已开具")
    @Schema(title = "0-未开具 1-已开具")
    private Long status;


    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(title = "申请时间")
    private LocalDateTime applyTime;

    /**
     * 单位名称/个人姓名
     */
    @Schema(title = "单位名称/个人姓名")
    private String name;

    /** 发票抬头 */
    @Excel(name = "发票抬头")
    @Schema(title = "发票抬头")
    private String hearder;

    /** 纳税人识别号 */
    @Excel(name = "纳税人识别号")
    @Schema(title = "纳税人识别号")
    private String number;

    /**
     * 注册地址
     */
    @Schema(title = "注册地址")
    private String address;

    /**
     * 注册电话
     */
    @Schema(title = "注册电话")
    private String phone;

    /**
     * 开户银行
     */
    @Schema(title = "开户银行")
    private String bank;

    /**
     * 银行账号
     */
    @Schema(title = "银行账户")
    private String account;

    /** 发票内容 */
    @Excel(name = "发票内容")
    @Schema(title = "发票内容")
    private String content;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;

    /* 新加字段 */
    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "用户名称")
    private String url;
}
