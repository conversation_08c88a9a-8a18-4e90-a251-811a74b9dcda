package com.ruoyi.system.api.oss.service;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.ruoyi.common.core.constant.JiCeConstants;
import com.ruoyi.system.api.domain.SysConfig;
import com.ruoyi.system.api.mapper.SysConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @Author: suhai
 * @Date: 2025/6/5
 * @Description: 使用阿里云 OSS SDK V4.x 的上传服务 私有读写上传文件图片
 */
@Service
public class AliyunOSSService {

    @Autowired
    private SysConfigMapper sysConfigMapper;

    // todo 后续换成 config配置文件获取
    String endpoint = "";
    String bucketName = "";
    String region = ""; //
    String maxFileSize = "";
    String accessKeyId = "";
    String accessKeySecret = "";


    /**
     * 获取OSS配置
     * @return
     */
    private void getOSSConfig(){
        SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique(JiCeConstants.OSS_KEY);
        String configValue = sysConfig.getConfigValue();
        JSONObject jsonObject = JSONObject.parseObject(configValue);
        accessKeyId = jsonObject.getString(JiCeConstants.OSS_ACCESS_KEY_ID);
        accessKeySecret = jsonObject.getString(JiCeConstants.OSS_ACCESS_KEY_SECRET);
        endpoint = jsonObject.getString(JiCeConstants.OSS_ENDPOINT);
        bucketName = jsonObject.getString(JiCeConstants.OSS_BUCKET_NAME);
        region = jsonObject.getString(JiCeConstants.OSS_REGION);
        maxFileSize = jsonObject.getString(JiCeConstants.OSS_MAX_FILE_SIZE);
    }


    /**
     * 上传文件到OSS
     * @param file 文件
     * @param folder 上传到的文件夹
     * @return 文件访问URL
     */
    public String uploadFile(MultipartFile file, String folder) {
        getOSSConfig();
        validateFile(file);
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        CredentialsProvider credentialsProvider = CredentialsProviderFactory.newDefaultCredentialProvider(accessKeyId,accessKeySecret);
        OSS ossClient = OSSClientBuilder.create()
                .endpoint(endpoint)
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();
        try {
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String safeFileName = UUID.randomUUID() + fileExtension;

            // URL 编码，防止非法字符
            String encodedFileName = URLEncoder.encode(safeFileName, StandardCharsets.UTF_8.toString()).replace("+", "%20");

            String filePath = folder + "/" + encodedFileName;

            InputStream inputStream = file.getInputStream();

            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, filePath, inputStream);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setHeader("Content-Type", file.getContentType());
            putObjectRequest.setMetadata(metadata);
            ossClient.putObject(putObjectRequest);
            return generateFileUrl(filePath);
        } catch (OSSException oe) {
            throw new RuntimeException("OSS错误 - 上传失败: " + oe.getErrorMessage(), oe);
        } catch (Exception e) {
            throw new RuntimeException("上传失败: " + e.getMessage(), e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 验证文件是否符合要求
     */
    private void validateFile(MultipartFile file) {
        getOSSConfig();
        long maxSize = parseSize(maxFileSize);
        if (file.getSize() > maxSize) {
            throw new RuntimeException("文件大小不能超过 " + maxFileSize);
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.contains(".")) {
            throw new RuntimeException("无效的文件名");
        }

        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();

        List<String> allowedExtensions = Arrays.asList("jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,mp4".split(","));
        if (!allowedExtensions.contains(fileExtension)) {
            throw new RuntimeException("不支持的文件类型，仅支持: " + String.join(",", allowedExtensions));
        }
    }

    /**
     * 生成文件访问URL
     */
    private String generateFileUrl(String filePath) {
        getOSSConfig();
        return endpoint.replace("https://", "https://" + bucketName + ".") + "/" + filePath;
    }

    /**
     * 将字符串格式的文件大小转换为字节数
     */
    private long parseSize(String size) {
        size = size.toUpperCase();
        if (size.endsWith("KB")) {
            return Long.parseLong(size.substring(0, size.length() - 2)) * 1024;
        } else if (size.endsWith("MB")) {
            return Long.parseLong(size.substring(0, size.length() - 2)) * 1024 * 1024;
        } else if (size.endsWith("GB")) {
            return Long.parseLong(size.substring(0, size.length() - 2)) * 1024 * 1024 * 1024;
        } else {
            return Long.parseLong(size);
        }
    }


    //私有读加签名
    public String getPrivateReadSignatureUrl(String filePath) throws MalformedURLException {
        getOSSConfig();
        URL urlbase = new URL(filePath);
        String objetName = urlbase.getPath();
        if (objetName.startsWith("/")) {
            objetName = objetName.substring(1);
        }
        CredentialsProvider credentialsProvider = CredentialsProviderFactory.newDefaultCredentialProvider(accessKeyId,accessKeySecret);
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        OSS ossClient = OSSClientBuilder.create()
                .endpoint(endpoint)
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();

        try {
            Date expiration = new Date(new Date().getTime() + 3600 * 1000L);
            // 生成以GET方法访问的预签名URL。本示例没有额外请求头，其他人可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(bucketName, objetName, expiration);
            return url.toString();
        } catch (OSSException oe) {
            throw new RuntimeException("OSS错误 - 上传失败: " + oe.getErrorMessage(), oe);
        } catch (ClientException ce) {
            throw new RuntimeException("客户端错误 - 上传失败: " + ce.getErrorMessage(), ce);
        } finally {
            ossClient.shutdown();
        }
    }
}
