package com.ruoyi.system.api.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName FrontCouponVo
 * @Description 优惠券相关交互参数
 * <AUTHOR>
 * @Date 2025/5/22 上午11:47
 */
public interface FrontCouponVo {

    @Data
    @Schema(description = "优惠券列表参数")
    class FrontCouponList {
        @Schema(description = "优惠券id/编号")
        private Integer couponId;
        @Schema(description = "优惠券名称")
        private String title;
        @Schema(name = "优惠券类型")
        private String type;
        @Schema(name = "可使用商品")
        private String used;
        @Schema(name = "使用门槛")
        private String threshold;
        @Schema(name = "面值")
        private BigDecimal balance;
        @Schema(name = "发行量")
        private Long total;
        @Schema(name = "领取量")
        private Long limit;
        @Schema(name = "使用量")
        private Long quantity;
        @Schema(name = "有效期")
        private String expirationDate;
        @Schema(name = "开始结束时间")
        private String time;
        @Schema(name = "状态")
        private String status;
        @Schema(name = "上架/下架")
        private String isStatus;
        @Schema(name = "创建时间")
        private LocalDateTime createTime;
    }

    @Data
    @Schema(description = "优惠券列表搜索参数")
    class FrontCouponListSearch {
        @Schema(name = "优惠券名称 优惠券id/编号")
        private String keyword;
        @Schema(name = "优惠券状态")
        private String status;
        @Schema(name = "优惠券类型")
        private String type;
    }

    @Data
    @Schema(description = "优惠券详情基本信息参数")
    class FrontCouponDetail {
        @Schema(name = "优惠券id")
        private Long couponId;
        @Schema(name = "优惠券名称")
        private String title;
        @Schema(name = "优惠券类型")
        private String type;
        @Schema(name = "可使用商品")
        private String used;
        @Schema(name = "使用门槛")
        private String threshold;
        @Schema(name = "面值")
        private BigDecimal balance;
        @Schema(name = "状态")
        private String status;
        @Schema(name = "开始结束时间")
        private String time;
        @Schema(name = "有效期")
        private String expirationDate;
        @Schema(name = "总发行量")
        private Long total;
        @Schema(name = "剩余量")
        private Long limit;
        @Schema(name = "已领取")
        private Long quantity;
        @Schema(name = "已使用")
        private Long usedQuantity;
        @Schema(name = "待使用")
        private Long toBeUsedQuantity;
        @Schema(name = "已过期")
        private Long expiredQuantity;
        @Schema(name = "创建时间")
        private LocalDateTime createTime;
    }

    @Data
    @Schema(description = "优惠券详情搜索")
    class FrontCouponDetailSearch {
        @Schema(name = "用户id 订单编号")
        private String keyword;
        @Schema(name = "优惠券id")
        private Long id;
    }
}
