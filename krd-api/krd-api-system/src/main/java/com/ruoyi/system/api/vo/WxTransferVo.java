package com.ruoyi.system.api.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxTransferVo {
    private String mch_appid; // 商户账号appid
    private String mchid;     // 商户号
    private String nonce_str; // 随机字符串，不长于32位。
    private String partner_trade_no; // 商户订单号，需保持唯一性
    private String openid; // 用户在商户appid下的唯一标识
    private String check_name; // 校验用户姓名选项 NO_CHECK：不校验真实姓名（默认值）；FORCE_CHECK：强校验真实姓名
    private Integer amount; // 金额，单位为分
    private String desc; // 企业付款描述信息
    private String spbill_create_ip; // 调用接口的机器IP地址
    private String sign; // 签名
}
