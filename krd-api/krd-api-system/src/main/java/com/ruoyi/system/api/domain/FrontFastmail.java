package com.ruoyi.system.api.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.ruoyi.common.core.annotation.Excel;

/**
 * 快递单管理对象 front_fastmail
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
@TableName("front_fastmail")
@Schema(description = "快递单管理对象 front_fastmail")
public class FrontFastmail implements Serializable
{

    /** $column.columnComment */
    @Schema(name = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 0-寄件快递 1-收件快递 */
    @Excel(name = "0-寄件快递 1-收件快递")
    @Schema(name = "0-寄件快递 1-收件快递")
    private Long fastType;

    /** 快递单号 */
    @Excel(name = "快递单号")
    @Schema(name = "快递单号")
    private String number;

    /** 快递公司名称 */
    @Excel(name = "快递公司名称")
    @Schema(name = "快递公司名称")
    private String company;

    /** 订单商品id */
    @Schema(name = "订单商品id")
    private Long orderGoodsId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @Schema(name = "订单编号 或者拆单的退货单号")
    private String orderNumber;

    /** 用户手机号 */
    @Excel(name = "用户手机号")
    @Schema(name = "用户手机号")
    private String userMobile;

    /** 配送员 */
    @Excel(name = "配送员")
    @Schema(name = "配送员")
    private String delivery;

    /** 配送员手机号 */
    @Excel(name = "配送员手机号")
    @Schema(name = "配送员手机号")
    private String deliveryMobile;

    /** 0-待取件 1-已取件 2-运输中 3-待签收 4-已签收 5-完成 */
    @Excel(name = "0-待取件 1-已取件 2-运输中 3-待签收 4-已签收 5-完成")
    @Schema(name = "0-待取件 1-已取件 2-运输中 3-待签收 4-已签收 5-完成")
    private Long status;

    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(name = "下单时间")
    private LocalDateTime orderTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(name = "完成时间")
    private LocalDateTime finishTime;

    /** 0-未删除 1-删除 */
    @Excel(name = "0-未删除 1-删除")
    @Schema(name = "0-未删除 1-删除")
    private Long isDel;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
}
