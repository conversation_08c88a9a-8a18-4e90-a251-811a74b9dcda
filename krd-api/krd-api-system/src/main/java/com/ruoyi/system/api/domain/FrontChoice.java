package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;

/**
 * 精选内容对象 front_choice
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@EqualsAndHashCode(callSuper = true)
@Setter
@Data
@TableName("front_choice")
public class FrontChoice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    @Excel(name = "简介")
    private String intro;

    @Excel(name = "封面主图")
    private String cover;

    /** 详情 */
    @Excel(name = "详情")
    private String info;

    /** 是否删除 0-false 1-true */
    @Excel(name = "是否删除 0-false 1-true")
    private Long isDel;

    /** 链接 */
    @Excel(name = "链接")
    private String url;

    /** 顺序 */
    @Excel(name = "顺序")
    private Long sort;

}
