package com.ruoyi.system.api.oss;

import com.ruoyi.system.api.oss.service.AliyunOSSService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: suhai
 * @Date: 2025/6/5
 * @Description: 对oss进行加签 和 去签 的工具类
 */
@Component
public class OssUrlCleanerUtil {


    @Autowired
    private AliyunOSSService aliyunOSSService;

    /**
     * 清除URL中的签名参数，只保留基础URL和文件路径。
     * @param urlsStr 带有签名参数的完整URL。
     * @return 清除签名参数后的URL。
     */
    public String cleanUrlsToString(String urlsStr) {
        if (urlsStr == null || urlsStr.trim().isEmpty()) {
            return "";
        }

        // 判断是否包含逗号，决定是多个还是单个URL
        if (urlsStr.contains(",")) {
            String[] urls = urlsStr.split(",");
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < urls.length; i++) {
                String url = urls[i].trim();
                String cleaned = removeQueryString(url);
                sb.append(cleaned);
                if (i < urls.length - 1) {
                    sb.append(","); // 拼接逗号
                }
            }
            return sb.toString();
        } else {
            return removeQueryString(urlsStr.trim());
        }
    }

    /**
     * 辅助方法：清除单个URL中的查询字符串（签名部分）
     */
    private String removeQueryString(String url) {
        int queryIndex = url.indexOf("?");
        if (queryIndex != -1) {
            return url.substring(0, queryIndex);
        }
        return url;
    }


    //图片加签访问
    public String getSignatureUrl(String urlOrUrls) {
        if (urlOrUrls == null || urlOrUrls.trim().isEmpty()) {
            return "";
        }

        // 判断是否为多个URL
        if (urlOrUrls.contains(",")) {
            String[] urls = urlOrUrls.split(",");
            StringBuilder signedUrls = new StringBuilder();
            for (int i = 0; i < urls.length; i++) {
                String originalUrl = urls[i].trim();
                String signedUrl;
                try {
                    signedUrl = aliyunOSSService.getPrivateReadSignatureUrl(originalUrl);
                } catch (MalformedURLException e) {
                    throw new RuntimeException("生成签名URL失败: " + originalUrl, e);
                }
                if (signedUrl != null && !signedUrl.isEmpty()) {
                    signedUrls.append(signedUrl);
                    if (i < urls.length - 1) {
                        signedUrls.append(",");
                    }
                }
            }
            return signedUrls.toString();
        } else {
            // 单个URL处理
            try {
                return aliyunOSSService.getPrivateReadSignatureUrl(urlOrUrls.trim());
            } catch (MalformedURLException e) {
                throw new RuntimeException("生成签名URL失败: " + urlOrUrls, e);
            }
        }
    }



    /**
     * 通用加签方法：支持以下三种输入：
     * 1. 纯图片URL（如："https://example.com/image.jpg"）
     * 2. 多个URL（用逗号分隔，如："url1.jpg,url2.jpg"）
     * 3. 含<img>标签的HTML富文本（如："<p><img src='...'></p>"）
     *
     * @return 加签后的结果（可能是URL字符串、逗号分隔的URLs、或修改后的富文本）
     */
    public String getSignatureEditor(String htmlContent) {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            return "";
        }
        Pattern pattern = Pattern.compile("<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"]", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(htmlContent);
        StringBuilder signedHtml = new StringBuilder();
        int prevEnd = 0;
        while (matcher.find()) {
            int start = matcher.start();
            int end = matcher.end();
            signedHtml.append(htmlContent, prevEnd, start);
            String originalUrl = matcher.group(1);
            String signedUrl = signSingleUrl(originalUrl);
            if (signedUrl != null && !signedUrl.isEmpty()) {
                String originalImgTag = matcher.group(0);
                String signedImgTag = originalImgTag.replaceFirst(originalUrl, signedUrl);
                signedHtml.append(signedImgTag);
            }
            prevEnd = end;
        }

        // 添加剩余的 HTML 内容
        signedHtml.append(htmlContent.substring(prevEnd));

        return signedHtml.toString();
    }

    /**
     * 对单个URL进行加签
     */
    private String signSingleUrl(String url) {
        try {
            return aliyunOSSService.getPrivateReadSignatureUrl(url.trim());
        } catch (MalformedURLException e) {
            throw new RuntimeException("生成签名URL失败: " + url, e);
        }
    }

    /**
     * 提取并加签HTML中所有的<img src="...">链接
     */
    private String signImageUrlsInHtml(String html) {
        Pattern pattern = Pattern.compile("<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"]", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(html);
        StringBuffer signedHtml = new StringBuffer();

        while (matcher.find()) {
            String originalSrc = matcher.group(1);
            String signedUrl = signSingleUrl(originalSrc);
            if (signedUrl != null && !signedUrl.isEmpty()) {
                matcher.appendReplacement(signedHtml, "<img src=\"" + signedUrl + "\"");
            } else {
                // 签名失败可以保留原图或者标记错误
                matcher.appendReplacement(signedHtml, matcher.group(0));
            }
        }
        matcher.appendTail(signedHtml);
        return signedHtml.toString();
    }
}
