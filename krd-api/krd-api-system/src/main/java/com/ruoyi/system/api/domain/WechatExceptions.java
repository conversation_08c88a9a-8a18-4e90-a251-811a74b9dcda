package com.ruoyi.system.api.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName WechatExceptions
 * @TableName wechat_exceptions
 * @Description 微信异常表
 * <AUTHOR>
 * @Date 2025/5/16 上午10:33
 */
@Data
@Schema(description = "微信异常表")
public class WechatExceptions implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(name = "id")
    private Integer id;

    @Schema(name = "错误码")
    private String errcode;

    @Schema(name = "错误信息")
    private String errmsg;

    @Schema(name = "回复数据")
    private String data;

    @Schema(name = "备注")
    private String remark;

    @Schema(name = "创建时间")
    private Date createTime;

    @Schema(name = "更新时间")
    private Date updateTime;


}
