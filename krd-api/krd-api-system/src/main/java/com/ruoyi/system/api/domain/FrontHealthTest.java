package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 健康管理测试对象 front_health_test
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@Setter
@Data
@TableName("front_health_test")
public class FrontHealthTest extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 0-基础信息 1-经期信息 2-健康信息 */
    @Excel(name = "0-基础信息 1-经期信息 2-健康信息")
    private Long type;

    /** 问题全量数据 包括标题，答案，是否必填 */
    @Excel(name = "问题全量数据 包括标题，答案，是否必填")
    private String content;

    /** 0-false 1-true */
    @Excel(name = "0-false 1-true")
    private Long isDel;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setType(Long type)
    {
        this.type = type;
    }

    public Long getType()
    {
        return type;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setIsDel(Long isDel)
    {
        this.isDel = isDel;
    }

    public Long getIsDel()
    {
        return isDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("type", getType())
            .append("content", getContent())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("isDel", getIsDel())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
