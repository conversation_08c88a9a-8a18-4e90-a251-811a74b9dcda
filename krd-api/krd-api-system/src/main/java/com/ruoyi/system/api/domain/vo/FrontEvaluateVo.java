package com.ruoyi.system.api.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @ClassName FrontEvaluateVo
 * @Description 评价管理 交互参数
 * <AUTHOR>
 * @Date 2025/5/20 下午5:02
 */
public interface FrontEvaluateVo {

    @Data
    @Schema(description = "评价详情参数")
    class FrontEvaluateDetail {
        @Schema(name = "待回复差评")
        private Long replyBad;
        @Schema(name = "今日新增差评")
        private Long replyBadNew;
        @Schema(name = "待回复中评")
        private Long replyMiddle;
        @Schema(name = "今日新增中评")
        private Long replyMiddleNew;
        @Schema(name = "可加精评论")
        private Long replyGood;
        @Schema(name = "今日新增加精评论")
        private Long replyGoodNew;
        @Schema(name = "近30天好评率")
        private BigDecimal replyGoodRate;
        @Schema(name = "较前Seven天增加/下降好评率")
        private BigDecimal replyGoodRateSeven;
        @Schema(name = "近30天差评率")
        private BigDecimal replyBadRate;
        @Schema(name = "较前Seven天增加/下降差评率")
        private BigDecimal replyBadRateSeven;
        @Schema(name = "近30天中评率")
        private BigDecimal replyMiddleRate;
        @Schema(name = "较前Seven天增加/下降中评率")
        private BigDecimal replyMiddleRateSeven;
    }

    @Data
    @Schema(description = "评价管理列表参数")
    class FrontEvaluateList {
        @Schema(name = "商品id")
        private Long id;
        @Schema(name = "商品类型")
        private Integer type;
        @Schema(name = "商品分类")
        private String categoryName;
        @Schema(name = "商品封面图")
        private String firstPic;
        @Schema(name = "商品信息")
        private String name;
        @Schema(name = "商品价格")
        private BigDecimal price;
        @Schema(name = "会员价")
        private BigDecimal vipPrice;
        @Schema(name = "商品状态")
        private Integer isStatus;
        @Schema(name = "商品库存")
        private Integer amount;
        @Schema(name = "商品销量")
        private Integer orderCount;
        @Schema(name = "好评")
        private Integer replyGood;
        @Schema(name = "中评")
        private Integer replyMiddle;
        @Schema(name = "差评")
        private Integer replyBad;
    }

    @Data
    @Schema(description = "评价管理搜索参数")
    class FrontEvaluateSearch { 
        @Schema(name = "商品名称")
        private String keyword;
        @Schema(name = "类型")
        private Long type;
        @Schema(name = "分类id")
        private Long categoryId;
    }

    @Data
    @Schema(description = "评价详情搜索参数")
    class FrontEvaluateDetailSearch {
        @Schema(name = "关键字")
        private String keyword;
        @Schema(name = "商品id")
        private Long goodsId;
        @Schema(name = "评价编号")
        private Long evaluateId;
        @Schema(name = "评价状态 0-好评 1-中评 2-差评")
        private Integer status;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Schema(description = "对应商品评价详情")
    class FrontEvaluateDetailInfo extends FrontEvaluateDetail{
        @Schema(name = "商品id")
        private Long goodsId;
        @Schema(name = "商品封面图")
        private String firstPic;
        @Schema(name = "商品名称")
        private String name;
        @Schema(name = "月销量")
        private Integer orderCount;
        @Schema(name = "总销量")
        private Integer totalCount;
        @Schema(name = "综合得分")
        private BigDecimal starRating;
        @Schema(name = "好评")
        private Integer totalReplyGood;
        @Schema(name = "中评")
        private Integer totalReplyMiddle;
        @Schema(name = "差评")
        private Integer totalReplyBad;
    }
}
