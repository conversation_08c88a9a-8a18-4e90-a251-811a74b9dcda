package com.ruoyi.system.api.mapper;

import java.util.List;
import com.ruoyi.system.api.domain.FrontAdvertType;

/**
 * 广告类型Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface FrontAdvertTypeMapper
{
    /**
     * 查询广告类型
     *
     * @param id 广告类型主键
     * @return 广告类型
     */
    public FrontAdvertType selectFrontAdvertTypeById(Long id);

    /**
     * 查询广告类型列表
     *
     * @param frontAdvertType 广告类型
     * @return 广告类型集合
     */
    public List<FrontAdvertType> selectFrontAdvertTypeList(FrontAdvertType frontAdvertType);

    /**
     * 新增广告类型
     *
     * @param frontAdvertType 广告类型
     * @return 结果
     */
    public int insertFrontAdvertType(FrontAdvertType frontAdvertType);

    /**
     * 修改广告类型
     *
     * @param frontAdvertType 广告类型
     * @return 结果
     */
    public int updateFrontAdvertType(FrontAdvertType frontAdvertType);

    /**
     * 删除广告类型
     *
     * @param id 广告类型主键
     * @return 结果
     */
    public int deleteFrontAdvertTypeById(Long id);

    /**
     * 批量删除广告类型
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontAdvertTypeByIds(Long[] ids);
}
