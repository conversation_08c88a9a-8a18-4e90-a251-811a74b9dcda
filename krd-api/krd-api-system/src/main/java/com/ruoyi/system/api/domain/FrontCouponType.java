package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName FrontCouponType
 * @Description 优惠券分类
 * <AUTHOR>
 * @Date 2025/5/30 下午5:00
 */
@Data
@Schema(description = "优惠券类型")
@TableName("front_coupon_type")
public class FrontCouponType implements Serializable {
    /** id */
    @TableId(value = "id",type = IdType.AUTO)
    @Schema(name = "id")
    private Long id;

    /** 类型名称 */
    @Schema(name = "类型名称")
    private String name;

    private LocalDateTime createTime;
}
