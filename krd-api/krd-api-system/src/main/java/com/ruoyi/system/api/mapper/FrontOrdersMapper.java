package com.ruoyi.system.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.vo.FrontOrdersVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName FrontOrdersMapper
 * @Description 订单mapper
 * <AUTHOR>
 * @Date 2025/5/20 下午6:18
 */
@Mapper
public interface FrontOrdersMapper extends BaseMapper<FrontOrders> {
    /**
     * 查询订单总
     *
     * @param id 订单总主键
     * @return 订单总
     */
    public FrontOrders selectFrontOrdersById(Long id);

    /**
     * 查询订单总列表
     *
     * @param vo 订单查询参数
     * @return 订单总集合
     */
    public List<FrontOrders> selectFrontOrdersList(@Param("query") FrontOrdersVo.FrontOrdersSearch vo);

    /**
     * 小程序查询订单总列表
     *
     * @param vo 订单查询参数
     * @return 订单总集合
     */
    public List<FrontOrders> selectUniOrdersList(@Param("query") FrontOrdersVo.UniFrontOrdersSearch vo);

    /**
     * 新增订单总
     *
     * @param frontOrders 订单总
     * @return 结果
     */
    public int insertFrontOrders(FrontOrders frontOrders);

    /**
     * 修改订单总
     *
     * @param frontOrders 订单总
     * @return 结果
     */
    public int updateFrontOrders(FrontOrders frontOrders);

    /**
     * 删除订单总
     *
     * @param id 订单总主键
     * @return 结果
     */
    public int deleteFrontOrdersById(Long id);

    /**
     * 批量删除订单总
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontOrdersByIds(Long[] ids);
}
