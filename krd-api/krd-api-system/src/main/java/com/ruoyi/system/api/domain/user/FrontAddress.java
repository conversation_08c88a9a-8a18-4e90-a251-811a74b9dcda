package com.ruoyi.system.api.domain.user;

import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(name = "用户地址")
public class FrontAddress extends BaseEntity {
    @Schema(title = "id")
    private Long id;
    @Schema(title = "用户id")
    private Long userId;
    @Schema(title = "收货人")
    private String name;
    @Schema(title = "手机号码")
    private String mobile;
    @Schema(title = "所在地区")
    private String area;
    @Schema(title = "详细地址")
    private String address;
    @Schema(title = "地址标签")
    private String tag;
    @Schema(title = "是否默认 0 - 否  1-是")
    private Long isDefault;
    @Schema(title = "是否删除 0-false 1-true")
    private Long isDel;
}
