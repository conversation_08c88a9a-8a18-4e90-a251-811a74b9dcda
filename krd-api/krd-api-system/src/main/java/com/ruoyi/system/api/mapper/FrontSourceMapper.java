package com.ruoyi.system.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.user.FrontSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @ClassName FrontSourceMapper
 * @Description 用户积分明细mapper
 * <AUTHOR>
 * @Date 2025/6/18 上午10:54
 */
@Mapper
public interface FrontSourceMapper extends BaseMapper<FrontSource> {

    /**
     * 查询用户积分
     * @param userId
     * @return
     */
    Integer selectFrontUserPoint(@Param("userId") Long userId);
}
