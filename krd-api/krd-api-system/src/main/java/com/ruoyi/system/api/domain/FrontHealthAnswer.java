package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 健康测试管理用户回答对象 front_health_answer
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Getter
@Setter
@Data
@TableName("front_health_answer")
public class FrontHealthAnswer
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long uid;

    /** 用户答卷json */
    @Excel(name = "用户答卷json")
    private String content;

    /** 1 健康 2个人中心 */
    @Excel(name = "1 健康 2个人中心")
    private Long type;

    /** 用户答卷的id */
    @Excel(name = "用户答卷的id")
    private Long questId;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setUid(Long uid)
    {
        this.uid = uid;
    }

    public Long getUid()
    {
        return uid;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setType(Long type)
    {
        this.type = type;
    }

    public Long getType()
    {
        return type;
    }
    public void setQuestId(Long questId)
    {
        this.questId = questId;
    }

    public Long getQuestId()
    {
        return questId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("uid", getUid())
                .append("content", getContent())
                .append("type", getType())
                .append("questId", getQuestId())
                .toString();
    }
}
