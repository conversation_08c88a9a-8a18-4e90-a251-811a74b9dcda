package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 问卷管理对象 front_question
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Data
@TableName("front_question")
public class FrontQuestion extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 使用积分 */
    @Excel(name = "使用积分")
    private Long point;

    /** 简介 */
    @Excel(name = "简介")
    private String intro;


    /**  */
    private Long id;

    /** 套餐ID */
    @Excel(name = "套餐ID")
    private Long packageId;

    /** 套餐名称 */
    @TableField(exist = false)
    private String packageName;

    /** 问卷名称 */
    @Excel(name = "问卷名称")
    private String questName;

    /** 是否发布 0-false 1-true */
    @Excel(name = "是否发布 0-false 1-true")
    private Long isPublish;

    /** 问题全量数据 多个问题存在一个json中 */
    @Excel(name = "问题全量数据 多个问题存在一个json中")
    private String content;

    /** 创建人 */
    @Excel(name = "创建人")
    private Long createUserId;

    /** 是否删除 0-false 1-true  */
    @Excel(name = "是否删除 0-false 1-true ")
    private Long isDel;

    /** 计对答案 */
    @Excel(name = "计对答案")
    private String answer;

    @TableField(exist = false)
    private Integer isAnswer;

    // 创建人
    @TableField(exist = false)
    private String createUserName;


}
