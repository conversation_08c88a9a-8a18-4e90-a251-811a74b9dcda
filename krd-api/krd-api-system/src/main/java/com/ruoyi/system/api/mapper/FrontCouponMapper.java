package com.ruoyi.system.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.StoreFrontCoupon;
import com.ruoyi.system.api.domain.vo.FrontCouponVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 优惠券Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Mapper
public interface FrontCouponMapper extends BaseMapper<StoreFrontCoupon>
{
    /**
     * 查询优惠券
     * 
     * @param id 优惠券主键
     * @return 优惠券
     */
    public StoreFrontCoupon selectFrontCouponById(Long id);

    /**
     * 查询优惠券详情
     *
     * @param id 优惠券id
     * @return 优惠券集合
     */
    FrontCouponVo.FrontCouponDetail selectFrontCouponDetailById(Long id);

    /**
     * 查询优惠券列表
     *
     * @param vo 查询参数
     * @return 优惠券集合
     */
    public List<FrontCouponVo.FrontCouponList> selectFrontCouponList(@Param("query")FrontCouponVo.FrontCouponListSearch vo);

    /**
     * 新增优惠券
     * 
     * @param frontCoupon 优惠券
     * @return 结果
     */
    public int insertFrontCoupon(StoreFrontCoupon frontCoupon);

    /**
     * 修改优惠券
     * 
     * @param frontCoupon 优惠券
     * @return 结果
     */
    public int updateFrontCoupon(StoreFrontCoupon frontCoupon);

    /**
     * 删除优惠券
     * 
     * @param id 优惠券主键
     * @return 结果
     */
    public int deleteFrontCouponById(Long id);

    /**
     * 批量删除优惠券
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontCouponByIds(Long[] ids);
}
