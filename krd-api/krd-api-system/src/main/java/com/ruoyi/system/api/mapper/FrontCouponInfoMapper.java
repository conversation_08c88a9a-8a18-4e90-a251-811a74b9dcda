package com.ruoyi.system.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontCouponInfo;
import com.ruoyi.system.api.domain.dto.UserCountDTO;
import com.ruoyi.system.api.domain.vo.UserCouponVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *@ClassName FrontCouponInfoMapper
 *@Description  优惠券使用明细 Mapper接口
 *<AUTHOR>
 *@Date 2025/5/22 上午11:27
 */
@Mapper
public interface FrontCouponInfoMapper extends BaseMapper<FrontCouponInfo> {
    List<UserCouponVO> getUserCouponList(Long uid);

    List<UserCountDTO> getUserCouponCountByUids(@Param("list") List<Long> uidList);
}
