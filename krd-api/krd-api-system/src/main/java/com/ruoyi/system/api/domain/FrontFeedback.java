package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * 意见反馈对象 front_feedback
 *
 * <AUTHOR>
 * @date 2025-06-04
 */

@Data
@Schema(description = "意见反馈对象")
@TableName("front_feedback")
public class FrontFeedback implements Serializable {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 意见内容 */
    @Excel(name = "意见内容")
    private String content;

    /** 文档  */
    @Excel(name = "文档 ")
    private String pic;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 手机号 */
    @Excel(name = "手机号")
    private String mobile;

    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private String startTime;
    @TableField(exist = false)
    private String endTime;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 0-false 1-true */
    @Excel(name = "0-false 1-true")
    private Long isDel;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setPic(String pic)
    {
        this.pic = pic;
    }

    public String getPic()
    {
        return pic;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setMobile(String mobile)
    {
        this.mobile = mobile;
    }

    public String getMobile()
    {
        return mobile;
    }
    public void setIsDel(Long isDel)
    {
        this.isDel = isDel;
    }

    public Long getIsDel()
    {
        return isDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("content", getContent())
            .append("pic", getPic())
            .append("createTime", getCreateTime())
            .append("userId", getUserId())
            .append("mobile", getMobile())
            .append("isDel", getIsDel())
            .toString();
    }
}
