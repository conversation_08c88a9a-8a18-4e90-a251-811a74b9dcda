package com.ruoyi.system.api.domain.user;

import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name="用户详情-检测报告")
@Data
public class CheckPackage extends BaseEntity {
    @Schema(title = "id")
    private Long id;
    @Schema(title = "姓名")
    private String name;
    @Schema(title = "身份")
    private String identity;
    @Schema(title = "年龄")
    private Long age;
    @Schema(title = "套餐名")
    private String packageName;
    @Schema(title = "检测人")
    private String checkPerson;
    @Schema(title = "审核人")
    private String auditPerson;
    @Schema(title = "检测时间")
    private String checkTime;
}
