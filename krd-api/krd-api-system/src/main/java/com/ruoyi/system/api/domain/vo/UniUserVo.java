package com.ruoyi.system.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName UniUserVo
 * @Description 小程序用户模块参数
 * <AUTHOR>
 * @Date 2025/6/19 下午4:59
 */
public interface UniUserVo {

    @Data
    @Schema(description = "用户余额明细")
    class UserMoneyDetail {
        @Schema(title = "金额")
        private BigDecimal money;
        @Schema(title = "时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime time;
        @Schema(title = "描述")
        private String desc;
        @Schema(title = "类型")
        private String type;
        @Schema(title = "标题")
        private String title;
    }

    @Data
    @Schema(description = "用户收入支出")
    class UserIncomeExpenditure {
        @Schema(title = "收入")
        private BigDecimal income;
        @Schema(title = "支出")
        private BigDecimal expenditure;
    }

    @Data
    @Schema(description = "用户积分明细")
    class UserIntegralDetail {
        @Schema(title = "积分")
        private BigDecimal integral;
        @Schema(title = "时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime time;
        @Schema(title = "描述")
        private String desc;
        @Schema(title = "类型")
        private String type;
        @Schema(title = "积分来源")
        private String source;
    }

    @Data
    @Schema(description = "用户拥有礼品卡列表")
    class UserGiftCardList {
        @Schema(title = "用户礼品卡ID")
        private Long id;
        @Schema(title = "礼品卡名称")
        private String name;
        @Schema(title = "礼品卡面值")
        private BigDecimal faceValue;
        @Schema(title = "礼品卡余额")
        private BigDecimal balance;
        @Schema(title = "有效期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime expireTime;
    }

    @Data
    @Schema(description = "用户礼品卡使用记录")
    class UserGiftCardUseRecord {
        @Schema(title = "金额")
        private BigDecimal money;
        @Schema(title = "时间")
        @JsonFormat(pattern = "MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime time;
        @Schema(title = "商品")
        private String product;
        @Schema(title = "类型")
        private Integer type;
    }

    @Data
    @Schema(description = "用户可购买礼品卡列表")
    class UserGiftCardBuyList {
        @Schema(title = "礼品卡ID")
        private Long id;
        @Schema(title = "礼品卡名称")
        private String name;
        @Schema(title = "礼品卡面值")
        private BigDecimal faceValue;
        @Schema(title = "有效期")
        private String expirationDate;
    }

    @Data
    @Schema(description = "用户优惠券列表")
    class UserCouponList {
        @Schema(title = "优惠券ID")
        private Long id;
        @Schema(title = "优惠券名称")
        private String name;
        @Schema(title = "优惠券类型")
        private String usedType;
        @Schema(title = "0-无限制 其他为金额（使用门槛）")
        private String threshold;
        @Schema(title = "优惠券面值")
        private BigDecimal balance;
        @Schema(title = "有效期")
        private String expireTime;
    }
}
