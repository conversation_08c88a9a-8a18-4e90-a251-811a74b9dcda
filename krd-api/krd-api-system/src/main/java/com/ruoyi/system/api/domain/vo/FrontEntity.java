package com.ruoyi.system.api.domain.vo;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name="后台用户请求参数")
@Data
public class FrontEntity {
    @Schema(title = "id")
    @Excel(name = "编号")
    private Long id;
    @Schema(title = "用户名")
    @Excel(name = "用户名")
    private String userName;
    @Schema(title = "手机号码")
    @Excel(name = "手机号码")
    private String userMobile;
    @Schema(title = "状态")
    @Excel(name = "状态")
    private boolean status;
    @Schema(title = "标签")
    @Excel(name = "标签")
    private String tagName;
    @Schema(title = "余额（元）")
    @Excel(name = "余额（元）")
    private String balance;
    @Schema(title = "优惠券")
    @Excel(name = "优惠券(张)")
    private String couponCount;
    @Schema(title = "礼品卡")
    @Excel(name = "礼品卡(张)")
    private String giftCount;
    @Schema(title = "积分")
    @Excel(name = "积分")
    private String points;
    @Schema(title = "注册时间")
    @Excel(name = "注册时间")
    private String createTime;

    private String searchText;
}
