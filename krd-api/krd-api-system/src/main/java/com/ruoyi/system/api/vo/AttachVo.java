package com.ruoyi.system.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description: 支付附加对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)

public class AttachVo {

    public AttachVo(String type, Integer userId) {
        this.type = type;
        this.userId = userId;
    }

    @Schema(name = "业务类型， 订单 = order， 充值 = recharge，礼品卡 = gift", required = true)
    private String type = "order";

    @Schema(name = "用户id", required = true)
    private Integer userId;

}
