package com.ruoyi.system.api.domain;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName FrontGategory
 * @TableName front_gategory
 * @Description 商品分类表
 * <AUTHOR>
 * @Date 2025/5/20 上午11:39
 */
@Data
@Schema(description = "商品分类表")
@TableName("front_gategory")
public class FrontGategory implements Serializable {
    @Schema(description="主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @Schema(description="父级id")
    private Long parentId;
    @Schema(description="分类名称")
    private String title;
    @Schema(description="0-不显示 1-显示")
    private Integer isShow;
    @Schema(description="图标链接")
    private String icon;
    @Schema(description="分类说明 100字")
    private String remark;
    @Schema(description="排序")
    private Integer sort;
    @Schema(description="")
    private LocalDateTime createTime;
    @Schema(description="")
    private LocalDateTime updateTime;
    @Schema(description="")
    private String createBy;
    @Schema(description="")
    private String updateBy;

    @Schema(description="商品数量")
    @TableField(exist = false)
    private Integer num;

    @Schema(description="上级分类名称")
    @TableField(exist = false)
    private String parentTitle;

    @Schema(description="是否为二级分类")
    @TableField(exist = false)
    private Boolean isSecond = false;

    @Schema(description="子分类")
    @TableField(exist = false)
    private List<FrontGategory> children = new ArrayList<FrontGategory>();
}
