package com.ruoyi.system.api.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.ruoyi.common.core.annotation.Excel;

/**
 * 优惠券对象 front_coupon
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Data
@TableName("front_coupon")
@Schema(name="优惠券对象")
public class StoreFrontCoupon implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(value = "id",type = IdType.AUTO)
    @Schema(name = "id")
    private Long id;

    /** 优惠券名称 */
    @Excel(name = "优惠券名称")
    @Schema(name = "优惠券名称")
    private String title;

    /** 类型 */
    @Excel(name = "类型")
    @Schema(name = "类型")
    private String type;

    /** 0-无限制 发行量 */
    @Excel(name = "0-无限制")
    @Schema(name = "0-无限制")
    private Long total;

    /** 每人限领 */
    @Excel(name = "每人限领")
    @Schema(name = "每人限领")
    private Long limits;

    /** 面值 */
    @Excel(name = "面值")
    @Schema(name = "面值")
    private BigDecimal balance;

    /** 0-无限制 其他为金额 */
    @Excel(name = "0-无限制 其他为金额")
    @Schema(name = "0-无限制 其他为金额 使用门槛")
    private String threshold;

    /** 有限期 0-无限制 日期范围[数组] 固定天数 [数字] */
    @Excel(name = "有限期 0-无限制 日期范围[数组] 固定天数 [数字]")
    @Schema(name = "有限期 0-无限制 日期范围[数组] 固定天数 [数字]")
    private String expirationDate;

    /** 0-全场使用  
指定分类【对应商品分类id】
指定商品【对应商品ID】
指定用户【对应用户ID】 */
    @Excel(name = "0-全场使用指定分类【对应商品分类id】\n" +
            "指定商品【对应商品ID】\n" +
            "指定用户【对应用户ID】")
    @Schema(name = "0-全场使用  \n" +
            "指定分类【对应商品分类id】\n" +
            "指定商品【对应商品ID】\n" +
            "指定用户【对应用户ID")
    private String used;

    /**
     * 优惠券描述
     */
    @Excel(name = "优惠券描述")
    @Schema(name = "优惠券描述")
    private String remark;

    /** 0-false 1-true */
    @Excel(name = "0-false 1-true")
    @Schema(name = "0-false 1-true")
    private Long isStatus;

    /** 0-false 1-true */
    @Excel(name = "0-false 1-true")
    @Schema(name = "0-false 1-true")
    private Long isDel;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;

    /**
     * 可使用商品优惠券类型（0-全场通用 1-商品分类 2-指定商品 3- 指定用户）
     */
    @Schema(name = "可使用商品优惠券类型（0-全场通用 1-商品分类 2-指定商品 3- 指定用户）")
    private Integer usedType;

}
