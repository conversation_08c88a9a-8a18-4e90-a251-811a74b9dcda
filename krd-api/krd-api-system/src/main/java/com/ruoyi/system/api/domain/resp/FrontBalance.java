package com.ruoyi.system.api.domain.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name="用户余额明细")
public class FrontBalance implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    @Schema(title = "0-充值 1-购物 2-退款")
    private int type;
    @Schema(title = "动账 元 0为+ ，1、2 为-")
    private String moveaccount;
    @Schema(title = "变动后金额")
    private String balance;
    @Schema(title = "订单编号")
    private String orderNumber;
    @Schema(title = "创建时间")
    private String createTime;

}
