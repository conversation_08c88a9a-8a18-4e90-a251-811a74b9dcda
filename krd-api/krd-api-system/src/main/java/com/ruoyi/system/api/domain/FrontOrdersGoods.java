package com.ruoyi.system.api.domain;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName FrontOrdersGoods
 * @TableName front_orders_goods
 * @Description 订单商品表
 * <AUTHOR>
 * @Date 2025/5/20 下午6:19
 */
@Data
@Schema(description = "订单商品表")
@TableName("front_orders_goods")
public class FrontOrdersGoods implements Serializable {
    @Schema(description="id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @Schema(description="0-检测套餐 1-商品")
    private Integer type;
    @Schema(description="订单ID")
    private Long orderId;
    @Schema(description="商品ID")
    private Long goodsId;
    @Schema(description="规格ID")
    private Long goodsSpecId;
    @Schema(description="购买数量")
    private Integer count;
    @Schema(description="商品金额")
    private BigDecimal price;
    @Schema(description="抵扣金额")
    private BigDecimal discount;
    @Schema(description="应付金额")
    private BigDecimal meetPrice;
    @Schema(description="实付金额")
    private BigDecimal payPrice;
    @Schema(description="处理备注")
    private String handleRemark;
    @Schema(description="售后退款原因")
    private String afterReason;
    @Schema(description="售后状态 0 正常 1 售后中 2已售后")
    private Integer afterStatus;
    @Schema(description="退款说明")
    private String afterDesc;
    @Schema(description="退款金额")
    private BigDecimal afterPrice;
    @Schema(description="申请时间")
    private LocalDateTime applyTime;
    @Schema(description="处理时间")
    private LocalDateTime handleTime;
    @Schema(description="实际退款金额")
    private BigDecimal ackAfterPrice;
    @Schema(description="售后地址")
    private String afterAddress;
    @Schema(description="")
    private String createBy;
    @Schema(description="")
    private String updateBy;
    @Schema(description="")
    private LocalDateTime createTime;
    @Schema(description="")
    private LocalDateTime updateTime;

    /* 新加字段 */
    @Schema(description = "评论状态 默认0 未评论")
    private Boolean commentStatus;

    @Schema(description = "退款凭证")
    private String refundImg;

    @Schema(description = "退款类型 1-仅退款 2-退货退款")
    private Integer refundType;
}
