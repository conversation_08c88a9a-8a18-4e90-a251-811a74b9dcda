package com.ruoyi.system.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontEvaluate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.api.domain.vo.FrontEvaluateVo;

import java.util.List;

/**
 * @ClassName FrontEvaluateMapper
 * @Description 评价管理mapper
 * <AUTHOR>
 * @Date 2025/5/20 下午4:49
 */
@Mapper
public interface FrontEvaluateMapper extends BaseMapper<FrontEvaluate> {

    /**
     * 查询评价列表
     * @param query
     * @return
     */
    List<FrontEvaluateVo.FrontEvaluateList> selectEvaluateList(@Param("query") FrontEvaluateVo.FrontEvaluateSearch query);

    /**
     * 查询对应商品评价详情
     * @param query
     * @return
     */
    FrontEvaluateVo.FrontEvaluateDetailInfo selectGoodsEvaluateDetail(@Param("query") FrontEvaluateVo.FrontEvaluateDetailSearch query);
}
