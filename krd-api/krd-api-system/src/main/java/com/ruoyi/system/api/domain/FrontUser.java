package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.system.api.domain.user.CheckPackage;
import com.ruoyi.system.api.domain.user.FrontAddress;
import com.ruoyi.system.api.domain.user.StatisticsInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 front_user
 *
 * @date 2025-05-09
 */
@Schema(name = "用户实体")
@Data
public class FrontUser implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** $column.columnComment */
    @Schema(title = "id")
    private Long id;

    /** 用户头像 */
    @Excel(name = "用户头像")
    @Schema(title = "用户头像")
    private String userIcon;

    /** 用户名 */
    @Excel(name = "用户名")
    @Schema(title = "用户名")
    private String userName;

    /** 城市 */
    @Excel(name = "城市")
    @Schema(title = "城市")
    private String userCity;

    /** 用户标签，存id */
    @Excel(name = "用户标签，存id")
    @Schema(title = "用户标签")
    private String userTag;

    /** 手机号码 */
    @Excel(name = "手机号码")
    @Schema(title = "手机号码")
    private String userMobile;

    /** 用户生日 */
    @Schema(title = "用户生日(存时间戳)")
    private String userBirthday;

    /** 年龄 */
    @Excel(name = "年龄")
    @Schema(title = "年龄")
    private Long userAge;

    /** 0-女 1-男 */
    @Excel(name = "0-女 1-男")
    @Schema(title = "0-女 1-男")
    private Long userSex;

    /** 体重 */
    @Excel(name = "体重")
    @Schema(title = "体重")
    private Long userHeight;

    /** 体重 */
    @Excel(name = "身高")
    @Schema(title = "身高")
    private Long userWeight;

    /** 状态 0-正常 1-true */
    @Excel(name = "状态 (是否冻结)  0-false 1-true")
    @Schema(title = "状态 (是否冻结)  0-false 1-true")
    private Boolean status;

    /** 用户IP */
    @Excel(name = "用户IP")
    @Schema(title = "用户IP")
    private String userIp;

    /** 是否进行问卷调查 0-false 1-true */
    @Excel(name = "是否进行问卷调查 0-false 1-true")
    @Schema(title = "是否进行问卷调查 0-false 1-true")
    private Long isDoQuest;

    /** 周期长度 */
    @Excel(name = "周期长度")
    @Schema(title = "周期长度")
    private Long periodCycle;

    /** 经期长度 */
    @Excel(name = "经期长度")
    @Schema(title = "经期长度")
    private Long periodLength;

    /** 最近一次月经开始日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "最近一次月经开始日")
    @Excel(name = "最近一次月经开始日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nearPeriodDate;

    //是否答过健康档案的问卷
    @Excel(name = "是否答过健康档案的问卷")
    private Long isAnswerHealth;

    //健康档案月经是否已经走了
    @Excel(name = "健康档案月经是否已经走了")
    private int isGoPeriod;

    @TableField(exist = false)
    private String token;

    // 统计信息
    @TableField(exist = false)
    private StatisticsInfo statisticsInfo;
    // 收货地址
    @TableField(exist = false)
    private List<FrontAddress> frontAddress;
    // 购买记录。区分检测套餐，商城商品

    // 检测报告
    @TableField(exist = false)
    private List<CheckPackage> checkPackage;

    @TableField(exist = false)
    private String healthType; // 是否是健康类型更改的 1 是 -不是

    @Schema(title = "用户余额")
    private BigDecimal nowMoney;

    @Schema(title = "用户积分")
    private BigDecimal integral;

    @Schema(title = "优惠券")
    @TableField(exist = false)
    private Integer coupon;
    @Schema(title = "礼品卡")
    @TableField(exist = false)
    private BigDecimal giftCard;

    @Schema(title = "用户下单数量")
    private Integer payCount;
}
