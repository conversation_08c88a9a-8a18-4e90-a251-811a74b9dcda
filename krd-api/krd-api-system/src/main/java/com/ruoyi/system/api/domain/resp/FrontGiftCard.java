package com.ruoyi.system.api.domain.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name="用户礼品卡")
public class FrontGiftCard implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    @Schema(title = "0-购买 1-好友赠送 2-购物抵扣")
    private int type;
    @Schema(title = "0-待使用 1-已使用 2-已过期")
    private String status;
    @Schema(title = "使用时间")
    private String useTime;
    @Schema(title = "订单编号")
    private String orderNumber;
    @Schema(title = "创建时间")
    private String createTime;
    @Schema(title = "礼品卡金额")
    private String balance;
    @Schema(title = "购买张数")
    private String count;
}
