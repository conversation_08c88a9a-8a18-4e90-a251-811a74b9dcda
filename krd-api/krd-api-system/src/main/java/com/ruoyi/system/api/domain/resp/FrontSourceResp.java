package com.ruoyi.system.api.domain.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name="用户积分明细")
public class FrontSourceResp implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    @Schema(title = "积分来源 0-签到 1-答问卷 2-注册 3-邀请好友 4-分享 5-购物抵扣 6-购买")
    private int source;
    @Schema(title = "类型 0-奖励 1-扣除")
    private String type;
    @Schema(title = "变动后金额")
    private String balance;
    @Schema(title = "订单编号")
    private String orderNumber;
    @Schema(title = "创建时间")
    private String createTime;
}
