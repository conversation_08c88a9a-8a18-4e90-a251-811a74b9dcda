package com.ruoyi.system.api.mapper;

import com.ruoyi.system.api.domain.FrontVideo;

import java.util.List;

/**
 * 采集视频Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface FrontVideoMapper
{
    /**
     * 查询采集视频
     *
     * @param id 采集视频主键
     * @return 采集视频
     */
    public FrontVideo selectFrontVideoById(Long id);

    /**
     * 查询采集视频列表
     *
     * @param frontVideo 采集视频
     * @return 采集视频集合
     */
    public List<FrontVideo> selectFrontVideoList(FrontVideo frontVideo);

    /**
     * 新增采集视频
     *
     * @param frontVideo 采集视频
     * @return 结果
     */
    public int insertFrontVideo(FrontVideo frontVideo);

    /**
     * 修改采集视频
     *
     * @param frontVideo 采集视频
     * @return 结果
     */
    public int updateFrontVideo(FrontVideo frontVideo);

    /**
     * 删除采集视频
     *
     * @param id 采集视频主键
     * @return 结果
     */
    public int deleteFrontVideoById(Long id);

    /**
     * 批量删除采集视频
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontVideoByIds(Long[] ids);
}
