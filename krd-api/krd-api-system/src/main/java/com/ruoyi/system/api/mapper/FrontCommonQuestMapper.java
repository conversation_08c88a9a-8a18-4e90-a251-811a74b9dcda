package com.ruoyi.system.api.mapper;

import com.ruoyi.system.api.domain.FrontCommonQuest;

import java.util.List;

/**
 * 常见问题Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface FrontCommonQuestMapper
{
    /**
     * 查询常见问题
     *
     * @param id 常见问题主键
     * @return 常见问题
     */
    public FrontCommonQuest selectFrontCommonQuestById(Long id);

    /**
     * 查询常见问题列表
     *
     * @param frontCommonQuest 常见问题
     * @return 常见问题集合
     */
    public List<FrontCommonQuest> selectFrontCommonQuestList(FrontCommonQuest frontCommonQuest);

    /**
     * 小程序查询常见问题列表
     *
     * @param frontCommonQuest 常见问题
     * @return 常见问题集合
     */
    public List<FrontCommonQuest> selectUniFrontCommonQuestList(FrontCommonQuest frontCommonQuest);

    /**
     * 新增常见问题
     *
     * @param frontCommonQuest 常见问题
     * @return 结果
     */
    public int insertFrontCommonQuest(FrontCommonQuest frontCommonQuest);

    /**
     * 修改常见问题
     *
     * @param frontCommonQuest 常见问题
     * @return 结果
     */
    public int updateFrontCommonQuest(FrontCommonQuest frontCommonQuest);

    /**
     * 删除常见问题
     *
     * @param id 常见问题主键
     * @return 结果
     */
    public int deleteFrontCommonQuestById(Long id);

    /**
     * 批量删除常见问题
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontCommonQuestByIds(Long[] ids);
}
