package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户金额记录对象 front_balance_info
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Getter
@Setter
@Data
@TableName("front_balance_info")
public class FrontBalanceInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /** 0-充值 1-购物 2-退款 */
    @Excel(name = "0-充值 1-购物 2-退款")
    private Long type;

    /** 动账 元 0为+ ，1、2 为- */
    @Excel(name = "动账 元 0为+ ，1、2 为-")
    private String moveaccount;

    /** 变动后金额 元 */
    @Excel(name = "变动后金额 元")
    private BigDecimal balance;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNumber;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    private LocalDateTime createTime;

}
