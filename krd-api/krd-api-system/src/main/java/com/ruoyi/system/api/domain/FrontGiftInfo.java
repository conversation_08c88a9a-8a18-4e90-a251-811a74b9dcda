package com.ruoyi.system.api.domain;

import javax.validation.constraints.Size;

import java.io.Serializable;

import java.time.LocalDateTime;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @ClassName FrontGiftInfo
 * @TableName front_gift_info
 * @Description 礼品卡明细
 * <AUTHOR>
 * @Date 2025/5/20 上午10:12
 */
@Data
@Schema(name = "礼品卡明细")
@TableName("front_gift_info")
public class FrontGiftInfo implements Serializable {

    /**
     * id
     */
    @Schema(name = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 领取用户
     */
    @Schema(name = "领取用户")
    private Long userId;
    /**
     * 创建时间
     */
    @Schema(name = "创建时间")
    private LocalDateTime createTime;
    /**
     * 0-待使用 1-已使用 2-已过期
     */
    @Size(max = 255, message = "编码长度不能超过255")
    @Schema(name = "0-待使用 1-已使用 2-已过期 3-使用中")
    @Length(max = 255, message = "编码长度不能超过255")
    private String status;
    /**
     * 使用/过期时间
     */
    @Schema(name = "使用/过期时间")
    private LocalDateTime useTime;
    /**
     * 礼品卡金额
     */
    @Schema(name = "礼品卡金额")
    private BigDecimal balance;
    /**
     * 订单编号
     */
    @Size(max = 255, message = "编码长度不能超过255")
    @Schema(name = "订单编号")
    @Length(max = 255, message = "编码长度不能超过255")
    private String orderNumber;
    /**
     *
     */
    @Schema(name = "")
    private LocalDateTime updateTime;
    /**
     *
     */
    @Size(max = 255, message = "编码长度不能超过255")
    @Schema(name = "")
    @Length(max = 255, message = "编码长度不能超过255")
    private String createBy;
    /**
     *
     */
    @Size(max = 255, message = "编码长度不能超过255")
    @Schema(name = "")
    @Length(max = 255, message = "编码长度不能超过255")
    private String updateBy;
    /**
     * 礼品卡id
     */
    @Schema(name = "礼品卡id")
    private Long giftId;
    /**
     * 0-购买 1-好友赠送 2-购物抵扣
     */
    @Schema(name = "0-购买 1-好友赠送 2-购物抵扣")
    private Integer type;
    /**
     * 礼品卡排序
     */
    @Schema(name = "礼品卡排序(用于区分使用那张礼品卡)")
    private Integer sort;
}