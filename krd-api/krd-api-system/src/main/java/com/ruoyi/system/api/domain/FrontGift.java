package com.ruoyi.system.api.domain;


import java.io.Serializable;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName FrontGift
 * @TableName front_gift
 * @Description 礼品卡
 * <AUTHOR>
 * @Date 2025/5/19 下午4:04
 */
@Data
@Schema(name = "礼品卡")
@TableName("front_gift")
public class FrontGift implements Serializable {

    /**
    * id
    */
    @Schema(name = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 礼品卡名称
    */
    @Schema(name = "礼品卡名称")
    private String name;
    /**
    * 总发行量
    */
    @Schema(name = "总发行量")
    private Integer total;
    /**
    * 面值
    */
    @Schema(name = "面值")
    private BigDecimal balance;
    /**
    * 有限期 0-无限制 固定天数 [数字]
    */
    @Schema(name = "有限期 0-无限制 固定天数 [数字]")
    private String expirationDate;
    /**
    * 礼品卡描述
    */
    @Schema(name = "礼品卡描述")
    private String remark;
    /**
    * 0-未删除 1-删除
    */
    @Schema(name = "0-未删除 1-删除")
    private Integer isDel;
    /**
    * 
    */
    @Schema(name = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
    * 
    */
    @Schema(name = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
    * 
    */
    @Schema(name = "")
    private String createBy;
    /**
    * 
    */
    @Schema(name = "")
    private String updateBy;

    @Schema(name = "固定天数")
    @TableField(exist = false)
    private Integer validDays;

}
