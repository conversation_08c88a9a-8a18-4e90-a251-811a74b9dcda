package com.ruoyi.system.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontAdvert;
import org.apache.ibatis.annotations.Mapper;

/**
 * 广告管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface FrontAdvertMapper extends BaseMapper<FrontAdvert>
{
    /**
     * 查询广告管理
     *
     * @param id 广告管理主键
     * @return 广告管理
     */
    public FrontAdvert selectFrontAdvertById(Long id);

    /**
     * 查询广告管理列表
     *
     * @param frontAdvert 广告管理
     * @return 广告管理集合
     */
    public List<FrontAdvert> selectFrontAdvertList(FrontAdvert frontAdvert);

    /**
     * 新增广告管理
     *
     * @param frontAdvert 广告管理
     * @return 结果
     */
    public int insertFrontAdvert(FrontAdvert frontAdvert);

    /**
     * 修改广告管理
     *
     * @param frontAdvert 广告管理
     * @return 结果
     */
    public int updateFrontAdvert(FrontAdvert frontAdvert);

    /**
     * 删除广告管理
     *
     * @param id 广告管理主键
     * @return 结果
     */
    public int deleteFrontAdvertById(Long id);

    /**
     * 批量删除广告管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontAdvertByIds(Long[] ids);
}
