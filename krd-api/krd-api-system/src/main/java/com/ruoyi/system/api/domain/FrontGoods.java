package com.ruoyi.system.api.domain;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName FrontGoods
 * @TableName front_goods
 * @Description 商品表
 * <AUTHOR>
 * @Date 2025/5/20 下午2:24
 */
@Data
@Schema(description = "商品表")
@TableName("front_goods")
public class FrontGoods implements Serializable {
    @Schema(description="id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @Schema(description="商品类型 0-检测套餐 1-商城商品")
    private Integer goodsType;
    @Schema(description="检测套餐才有的")
    private String packageId;
    @Schema(description="商品分类ID")
    private Long gategoryId;
    @Schema(description="套餐名称")
    private String name;
    @Schema(description="副标题")
    private String subtitle;
    @Schema(description="原价")
    private BigDecimal price;
    @Schema(description="库存")
    private Integer amount;
    @Schema(description="商品货号 只商品分类可用")
    private String goodsNo;
    @Schema(description="上下架 0-下架 1-上架")
    private Integer isStatus;
    @Schema(description="0-不推荐 1-首页大图展示 2-首页小图展示 3-商城大图")
    private String featuredFirst;
    @Schema(description="首页图片，featured_first  != 0 暂时该图片")
    private String indexImage;
    @Schema(description="套餐首图 / 商品首图 地址")
    private String firstPic;
    @Schema(description="banner")
    private String banner;
    @Schema(description="套餐单位 / 商品单位")
    private String unit;
    @Schema(description="搜索关键字")
    private String keywords;
    @Schema(description="详细介绍")
    private String details;
    @Schema(description="规格ID")
    private Long specId;
    @Schema(description="报告解读")
    private String reportInfo;
    @Schema(description="")
    private LocalDateTime createTime;
    @Schema(description="")
    private LocalDateTime updateTime;
    @Schema(description="")
    private String createBy;
    @Schema(description="")
    private String updateBy;
    @Schema(description="0-未删除  1-删除")
    private Integer isDel;
    @Schema(name = "排序默认(越大越靠前)")
    private Integer sort;

    /*补充字段*/
    @Schema(name = "销量")
    @TableField(exist = false)
    private Long sales;
    @Schema(name = "分类名称")
    @TableField(exist = false)
    private String categoryName;
    @Schema(name = "商品套餐规格")
    @TableField(exist = false)
    private List<FrontGoodsSpec> frontGoodsSpecList;

    @Schema(name = "会员价")
    @TableField(exist = false)
    private BigDecimal vipPrice;

}
