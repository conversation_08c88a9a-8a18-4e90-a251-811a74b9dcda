package com.ruoyi.system.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontSysDoc;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 文档管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Mapper
public interface FrontSysDocMapper extends BaseMapper<FrontSysDoc>
{
    /**
     * 查询文档管理
     *
     * @param id 文档管理主键
     * @return 文档管理
     */
    public FrontSysDoc selectFrontSysDocById(Long id);

    /**
     * 查询文档管理列表
     *
     * @param frontSysDoc 文档管理
     * @return 文档管理集合
     */
    public List<FrontSysDoc> selectFrontSysDocList(FrontSysDoc frontSysDoc);

    /**
     * 新增文档管理
     *
     * @param frontSysDoc 文档管理
     * @return 结果
     */
    public int insertFrontSysDoc(FrontSysDoc frontSysDoc);

    /**
     * 修改文档管理
     *
     * @param frontSysDoc 文档管理
     * @return 结果
     */
    public int updateFrontSysDoc(FrontSysDoc frontSysDoc);

    /**
     * 删除文档管理
     *
     * @param id 文档管理主键
     * @return 结果
     */
    public int deleteFrontSysDocById(Long id);

    /**
     * 批量删除文档管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontSysDocByIds(Long[] ids);
}
