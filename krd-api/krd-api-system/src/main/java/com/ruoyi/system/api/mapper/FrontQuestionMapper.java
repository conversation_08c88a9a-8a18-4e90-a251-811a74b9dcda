package com.ruoyi.system.api.mapper;


import com.ruoyi.system.api.domain.FrontQuestion;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 问卷管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Mapper
public interface FrontQuestionMapper
{
    /**
     * 查询问卷管理
     *
     * @param id 问卷管理主键
     * @return 问卷管理
     */
    public FrontQuestion selectFrontQuestionById(Long id);

    /**
     * 查询问卷管理列表
     *
     * @param frontQuestion 问卷管理
     * @return 问卷管理集合
     */
    public List<FrontQuestion> selectFrontQuestionList(FrontQuestion frontQuestion);


    /**
     * 查询问卷管理列表 uni
     *
     * @return 问卷管理集合
     */
    public List<FrontQuestion> selectUniQuestionList( );

    /**
     * 新增问卷管理
     *
     * @param frontQuestion 问卷管理
     * @return 结果
     */
    public int insertFrontQuestion(FrontQuestion frontQuestion);

    /**
     * 修改问卷管理
     *
     * @param frontQuestion 问卷管理
     * @return 结果
     */
    public int updateFrontQuestion(FrontQuestion frontQuestion);

    /**
     * 删除问卷管理
     *
     * @param id 问卷管理主键
     * @return 结果
     */
    public int deleteFrontQuestionById(Long id);

    /**
     * 批量删除问卷管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontQuestionByIds(Long[] ids);
}
