package com.ruoyi.system.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontGoodsSpec;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商品规格Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Mapper
public interface FrontGoodsSpecMapper extends BaseMapper<FrontGoodsSpec>
{
    /**
     * 查询商品规格
     * 
     * @param id 商品规格主键
     * @return 商品规格
     */
    public FrontGoodsSpec selectFrontGoodsSpecById(Long id);

    /**
     * 查询商品规格列表
     * 
     * @param frontGoodsSpec 商品规格
     * @return 商品规格集合
     */
    public List<FrontGoodsSpec> selectFrontGoodsSpecList(FrontGoodsSpec frontGoodsSpec);

    /**
     * 新增商品规格
     * 
     * @param frontGoodsSpec 商品规格
     * @return 结果
     */
    public int insertFrontGoodsSpec(FrontGoodsSpec frontGoodsSpec);

    /**
     * 修改商品规格
     * 
     * @param frontGoodsSpec 商品规格
     * @return 结果
     */
    public int updateFrontGoodsSpec(FrontGoodsSpec frontGoodsSpec);

    /**
     * 删除商品规格
     * 
     * @param id 商品规格主键
     * @return 结果
     */
    public int deleteFrontGoodsSpecById(Long id);

    /**
     * 批量删除商品规格
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontGoodsSpecByIds(Long[] ids);
}
