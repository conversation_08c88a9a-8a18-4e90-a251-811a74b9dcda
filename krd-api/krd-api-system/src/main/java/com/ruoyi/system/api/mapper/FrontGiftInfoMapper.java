package com.ruoyi.system.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontGiftInfo;
import com.ruoyi.system.api.domain.dto.UserCountDTO;
import com.ruoyi.system.api.domain.vo.UniUserVo;
import com.ruoyi.system.api.vo.UserGiftVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName FrontGiftInfoMapper
 * @Description 礼品卡明细mapper
 * <AUTHOR>
 * @Date 2025/5/20 上午10:13
 */
@Mapper
public interface FrontGiftInfoMapper extends BaseMapper<FrontGiftInfo> {
    List<UserGiftVO> getUserGiftList(Long uid);

    List<UserCountDTO> getUserGiftCountByUids(@Param("list") List<Long> uidList);

    /**
     * 获取用户礼品卡列表
     * @param uid 用户ID
     * @param type 1可用 2不可用
     * @return
     */
    List<UniUserVo.UserGiftCardList> listGiftCardList(@Param("uid") Long uid, @Param("type") Integer type);
}
