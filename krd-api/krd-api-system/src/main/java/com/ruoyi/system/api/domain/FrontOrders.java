package com.ruoyi.system.api.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.system.api.domain.vo.FrontOrdersVo;
import com.ruoyi.system.api.domain.vo.GiftDeduVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName FrontOrders
 * @TableName front_orders
 * @Description 订单表
 * <AUTHOR>
 * @Date 2025/5/20 下午6:19
 */
@Data
@Schema(description = "订单表")
@TableName("front_orders")
public class FrontOrders implements Serializable {
    @Schema(description="id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @Schema(description="订单编号")
    @Excel(name = "订单编号")
    private String orderNumber;
    @Schema(description="用户ID")
    @Excel(name = "用户ID")
    private Long userId;
    @Schema(description="总金额")
    @Excel(name = "金额")
    private BigDecimal totalPrice;
    @Schema(description="使用积分")
    @Excel(name = "使用积分")
    private BigDecimal usePoint;
    @Schema(description="使用余额")
    @Excel(name = "使用余额")
    private BigDecimal useBalance;
    @Schema(description="抵扣类型")
    @Excel(name = "抵扣类型")
    private String deductionType;
    @Schema(description="抵扣金额总金额")
    @Excel(name = "抵扣金额总金额")
    private BigDecimal deductionPrice;
    @Schema(description="支付金额")
    @Excel(name = "支付金额")
    private BigDecimal payPrice;
    @Schema(description="发货单流水号")
    private String pushGoodsNo;
    @Schema(description="下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单时间")
    private LocalDateTime createTime;
    @Schema(description="完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间")
    private LocalDateTime finishTime;
    @Schema(description="是否售后 0-false 1-true")
    private Integer isAfter;
    @Schema(description="0-待支付 1-待发货 2-待收货 3-已完成 4-待评价 5-已取消 6-售后订单 7-退款")
    @Excel(name = "订单状态",readConverterExp = "0=待支付,1=待发货,2=待收货,3-已完成,4-待评价,5=已取消,6=售后订单,7=退款")
    private Integer status;
    @Schema(description="用户备注")
    private String userRemark;
    @Schema(description="平台备注")
    private String platRemark;
    @Schema(description="是否关闭订单 0-false 1-true")
    private Integer closeOrder;
    @Schema(description="")
    private LocalDateTime updateTime;
    @Schema(description="")
    private String createBy;
    @Schema(description="")
    private String updateBy;

    //---------以下是新增的字段-----------
    @Schema(description = "是否删除")
    private Boolean isDel;

    @Schema(description = "是否支付")
    private Boolean paid;

    @Schema(description = "支付方式")
    private String payType;

    @Schema(description = "商户系统内部的订单号,32个字符内、可包含字母, 其他说明见商户订单号")
    private String outTradeNo;

    @Schema(name = "地址id")
    private Long addressId;

    @Schema(name = "收货人")
    private String receiveName;

    @Schema(name = "收货人手机号")
    private String receivePhone;

    @Schema(name = "收货人地址——省市区")
    private String receiveAddress;

    @Schema(name = "收货人地址详情")
    private String receiveAddressDetail;

    @Schema(name = "积分抵扣")
    private BigDecimal pointsDeduction;

    @Schema(name = "礼品卡抵扣")
    private BigDecimal giftDeduction;

    @Schema(name = "礼品卡抵扣列表")
    private List<GiftDeduVo> giftList;

    @Schema(name = "优惠券抵扣")
    private BigDecimal couponDeduction;

    @Schema(name = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    /* 新增不存在数据库字段 */
    @Schema(description = "商品名称")
    @TableField(exist = false)
    @Excel(name = "商品名称")
    private String goodsName;

    @Schema(description = "订单商品信息")
    @TableField(exist = false)
    private List<FrontOrdersVo.FrontOrdersGoodsInfo> orderGoods;

    @Schema(description = "订单发票信息")
    @TableField(exist = false)
    private Boolean invoice = Boolean.FALSE;

    @Schema(description = "是否开具发票")
    @TableField(exist = false)
    private Integer invoiceApply;
}
