package com.ruoyi.system.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontHealthAnswer;

import java.util.List;

/**
 * 健康测试管理用户回答Mapper接口
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface FrontHealthAnswerMapper extends BaseMapper<FrontHealthAnswer>
{
    /**
     * 查询健康测试管理用户回答
     *
     * @param id 健康测试管理用户回答主键
     * @return 健康测试管理用户回答
     */
    public FrontHealthAnswer selectFrontHealthAnswerById(Long id);

    /**
     * 查询健康测试管理用户回答列表
     *
     * @param frontHealthAnswer 健康测试管理用户回答
     * @return 健康测试管理用户回答集合
     */
    public List<FrontHealthAnswer> selectFrontHealthAnswerList(FrontHealthAnswer frontHealthAnswer);

    /**
     * 新增健康测试管理用户回答
     *
     * @param frontHealthAnswer 健康测试管理用户回答
     * @return 结果
     */
    public int insertFrontHealthAnswer(FrontHealthAnswer frontHealthAnswer);

    /**
     * 修改健康测试管理用户回答
     *
     * @param frontHealthAnswer 健康测试管理用户回答
     * @return 结果
     */
    public int updateFrontHealthAnswer(FrontHealthAnswer frontHealthAnswer);

    /**
     * 删除健康测试管理用户回答
     *
     * @param id 健康测试管理用户回答主键
     * @return 结果
     */
    public int deleteFrontHealthAnswerById(Long id);

    /**
     * 批量删除健康测试管理用户回答
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontHealthAnswerByIds(Long[] ids);
}
