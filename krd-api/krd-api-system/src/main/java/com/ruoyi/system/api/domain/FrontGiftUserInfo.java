package com.ruoyi.system.api.domain;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户礼品卡使用信息
 */
@Data
@TableName("front_gift_user_info")
@Schema(description="用户礼品卡使用信息")
public class FrontGiftUserInfo implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="id")
    private Long id;
    @Schema(description="用户礼品卡id")
    private Integer giftInfoId;
    @Schema(description="用户下单订单号")
    private String orderNo;
    @Schema(description="0 - 支出 1 获得")
    private Integer type;
    @Schema(description="使用金额")
    private BigDecimal balance;
    @Schema(description="创建时间")
    private LocalDateTime createTime;
}
