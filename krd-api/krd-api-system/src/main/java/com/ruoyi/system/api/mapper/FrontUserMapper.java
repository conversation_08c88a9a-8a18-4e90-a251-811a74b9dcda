package com.ruoyi.system.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.resp.FrontUserMoneyResp;
import com.ruoyi.system.api.domain.user.CheckPackage;
import com.ruoyi.system.api.domain.user.FrontAddress;
import com.ruoyi.system.api.domain.user.StatisticsInfo;
import com.ruoyi.system.api.domain.vo.FrontEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户Mapper接口
 *
 * @date 2025-05-09
 */
@Mapper
public interface FrontUserMapper extends BaseMapper<FrontUser>
{
    /**
     * 查询用户
     *
     * @param id 用户主键
     * @return 用户
     */
    public FrontUser selectFrontUserById(Long id);

    /**
     * 查询用户列表
     *
     * @param frontUser 用户
     * @return 用户集合
     */
    public List<FrontEntity> selectFrontUserList(FrontEntity frontUser);

    /**
     * 新增用户
     *
     * @param frontUser 用户
     * @return 结果
     */
    public int insertFrontUser(FrontUser frontUser);

    /**
     * 修改用户
     *
     * @param frontUser 用户
     * @return 结果
     */
    public int updateFrontUser(FrontUser frontUser);

    /**
     * 删除用户
     *
     * @param id 用户主键
     * @return 结果
     */
    public int deleteFrontUserById(Long id);

    /**
     * 批量删除用户
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontUserByIds(Long[] ids);

    List<FrontUserMoneyResp> listMoneyPackageInfo(@Param("type") String type,
                                                  @Param("userId") Long userId);



    /**
     * 统计信息
     * @param userId
     * @return
     */
    StatisticsInfo selectStatisticsInfoByUserId(Long userId);

    /**
     *  收货地址
     * @param userId 用户ID
     * @return
     */
    List<FrontAddress> selectFrontAddressByUserId(Long userId);

    /**
     * 检测报告
     * @param id
     * @return
     */
    List<CheckPackage> selectCheckPackage(Long id);

    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return
     */
    FrontUser selectPhoneOne(@Param("phone") String phone);

    //查看用户交易数量匹配 数
    List<Integer> selectUserDealCount(@Param("start")String start,@Param("end")String end, @Param("amount")String amount);

    //查看用户交易金额匹配 数
    List<Integer> selectUserDealAmount(@Param("start")String start,@Param("end")String end, @Param("amount")String amount);
}
