package com.ruoyi.system.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontFastmail;

/**
 * 快递单管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
public interface FrontFastmailMapper extends BaseMapper<FrontFastmail>
{
    /**
     * 查询快递单管理
     * 
     * @param id 快递单管理主键
     * @return 快递单管理
     */
    public FrontFastmail selectFrontFastmailById(Long id);

    /**
     * 根据订单编号查询快递单管理
     * @param num
     * @return
     */
    public FrontFastmail selectFrontFastmailByOrderNum(String num);

    /**
     * 查询快递单管理列表
     * 
     * @param frontFastmail 快递单管理
     * @return 快递单管理集合
     */
    public List<FrontFastmail> selectFrontFastmailList(FrontFastmail frontFastmail);

    /**
     * 新增快递单管理
     * 
     * @param frontFastmail 快递单管理
     * @return 结果
     */
    public int insertFrontFastmail(FrontFastmail frontFastmail);

    /**
     * 修改快递单管理
     * 
     * @param frontFastmail 快递单管理
     * @return 结果
     */
    public int updateFrontFastmail(FrontFastmail frontFastmail);

    /**
     * 删除快递单管理
     * 
     * @param id 快递单管理主键
     * @return 结果
     */
    public int deleteFrontFastmailById(Long id);

    /**
     * 批量删除快递单管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontFastmailByIds(Long[] ids);
}
