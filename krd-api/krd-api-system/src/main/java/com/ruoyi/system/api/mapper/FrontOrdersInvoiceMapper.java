package com.ruoyi.system.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.FrontOrdersInvoice;
import org.apache.ibatis.annotations.Mapper;

/**
 * 订单-发票信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Mapper
public interface FrontOrdersInvoiceMapper extends BaseMapper<FrontOrdersInvoice>
{
    /**
     * 查询订单-发票信息
     *
     * @param id 订单-发票信息主键
     * @return 订单-发票信息
     */
    public FrontOrdersInvoice selectFrontOrdersInvoiceById(Long id);

    /**
     * 查询订单-发票信息列表
     *
     * @param frontOrdersInvoice 订单-发票信息
     * @return 订单-发票信息集合
     */
    public List<FrontOrdersInvoice> selectFrontOrdersInvoiceList(FrontOrdersInvoice frontOrdersInvoice);

    /**
     * 新增订单-发票信息
     *
     * @param frontOrdersInvoice 订单-发票信息
     * @return 结果
     */
    public int insertFrontOrdersInvoice(FrontOrdersInvoice frontOrdersInvoice);

    /**
     * 修改订单-发票信息
     *
     * @param frontOrdersInvoice 订单-发票信息
     * @return 结果
     */
    public int updateFrontOrdersInvoice(FrontOrdersInvoice frontOrdersInvoice);

    /**
     * 删除订单-发票信息
     *
     * @param id 订单-发票信息主键
     * @return 结果
     */
    public int deleteFrontOrdersInvoiceById(Long id);

    /**
     * 批量删除订单-发票信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrontOrdersInvoiceByIds(Long[] ids);
}
