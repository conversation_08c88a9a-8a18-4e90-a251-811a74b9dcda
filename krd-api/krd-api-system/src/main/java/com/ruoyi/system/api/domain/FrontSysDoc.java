package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 文档管理对象 front_sys_doc
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Data
@Schema(description = "文档管理对象")
@TableName("front_sys_doc")
public class FrontSysDoc
{
    private static final long serialVersionUID = 1L;

    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**  */
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 类型*/
    @Excel(name = "类型")
    private String docType;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 0-未删除 1-删除 */
    @Excel(name = "0-未删除 1-删除")
    private Long isDel;

}
