package com.ruoyi.system.api.domain.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName FrontSource
 * @Tablename front_source
 * @Description 用户积分明细实体类
 * <AUTHOR>
 * @Date 2025/6/18 上午10:48
 */
@TableName("front_source")
@Data
@Schema(description = "用户积分明细对象")
public class FrontSource implements Serializable {

    @Schema(name = "用户积分明细id")
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @Schema(name = "用户id")
    private Long userId;

    @Schema(name = "积分来源 0-签到 1-答问卷 2-注册 3-邀请好友 4-分享 5-购物抵扣 6-购买")
    private Integer source;

    @Schema(name = "类型 0-奖励 1-扣除")
    private Integer type;

    @Schema(name = "每次变动积分")
    private Integer point;

    @Schema(name = "变动后余额")
    private BigDecimal balance;

    @Schema(name = "订单编号")
    private String orderNumber;

    @Schema(name = "创建时间")
    private LocalDateTime createTime;

    @Schema(name = "更新时间")
    private LocalDateTime updateTime;

    @Schema(name = "创建人")
    private String createBy;

    @Schema(name = "更新人")
    private String updateBy;
}
