package com.ruoyi.system.api.domain.user;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "统计信息")
public class StatisticsInfo {
    @Excel(name = "消费金额")
    private double consumptionAmount;
    // 可用积分
    @Excel(name = "可用积分")
    private Long available;
    // 礼品卡金额
    @Excel(name = "礼品卡金额")
    private String giftCardAmount;
    // 可用优惠券金额
    @Excel(name = "可用优惠券金额")
    private String availableCouponAmount;
    // 邀请好友次数
    @Excel(name = "邀请好友次数")
    private Long inviteFriendsCount;
    // 订单数量
    @Excel(name = "订单数量")
    private Long orderCount;
    // 售后退款数
    @Excel(name = "售后退款数")
    private Long afterSaleRefundCount;
    // 收藏商品数
    @Excel(name = "收藏商品数")
    private Long collectGoodsCount;
    // 商品评价
    @Excel(name = "商品评价")
    private Long goodsEvaluation;
    // 检测次数
    @Excel(name = "检测次数")
    private Long checkCount;
}
