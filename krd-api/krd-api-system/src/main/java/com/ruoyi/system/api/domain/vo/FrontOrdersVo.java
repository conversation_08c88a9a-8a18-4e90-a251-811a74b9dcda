package com.ruoyi.system.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.api.domain.FrontOrdersInvoice;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName FrontOrdersVo
 * @Description 订单相关参数
 * <AUTHOR>
 * @Date 2025/5/26 上午10:58
 */
public interface FrontOrdersVo {

    @Data
    @Schema(description = "小程序订单搜索参数")
    class UniFrontOrdersSearch {
        @Schema(name = "订单编号 / 商品名称")
        private String keyword;
        @Schema(name = "订单状态")
        private Integer status;
        @Schema(name = "用户id")
        private Integer uid;
    }

    @Data
    @Schema(description = "订单搜索参数")
    class FrontOrdersSearch {
        @Schema(name = "订单编号 / 用户ID / 商品名称")
        private String keyword;
        @Schema(name = "订单状态")
        private Integer status;
        @Schema(name = "订单类型")
        private Integer type;
    }

    @Data
    @Schema(name = "订单详情")
    class FrontOrdersDetail {
        @Schema(name = "订单基本详情")
        private FrontOrdersBaseInfo baseInfo;
        @Schema(name = "商品信息")
        private List<FrontOrdersGoodsInfo> goodsInfos;
        @Schema(name = "订单收货信息及备注")
        private FrontOrdersReceiveInfo receiveInfo;
        @Schema(name = "发票信息")
        private FrontOrdersInvoice invoiceInfo;
        @Schema(name = "订单付款信息")
        private FrontOrdersPayInfo payInfo;
    }

    @Data
    @Schema(description = "订单基本信息")
    class FrontOrdersBaseInfo {
        @Schema(name = "订单状态")
        private Integer status;
        @Schema(name = "订单创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;
        @Schema(name = "订单编号")
        private String orderNum;
        @Schema(name = "类型")
        private Integer type;
        @Schema(name = "用户账号 手机号 + 姓名")
        private String userName;
        @Schema(name = "发货单流水号")
        private String pushGoodsNo;
        @Schema(name = "物流公司")
        private String deliveryCompany;
        @Schema(name = "物流单号")
        private String deliveryNum;
    }

    @Data
    @Schema(description = "商品信息")
    class FrontOrdersGoodsInfo {
        @Schema(name = "商品id")
        private Long goodsId;
        @Schema(name = "规格id")
        private Long specId;
        @Schema(name = "商品图片")
        private String goodsImg;
        @Schema(name = "商品名称")
        private String goodsName;
        @Schema(name = "商品规格")
        private String goodsSpec;
        @Schema(name = "商品类型")
        private Integer goodsType;
        @Schema(name = "单位")
        private String unit;
        @Schema(name = "货号")
        private String goodsNo;
        @Schema(name = "商品价格")
        private BigDecimal goodsPrice;
        @Schema(name = "商品总价")
        private BigDecimal goodsAmount;
        @Schema(name = "商品数量")
        private Integer goodsNum;
        @Schema(name = "优惠抵扣")
        private BigDecimal discount;
        @Schema(name = "应付小计")
        private BigDecimal subtotal;
        @Schema(name = "实付金额")
        private BigDecimal payAmount;
        @Schema(name = "是否评价")
        private Boolean isComment;
        @Schema(name = "售后状态 0 正常 1 售后中 2已售后")
        private Integer isAfter;
    }

    @Data
    @Schema(description = "订单收货信息及备注")
    class FrontOrdersReceiveInfo {
        @Schema(name = "收货人")
        private String receiveName;
        @Schema(name = "收货人手机号")
        private String receivePhone;
        @Schema(name = "收货人地址")
        private String receiveAddress;
        @Schema(name = "收货人地址详情")
        private String receiveAddressDetail;
        @Schema(name = "用户备注")
        private String userRemark;
        @Schema(name = "平台备注")
        private String platformRemark;
    }

    @Data
    @Schema(description = "订单付款明细")
    class FrontOrdersPayInfo {
        @Schema(name = "商品合计")
        private BigDecimal goodsTotal;
        @Schema(name = "积分抵扣")
        private BigDecimal integral;
        @Schema(name = "礼品卡")
        private BigDecimal card;
        @Schema(name = "优惠券")
        private BigDecimal coupon;
        @Schema(name = "应付小计金额")
        private BigDecimal subtotal;
        @Schema(name = "实付金额")
        private BigDecimal payAmount;
    }

    @Data
    @Schema(description = "订单发货单信息")
    class FrontOrdersDeliveryInfo extends FrontOrdersReceiveInfo{
        @Schema(name = "订单编号")
        private String orderNum;
        @Schema(name = "下单用户")
        private String userName;
        @Schema(name = "商品信息")
        private List<FrontOrdersGoodsInfo> goodsInfos;
    }

    @Data
    @Schema(description = "修改订单收货信息")
    class FrontOrdersReceiveUpdateInfo {
        @Schema(name = "订单编号")
        private String orderNum;
        @Schema(name = "收货人")
        private String receiveName;
        @Schema(name = "收货人手机号")
        private String receivePhone;
        @Schema(name = "收货人地址 只是省市区")
        private String receiveAddress;
        @Schema(name = "收货人地址详情 省市区加详情地址")
        private String receiveAddressDetail;
    }

    @Data
    @Schema(description = "修改订单备注")
    class FrontOrdersRemarkUpdateInfo {
        @Schema(name = "订单编号")
        private String orderNum;
        @Schema(name = "用户备注")
        private String userRemark;
        @Schema(name = "平台备注")
        private String platformRemark;
    }

    @Data
    @Schema(description = "订单下单信息")
    class FrontOrdersAddInfo {
        @Schema(description = "地址id")
        private Long addressId;
        @Schema(description = "收货人")
        private String receiveName;
        @Schema(description = "收货人手机号")
        private String receivePhone;
        @Schema(description = "收货人地址")
        private String receiveAddress;
        @Schema(description = "收货人地址详情")
        private String receiveAddressDetail;
        @Schema(description = "商品信息")
        private List<FrontOrdersGoodsAddInfo> goodsInfos;
    }

    @Data
    @Schema(description = "订单下单商品信息")
    class FrontOrdersGoodsAddInfo {
        @Schema(description = "商品ID")
        private Long goodsId;
        @Schema(description = "商品规格id")
        private Long specId;
        @Schema(description = "商品数量")
        private Integer amount;
        @Schema(description = "商品类型0-检测套餐 1-商品")
        private Integer goodsType;
    }

    @Data
    @Schema(description = "下单后订单参数")
    class FrontOrdersAddResult {
        @Schema(name = "订单编号")
        private String orderNo;
        @Schema(name = "地址id")
        private Long addressId;
        @Schema(name = "收货人")
        private String receiveName;
        @Schema(name = "收货人手机号")
        private String receivePhone;
        @Schema(name = "收货人地址")
        private String receiveAddress;
        @Schema(name = "收货人地址详情")
        private String receiveAddressDetail;
        @Schema(name = "商品信息")
        private List<OrdersGoodsInfo> orderGoodsInfos;
        @Schema(name = "商品总价")
        private BigDecimal totalPrice;
        @Schema(name = "实际支付")
        private BigDecimal payPrice;
        @Schema(name = "订单状态")
        private Integer status;
        @Schema(name = "下单时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;
        @Schema(name = "支付方式")
        private String payType;
        @Schema(name = "支付时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime payTime;
        @Schema(name = "完成时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime finishTime;
        @Schema(name = "是否填写单号")
        private Boolean isWriteExpress = Boolean.FALSE;
    }

    @Data
    @Schema(description = "下单的商品详情")
    class OrdersGoodsInfo {
        @Schema(name = "商品ID")
        private Long goodsId;
        @Schema(name = "商品规格ID")
        private Long specId;
        @Schema(name = "商品名称")
        private String goodsName;
        @Schema(name = "商品图片")
        private String goodsImg;
        @Schema(name = "商品原价")
        private BigDecimal goodsPrice;
        @Schema(name = "商品折扣价")
        private BigDecimal goodsDiscountPrice;
        @Schema(name = "商品数量")
        private Integer goodsNum;
        @Schema(name = "商品规格名称")
        private String specName;
        @Schema(name = "是否评价")
        private Boolean isComment;
        @Schema(name = "售后状态 0 正常 1 售后中 2已售后")
        private Integer isAfter;
        @Schema(name = "退款类型")
        private Integer refundType;
    }

    @Data
    @Schema(description = "订单修改地址")
    class UpdateAddressInfo {
        @Schema(title = "订单编号")
        private String orderNo;
        @Schema(title = "地址id")
        private Long addressId;
        @Schema(title = "收货人")
        private String receiveName;
        @Schema(title = "收货人手机号")
        private String receivePhone;
        @Schema(title = "收货人地址")
        private String receiveAddress;
        @Schema(title = "收货人地址详情")
        private String receiveAddressDetail;
    }

    @Data
    @Schema(description = "订单评价")
    class CommentInfo {
        @Schema(title = "订单id")
        private String orderNo;
        @Schema(title = "商品id")
        private Long goodsId;
        @Schema(title = "商品规格id")
        private Long specId;
        @Schema(title = "评价星级")
        private Integer starRating;
        @Schema(title = "0-好评 1-中评 2-差评")
        private Integer type;
        @Schema(title = "评价内容")
        private String content;
        @Schema(title = "图片")
        private String pic;
    }

    @Data
    @Schema(description = "订单提交退款参数")
    class RefundInfo {
        @Schema(title = "订单编号")
        private String orderNo;
        @Schema(title = "申请原因")
        private String reason;
        @Schema(title = "订单退款商品详情")
        private List<RefundOrderGoodsInfo> refundOrderGoodsInfoList;
        @Schema(title = "问题描述")
        private String desc;
        @Schema(title = "退款凭证")
        private String pic;
        @Schema(title = "退款类型 1-仅退款 2-退货退款")
        private Integer type;
    }

    @Data
    @Schema(description = "订单退款商品详情")
    class RefundOrderGoodsInfo {
        @Schema(title = "商品id")
        private Long goodsId;
        @Schema(title = "商品规格id")
        private Long specId;
        @Schema(title = "退货数量")
        private Integer goodsNum;
    }

    @Data
    @Schema(description = "订单退款申请查询参数")
    class RefundSearch {
        @Schema(title = "商品id")
        private Long goodsId;
        @Schema(title = "商品规格id")
        private Long specId;
        @Schema(title = "售后状态")
        private Integer isAfter;
    }

    @Data
    @Schema(description = "订单退款申请查询list")
    class RefundList {
        @Schema(title = "订单编号")
        private String orderNo;
        @Schema(title = "订单退款申请查询参数")
        private List<RefundSearch> refundList;
    }


    @Data
    @Schema(description = "订单退款申请查询返回参数")
    class RefundReturnInfo {
        @Schema(title = "退款联系人")
        private String refundName;
        @Schema(title = "退款联系人手机号")
        private String refundPhone;
        @Schema(title = "退款金额")
        private BigDecimal refundPrice;
        @Schema(title = "退款商品信息")
        private List<RefundGoodsInfo> refundGoodsInfos;
    }

    @Data
    @Schema(description = "退款商品信息")
    class RefundGoodsInfo {
        @Schema(name = "商品ID")
        private Long goodsId;
        @Schema(name = "商品规格ID")
        private Long specId;
        @Schema(name = "商品名称")
        private String goodsName;
        @Schema(name = "商品图片")
        private String goodsImg;
        @Schema(name = "商品原价")
        private BigDecimal goodsPrice;
        @Schema(name = "商品折扣价")
        private BigDecimal goodsDiscountPrice;
        @Schema(name = "商品数量")
        private Integer goodsNum;
        @Schema(name = "商品规格名称")
        private String specName;
        @Schema(name = "商品总价")
        private BigDecimal goodsTotalPrice;
    }

    @Data
    @Schema(description = "订单退款详情")
    class RefundDetailInfo {
        @Schema(title = "订单编号")
        private String orderNo;
        @Schema(title = "退款金额")
        private BigDecimal refundPrice;
        @Schema(title = "退款原因")
        private String reason;
        @Schema(title = "申请时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime applyTime;
        @Schema(title = "退款商品信息")
        private List<RefundGoodsInfo> refundGoodsInfos;
        @Schema(title = "退款状态")
        private Integer refundStatus;
        @Schema(title = "退款申请凭证")
        private String pic;
    }

    @Data
    @Schema(description = "退货订单快递参数")
    class ReturnExpressInfo {
        @Schema(title = "订单编号")
        private String orderNo;
        @Schema(title = "快递公司")
        private String expressCompany;
        @Schema(title = "快递单号")
        private String expressNo;
    }

    @Data
    @Schema(description = "退款拆单查询参数")
    class RefundSplitInfo {
        @Schema(title = "订单编号")
        private String orderNo;
    }

    @Data
    @Schema(description = "发票信息")
    class InvoiceInfo {
        @Schema(title = "pdf")
        private String pdf;
        @Schema(title = "图片")
        private List<String> pic;
    }

}
